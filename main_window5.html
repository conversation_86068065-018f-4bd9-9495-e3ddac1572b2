<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عناوين الأوراق والملاحظات</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }        body {
            font-family: '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Arial', sans-serif;
            font-size: 13px;
            font-weight: bold;
            background-color: #B0E0E6;
            min-height: 100vh;
            padding: 15px;
            direction: rtl;
            scroll-behavior: smooth;
        }        .content-frame {
            background-color: white;
            border-radius: 8px;
            box-shadow: 5px 5px 15px rgba(0, 0, 0, 0.15);
            width: calc(100% - 30px);
            height: calc(100vh - 30px);
            margin: 0 auto;
            padding: 10px;
            overflow-y: auto;
            overflow-x: hidden;
            /* جعل شريط التمرير من الجهة اليمنى */
            direction: ltr;
        }

        /* إعادة ضبط النصوص للاتجاه العربي */
        .content-frame > * {
            direction: rtl;
        }

        /* تنسيق شريط التمرير */
        .content-frame::-webkit-scrollbar {
            width: 14px;
        }

        .content-frame::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 7px;
            margin: 10px 0;
        }

        .content-frame::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #1976d2, #1565c0);
            border-radius: 7px;
            border: 2px solid #f1f1f1;
        }

        .content-frame::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #1565c0, #0d47a1);
        }

        .content-frame::-webkit-scrollbar-thumb:active {
            background: linear-gradient(135deg, #0d47a1, #1976d2);
        }

        .header {
            background: linear-gradient(135deg, #1976d2, #1565c0);
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
            margin: -10px -10px 20px -10px;
        }

        .header h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }

        .back-button {
            position: absolute;
            top: 25px;
            right: 25px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            padding: 8px 15px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .back-button:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .table-container {
            margin-bottom: 20px;
        }

        .table-title {
            font-size: 14px;
            font-weight: bold;
            color: #0d47a1;
            text-align: center;
            margin-bottom: 10px;
        }

        table {
            width: 900px;
            margin: 0 auto;
            border-collapse: collapse;
            background-color: white;
            border-radius: 4px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        th {
            background-color: #1976d2;
            color: white;
            font-size: 14px;
            font-weight: bold;
            padding: 8px;
            text-align: center;
            border: 1px solid #1976d2;
            height: 40px;
        }

        td {
            padding: 4px 8px;
            border: 1px solid #ddd;
            font-size: 13px;
            font-weight: bold;
            color: black;
            height: 25px;
            text-align: center;
        }

        .editable {
            background-color: white;
            cursor: text;
        }

        .editable:hover {
            background-color: #f5f5f5;
        }

        .non-editable {
            background-color: #f8f9fa;
            cursor: not-allowed;
        }

        input[type="text"] {
            width: 100%;
            border: none;
            background: transparent;
            font-size: 13px;
            font-weight: bold;
            color: black;
            padding: 2px;
            text-align: center;
        }

        input[type="text"]:focus {
            outline: 2px solid #1976d2;
            background-color: #e3f2fd;
        }

        .tables-row {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }

        .table-section {
            flex: 1;
            max-width: 445px;
        }

        .table-section table {
            width: 100%;
        }

        .loading {
            text-align: center;
            color: #666;
            font-size: 16px;
            margin: 50px 0;
        }

        .error {
            color: #f44336;
            text-align: center;
            margin: 20px 0;
            padding: 10px;
            background-color: #ffebee;
            border-radius: 4px;
        }

        .success {
            color: #4caf50;
            text-align: center;
            margin: 20px 0;
            padding: 10px;
            background-color: #e8f5e8;
            border-radius: 4px;
        }

        /* تنسيق خاص للجدول الأول */
        .first-table {
            margin-bottom: 15px;
        }

        .first-table th:nth-child(1) { width: 150px; }
        .first-table th:nth-child(2) { width: 150px; }
        .first-table th:nth-child(3) { width: 580px; }        /* تنسيق خاص للجدولين السفليين */
        .violations-table th:nth-child(1) { width: 120px; }
        .violations-table th:nth-child(2) { width: 305px; }        .procedures-table th:nth-child(1) { width: 120px; }
        .procedures-table th:nth-child(2) { width: 305px; }
    </style>
</head>
<body>
    <div class="content-frame">
        <div class="header">
            <button class="back-button" onclick="goBack()">← العودة للرئيسية</button>
            <h1>عناوين الأوراق والملاحظات</h1>
        </div>

        <div id="loading" class="loading" style="display: none;">
            جاري تحميل البيانات...
        </div>

        <div id="error" class="error" style="display: none;"></div>
        <div id="success" class="success" style="display: none;"></div>        <!-- الجدول الأول: الأوراق العامة (ID 1-6) -->
        <div class="table-container first-table">
            <div class="table-title">الأوراق العامة</div>
            <table class="first-table">
                <thead>
                    <tr>
                        <th>الاسم</th>
                        <th>العنوان</th>
                        <th>ملاحظات</th>
                    </tr>
                </thead>
                <tbody id="general-table-body">
                    <!-- سيتم ملء البيانات ديناميكياً -->
                </tbody>
            </table>
        </div>        <!-- الجدولان السفليان -->
        <div class="tables-row">
            <!-- جدول المخالفات (ID 7-16) -->
            <div class="table-section">
                <div class="table-title">المخالفات</div>
                <table class="violations-table">
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>العنوان</th>
                        </tr>
                    </thead>
                    <tbody id="violations-table-body">
                        <!-- سيتم ملء البيانات ديناميكياً -->
                    </tbody>
                </table>
            </div>

            <!-- جدول الإجراءات (ID 17-20) -->
            <div class="table-section">
                <div class="table-title">الإجراءات</div>
                <table class="procedures-table">
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>العنوان</th>
                        </tr>
                    </thead>
                    <tbody id="procedures-table-body">
                        <!-- سيتم ملء البيانات ديناميكياً -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        const BASE_URL = 'http://localhost:5000';
        let isLoading = false;

        // العودة للنافذة الرئيسية
        function goBack() {
            window.location.href = 'main_window0.html';
        }

        // عرض رسالة
        function showMessage(message, type = 'info') {
            const errorDiv = document.getElementById('error');
            const successDiv = document.getElementById('success');
            
            errorDiv.style.display = 'none';
            successDiv.style.display = 'none';
            
            if (type === 'error') {
                errorDiv.textContent = message;
                errorDiv.style.display = 'block';
            } else if (type === 'success') {
                successDiv.textContent = message;
                successDiv.style.display = 'block';
                setTimeout(() => {
                    successDiv.style.display = 'none';
                }, 3000);
            }
        }

        // عرض/إخفاء مؤشر التحميل
        function showLoading(show) {
            const loadingDiv = document.getElementById('loading');
            loadingDiv.style.display = show ? 'block' : 'none';
            isLoading = show;
        }

        // تحميل البيانات من الخادم
        async function loadData() {
            showLoading(true);
            showMessage('');

            try {
                const response = await fetch(`${BASE_URL}/api/documents/all`);
                const result = await response.json();

                if (result.status === 'success') {
                    populateTable('general-table-body', result.data.general, ['الاسم', 'العنوان', 'ملاحظات']);
                    populateTable('violations-table-body', result.data.violations, ['الاسم', 'العنوان']);
                    populateTable('procedures-table-body', result.data.procedures, ['الاسم', 'العنوان']);
                } else {
                    showMessage(result.message || 'خطأ في تحميل البيانات', 'error');
                }
            } catch (error) {
                console.error('Error loading data:', error);
                showMessage('خطأ في الاتصال بالخادم', 'error');
            } finally {
                showLoading(false);
            }
        }

        // ملء جدول بالبيانات
        function populateTable(tableBodyId, data, columns) {
            const tbody = document.getElementById(tableBodyId);
            tbody.innerHTML = '';

            data.forEach(row => {
                const tr = document.createElement('tr');
                
                columns.forEach((column, index) => {
                    const td = document.createElement('td');
                    const isEditable = column !== 'الاسم';
                    
                    if (isEditable) {
                        td.className = 'editable';
                        const input = document.createElement('input');
                        input.type = 'text';
                        input.value = row[column] || '';
                        input.addEventListener('blur', () => updateData(row.ID, column, input.value));
                        input.addEventListener('keypress', (e) => {
                            if (e.key === 'Enter') {
                                input.blur();
                            }
                        });
                        td.appendChild(input);
                    } else {
                        td.className = 'non-editable';
                        td.textContent = row[column] || '';
                    }
                    
                    tr.appendChild(td);
                });
                
                tbody.appendChild(tr);
            });
        }

        // تحديث البيانات في الخادم
        async function updateData(id, column, value) {
            if (isLoading) return;

            try {
                const response = await fetch(`${BASE_URL}/api/documents/update`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        id: id,
                        column: column,
                        value: value
                    })
                });

                const result = await response.json();

                if (result.status === 'success') {
                    showMessage('تم حفظ التغيير بنجاح', 'success');
                } else {
                    showMessage(result.message || 'خطأ في حفظ التغيير', 'error');
                }
            } catch (error) {
                console.error('Error updating data:', error);
                showMessage('خطأ في الاتصال بالخادم', 'error');
            }
        }

        // فحص حالة الخادم
        async function checkServerStatus() {
            try {
                const response = await fetch(`${BASE_URL}/api/check-status`);
                const result = await response.json();
                return result.status === 'success';
            } catch (error) {
                return false;
            }
        }

        // تهيئة الصفحة
        async function initializePage() {
            // فحص حالة الخادم أولاً
            const serverOnline = await checkServerStatus();
            
            if (!serverOnline) {
                showMessage('الخادم غير متاح. يرجى التأكد من تشغيل الخادم الموحد.', 'error');
                return;
            }

            // تحميل البيانات
            await loadData();
        }        // تشغيل التطبيق عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', initializePage);
    </script>
</body>
</html>
