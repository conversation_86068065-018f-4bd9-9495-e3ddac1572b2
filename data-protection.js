/**
 * نظام تشفير وحماية البيانات
 */

class DataProtection {
    constructor() {
        this.encryptionKey = this.generateKey();
        this.init();
    }

    init() {
        console.log('🔐 تم تفعيل نظام حماية البيانات');
    }

    // توليد مفتاح تشفير
    generateKey() {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let key = '';
        for (let i = 0; i < 32; i++) {
            key += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return key;
    }

    // تشفير بسيط للبيانات الحساسة
    encrypt(text) {
        if (!text) return '';
        
        let encrypted = '';
        for (let i = 0; i < text.length; i++) {
            const charCode = text.charCodeAt(i);
            const keyChar = this.encryptionKey.charCodeAt(i % this.encryptionKey.length);
            encrypted += String.fromCharCode(charCode ^ keyChar);
        }
        return btoa(encrypted); // Base64 encoding
    }

    // فك التشفير
    decrypt(encryptedText) {
        if (!encryptedText) return '';
        
        try {
            const encrypted = atob(encryptedText); // Base64 decoding
            let decrypted = '';
            
            for (let i = 0; i < encrypted.length; i++) {
                const charCode = encrypted.charCodeAt(i);
                const keyChar = this.encryptionKey.charCodeAt(i % this.encryptionKey.length);
                decrypted += String.fromCharCode(charCode ^ keyChar);
            }
            return decrypted;
        } catch (e) {
            console.error('خطأ في فك التشفير:', e);
            return '';
        }
    }

    // حماية البيانات في التخزين المحلي
    secureStore(key, value) {
        const encrypted = this.encrypt(JSON.stringify(value));
        localStorage.setItem(key, encrypted);
    }

    secureRetrieve(key) {
        const encrypted = localStorage.getItem(key);
        if (!encrypted) return null;
        
        try {
            const decrypted = this.decrypt(encrypted);
            return JSON.parse(decrypted);
        } catch (e) {
            console.error('خطأ في استرجاع البيانات:', e);
            return null;
        }
    }

    // حماية كلمات المرور
    hashPassword(password) {
        // تشفير بسيط - في البيئة الحقيقية استخدم bcrypt أو مشابه
        let hash = 0;
        for (let i = 0; i < password.length; i++) {
            const char = password.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32bit integer
        }
        return Math.abs(hash).toString(16);
    }

    verifyPassword(password, hash) {
        return this.hashPassword(password) === hash;
    }

    // تنظيف البيانات من الذاكرة
    secureClear() {
        // مسح البيانات الحساسة
        if (typeof window.studentManagement !== 'undefined') {
            // مسح بيانات الطلاب من الذاكرة
            window.studentManagement.students = [];
            window.studentManagement.filteredStudents = [];
        }
        
        // مسح الحقول الحساسة
        const sensitiveFields = [
            'institution-phone',
            'registration-number',
            'institution-name'
        ];
        
        sensitiveFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field) {
                field.value = '';
            }
        });
    }

    // فحص سلامة البيانات
    validateDataIntegrity(data) {
        const requiredFields = ['id', 'name'];
        
        if (!data || typeof data !== 'object') {
            return false;
        }
        
        for (const field of requiredFields) {
            if (!data.hasOwnProperty(field)) {
                return false;
            }
        }
        
        return true;
    }

    // إنشاء نسخة احتياطية آمنة
    createSecureBackup() {
        const data = {
            timestamp: new Date().toISOString(),
            version: '1.0',
            data: window.studentManagement ? window.studentManagement.students : [],
            checksum: this.generateChecksum(JSON.stringify(window.studentManagement?.students || []))
        };

        const encrypted = this.encrypt(JSON.stringify(data));
        const blob = new Blob([encrypted], { type: 'application/octet-stream' });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `backup_${new Date().toISOString().split('T')[0]}.enc`;
        a.click();
        URL.revokeObjectURL(url);
        
        return '✅ تم إنشاء نسخة احتياطية مشفرة';
    }

    // استرجاع النسخة الاحتياطية
    restoreSecureBackup(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = (e) => {
                try {
                    const encrypted = e.target.result;
                    const decrypted = this.decrypt(encrypted);
                    const data = JSON.parse(decrypted);
                    
                    // التحقق من الـ checksum
                    const currentChecksum = this.generateChecksum(JSON.stringify(data.data));
                    if (currentChecksum !== data.checksum) {
                        reject('❌ البيانات تالفة أو تم التلاعب بها');
                        return;
                    }
                    
                    // التحقق من صحة البيانات
                    if (!Array.isArray(data.data)) {
                        reject('❌ تنسيق البيانات غير صحيح');
                        return;
                    }
                    
                    resolve(data);
                } catch (error) {
                    reject('❌ خطأ في فك تشفير النسخة الاحتياطية');
                }
            };
            
            reader.onerror = () => {
                reject('❌ خطأ في قراءة الملف');
            };
            
            reader.readAsText(file);
        });
    }

    // توليد checksum للتحقق من سلامة البيانات
    generateChecksum(data) {
        let hash = 0;
        if (data.length === 0) return hash.toString();
        
        for (let i = 0; i < data.length; i++) {
            const char = data.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return Math.abs(hash).toString(16);
    }

    // حماية من SQL Injection (للبيانات المرسلة للخادم)
    sanitizeInput(input) {
        if (typeof input !== 'string') return input;
        
        return input
            .replace(/'/g, "''")  // escape single quotes
            .replace(/"/g, '""')  // escape double quotes
            .replace(/\\/g, '\\\\') // escape backslashes
            .replace(/\0/g, '\\0') // escape null bytes
            .trim();
    }

    // تنظيف HTML من البيانات المدخلة
    sanitizeHTML(input) {
        const div = document.createElement('div');
        div.textContent = input;
        return div.innerHTML;
    }

    // فحص البيانات المرسلة
    validateRequest(data) {
        const maxLength = 1000;
        const allowedFields = [
            'name', 'phone', 'city', 'manager', 
            'academicYear', 'registrationNumber'
        ];

        if (!data || typeof data !== 'object') {
            return { valid: false, error: 'بيانات غير صحيحة' };
        }

        for (const [key, value] of Object.entries(data)) {
            if (!allowedFields.includes(key)) {
                return { valid: false, error: `حقل غير مسموح: ${key}` };
            }

            if (typeof value === 'string' && value.length > maxLength) {
                return { valid: false, error: `${key} طويل جداً` };
            }
        }

        return { valid: true };
    }
}

// تهيئة نظام حماية البيانات
window.dataProtection = new DataProtection();

// حماية من إعادة تعريف الدوال المهمة
Object.freeze(DataProtection.prototype);
Object.freeze(window.dataProtection);
