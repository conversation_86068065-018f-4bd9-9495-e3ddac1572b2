#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت لتحليل ملفات الرمز السري بالتفصيل
"""

import pandas as pd
import os

def analyze_secret_file(file_path):
    """تحليل ملف الرمز السري"""
    print(f"🔍 تحليل الملف: {file_path}")
    print("=" * 60)
    
    try:
        if not os.path.exists(file_path):
            print("❌ الملف غير موجود")
            return
        
        # قراءة الملف
        df = pd.read_excel(file_path)
        print(f"📊 عدد الأعمدة: {len(df.columns)}")
        print(f"📊 عدد الصفوف: {len(df)}")
        print(f"📊 أسماء الأعمدة: {list(df.columns)}")
        print()
        
        # البحث عن الصفوف المهمة
        print("🔍 البحث عن البيانات المهمة:")
        for index, row in df.iterrows():
            for col in df.columns:
                cell_value = str(row[col]).strip() if pd.notna(row[col]) else ""
                if any(keyword in cell_value for keyword in ["رقم التلميذ", "رقم الطالب", "الرمز"]):
                    print(f"   الصف {index + 1}, العمود {col}: '{cell_value}'")
                elif len(cell_value) > 5 and cell_value[0].isalpha() and any(c.isdigit() for c in cell_value[1:]):
                    print(f"   الصف {index + 1}, العمود {col}: رمز محتمل '{cell_value}'")
                    # فحص الأعمدة المجاورة للرمز السري
                    col_index = list(df.columns).index(col)
                    for j in range(max(0, col_index-1), min(len(df.columns), col_index+3)):
                        adjacent_col = df.columns[j]
                        adjacent_value = str(row[adjacent_col]).strip() if pd.notna(row[adjacent_col]) else ""
                        if adjacent_value and adjacent_value != "nan" and adjacent_value != cell_value:
                            print(f"     العمود المجاور {adjacent_col}: '{adjacent_value}'")
                    break
        
        print()
        print("📋 عينة من البيانات (أول 10 صفوف):")
        print("-" * 60)
        for i in range(min(10, len(df))):
            print(f"الصف {i+1}:")
            for col in df.columns:
                value = str(df.iloc[i][col]).strip() if pd.notna(df.iloc[i][col]) else "فارغ"
                if value != "فارغ" and value != "nan":
                    print(f"  {col}: '{value}'")
            print()
        
        # محاولة استخراج البيانات
        print("🎯 محاولة استخراج أزواج الرمز والرمز السري:")
        print("-" * 60)
        
        # البحث عن عمود الرمز
        code_column = None
        start_row = 0
          for index, row in df.iterrows():
            for col in df.columns:
                cell_value = str(row[col]).strip() if pd.notna(row[col]) else ""
                if len(cell_value) > 5 and cell_value[0].isalpha() and any(c.isdigit() for c in cell_value[1:]):
                    code_column = col
                    start_row = index
                    print(f"✅ تم العثور على عمود الرمز: {col} في الصف {index + 1}")
                    break
            if code_column:
                break
        
        if code_column:
            # استخدام العمود الثالث (DD) للرمز السري
            if len(df.columns) >= 3:
                secret_column = df.columns[2]  # العمود الثالث (DD)
                print(f"✅ سيتم استخدام العمود الثالث للرمز السري: {secret_column}")
            else:
                # البحث عن عمود الرمز السري كما كان من قبل
                col_index = list(df.columns).index(code_column)
                secret_column = None
                
                # جرب الأعمدة المجاورة
                for offset in [1, 2, -1, 3, 4, 5]:
                    test_col_index = col_index + offset
                    if 0 <= test_col_index < len(df.columns):
                        test_col = df.columns[test_col_index]
                        test_value = str(df.iloc[start_row][test_col]).strip() if pd.notna(df.iloc[start_row][test_col]) else ""
                        if test_value and test_value != "nan" and len(test_value) > 1:
                            secret_column = test_col
                            print(f"✅ عمود الرمز السري المحتمل: {test_col}")
                            break
            
            if secret_column:
                print(f"\n📝 استخراج البيانات من العمودين {code_column} و {secret_column}:")
                extracted_count = 0
                for i in range(start_row, len(df)):
                    code = str(df.iloc[i][code_column]).strip() if pd.notna(df.iloc[i][code_column]) else ""
                    secret = str(df.iloc[i][secret_column]).strip() if pd.notna(df.iloc[i][secret_column]) else ""
                    
                    code = code.replace("nan", "").strip()
                    secret = secret.replace("nan", "").strip()
                    
                    if (code and len(code) > 3 and 
                        secret and len(secret) > 1 and 
                        code != secret):
                        print(f"  {extracted_count + 1}. الرمز: '{code}' -> الرمز السري: '{secret}'")
                        extracted_count += 1
                        if extracted_count >= 5:  # عرض أول 5 فقط
                            break
                
                print(f"\n🎉 تم استخراج {extracted_count} زوج من البيانات الصالحة")
            else:
                print("❌ لم يتم العثور على عمود الرمز السري")
        else:
            print("❌ لم يتم العثور على عمود الرمز")
            
    except Exception as e:
        print(f"❌ خطأ في التحليل: {str(e)}")

if __name__ == "__main__":
    # تحليل الملف الأول
    file_path = r"اللوائح\الرمز الزري\export_InfoEleve_22032025110700.xlsx"
    analyze_secret_file(file_path)
