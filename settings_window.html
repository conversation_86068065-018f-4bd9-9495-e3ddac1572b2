<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات البرنامج</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Calibri', sans-serif;
            background-color: #f5f5f5;
            color: #333;
            direction: rtl;
            text-align: right;
        }

        .container {
            max-width: 900px;
            margin: 20px auto;
            padding: 20px;
        }

        /* تصميم العنوان */
        .title-frame {
            background: linear-gradient(135deg, #0D47A1, #1565C0);
            border-radius: 12px;
            min-height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(13, 71, 161, 0.3);
        }

        .title-label {
            font-size: 18px;
            font-weight: bold;
            color: white;
            text-align: center;
        }

        /* إطار الأزرار */
        .buttons-frame {
            background-color: white;
            border-radius: 10px;
            border: 1px solid #e0e0e0;
            padding: 30px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        /* تصميم الأزرار */
        .settings-button {
            width: 100%;
            padding: 15px;
            margin-bottom: 15px;
            border: none;
            border-radius: 10px;
            font-family: 'Calibri', sans-serif;
            font-size: 16px;
            font-weight: bold;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: right;
            min-height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .settings-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
        }

        .settings-button:active {
            transform: translateY(0);
        }

        /* ألوان الأزرار */
        .delete-btn {
            background-color: #e74c3c;
        }

        .delete-btn:hover {
            background-color: #c0392b;
        }

        .new-season-btn {
            background-color: #8e44ad;
        }

        .new-season-btn:hover {
            background-color: #7d3c98;
        }

        .backup-btn {
            background-color: #f39c12;
        }

        .backup-btn:hover {
            background-color: #e67e22;
        }

        .restore-btn {
            background-color: #9b59b6;
        }

        .restore-btn:hover {
            background-color: #8e44ad;
        }

        .import-btn {
            background-color: #27ae60;
        }

        .import-btn:hover {
            background-color: #229954;
        }

        .printer-btn {
            background-color: #3498db;
        }

        .printer-btn:hover {
            background-color: #2980b9;
        }

        /* نمط النوافذ المنبثقة */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: white;
            margin: 10% auto;
            padding: 30px;
            border-radius: 15px;
            width: 80%;
            max-width: 500px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .modal-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
        }

        .modal-message {
            font-size: 14px;
            line-height: 1.6;
            margin-bottom: 25px;
            color: #555;
        }

        .modal-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            margin-bottom: 20px;
            font-family: 'Calibri', sans-serif;
        }

        .modal-input:focus {
            outline: none;
            border-color: #3498db;
        }

        .modal-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .modal-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Calibri', sans-serif;
        }

        .modal-btn-primary {
            background-color: #3498db;
            color: white;
        }

        .modal-btn-primary:hover {
            background-color: #2980b9;
        }

        .modal-btn-secondary {
            background-color: #95a5a6;
            color: white;
        }

        .modal-btn-secondary:hover {
            background-color: #7f8c8d;
        }

        .modal-btn-danger {
            background-color: #e74c3c;
            color: white;
        }

        .modal-btn-danger:hover {
            background-color: #c0392b;
        }

        /* شريط التقدم */
        .progress-modal {
            display: none;
            position: fixed;
            z-index: 1001;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
        }

        .progress-content {
            background-color: white;
            margin: 20% auto;
            padding: 30px;
            border-radius: 15px;
            width: 80%;
            max-width: 400px;
            text-align: center;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #ecf0f1;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background-color: #3498db;
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-text {
            font-size: 14px;
            color: #333;
            margin-bottom: 10px;
        }

        /* تصميم اختيار الملفات */
        .file-input {
            display: none;
        }

        .file-select-btn {
            padding: 12px 20px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-family: 'Calibri', sans-serif;
            font-size: 14px;
            font-weight: bold;
            margin: 10px 5px;
        }

        .file-select-btn:hover {
            background-color: #2980b9;
        }

        /* رسائل الحالة */
        .status-message {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
            text-align: center;
        }

        .status-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status-warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status-info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        /* تحسينات متجاوبة */
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 15px;
            }

            .modal-content {
                width: 95%;
                margin: 5% auto;
                padding: 20px;
            }

            .settings-button {
                font-size: 14px;
                padding: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- العنوان -->
        <div class="title-frame">
            <div class="title-label">⚙️ إعدادات البرنامج</div>
        </div>

        <!-- إطار الأزرار -->
        <div class="buttons-frame">
            <button class="settings-button delete-btn" onclick="deleteAllData()">
                🗑️ حذف جميع البيانات
            </button>

            <button class="settings-button new-season-btn" onclick="prepareNewSeason()">
                🔄 تهيئة البرنامج لبداية موسم جديد
            </button>

            <button class="settings-button backup-btn" onclick="backupDatabase()">
                💾 نسخ احتياطي للبيانات
            </button>

            <button class="settings-button restore-btn" onclick="restoreBackup()">
                📂 استيراد نسخة احتياطية
            </button>

            <button class="settings-button import-btn" onclick="importExcelData()">
                📊 استيراد البيانات من ملف إكسل
            </button>

            <button class="settings-button printer-btn" onclick="openPrinterSettings()">
                🖨️ إعدادات الطابعة
            </button>
        </div>
    </div>

    <!-- نافذة منبثقة لكلمة المرور -->
    <div id="passwordModal" class="modal">
        <div class="modal-content">
            <div class="modal-title" id="passwordModalTitle">التحقق من الهوية</div>
            <div class="modal-message" id="passwordModalMessage">الرجاء إدخال رمز الحذف للمتابعة:</div>
            <input type="password" id="passwordInput" class="modal-input" placeholder="أدخل كلمة المرور">
            <div class="modal-buttons">
                <button class="modal-btn modal-btn-primary" onclick="confirmPassword()">موافق</button>
                <button class="modal-btn modal-btn-secondary" onclick="closePasswordModal()">إلغاء</button>
            </div>
        </div>
    </div>

    <!-- نافذة منبثقة للتأكيد -->
    <div id="confirmModal" class="modal">
        <div class="modal-content">
            <div class="modal-title" id="confirmModalTitle">تأكيد العملية</div>
            <div class="modal-message" id="confirmModalMessage">هل أنت متأكد من هذه العملية؟</div>
            <div class="modal-buttons">
                <button class="modal-btn modal-btn-danger" id="confirmYesBtn" onclick="confirmAction()">نعم</button>
                <button class="modal-btn modal-btn-secondary" onclick="closeConfirmModal()">لا</button>
            </div>
        </div>
    </div>

    <!-- نافذة منبثقة لشريط التقدم -->
    <div id="progressModal" class="progress-modal">
        <div class="progress-content">
            <div class="progress-text" id="progressText">جاري المعالجة...</div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div id="progressPercent">0%</div>
        </div>
    </div>

    <!-- نافذة منبثقة للرسائل -->
    <div id="messageModal" class="modal">
        <div class="modal-content">
            <div class="modal-title" id="messageModalTitle">رسالة</div>
            <div class="modal-message" id="messageModalMessage">رسالة النظام</div>
            <div class="modal-buttons">
                <button class="modal-btn modal-btn-primary" onclick="closeMessageModal()">موافق</button>
            </div>
        </div>
    </div>

    <!-- مدخل الملفات المخفي -->
    <input type="file" id="fileInput" class="file-input" accept=".zip,.sqlite,.db,.xlsx,.xls">

    <script>
        // متغيرات عامة
        let currentAction = null;
        let dbPath = 'data.db'; // مسار قاعدة البيانات

        // قائمة الجداول المراد حذفها في عملية الحذف الكامل
        const tablesToClear = [
            'الحساب_الرئيسي',
            'المسحوبات',
            'registration_fees',
            'احصائيات_الغياب_الشهرية',
            'احصائيات_الغياب_السنوية',
            'الاساتذة',
            'المصاريف',
            'الموازنة_السنوية',
            'الواجبات_الشهرية',
            'تدوين_الغياب',
            'جدول_الاداءات',
            'جدول_البيانات',
            'جدول_المواد_والاقسام',
            'tasks_appointments',
            'واجبات_التسجيل',
            'monthly_duties'
        ];

        // وظائف الأزرار الرئيسية
        function deleteAllData() {
            currentAction = 'delete';
            showPasswordModal('التحقق من الهوية', 'الرجاء إدخال رمز الحذف للمتابعة:');
        }

        function prepareNewSeason() {
            currentAction = 'newSeason';
            showPasswordModal('🔄 تهيئة موسم جديد', 'الرجاء إدخال رمز التحقق (12345) للمتابعة:');
        }

        function backupDatabase() {
            showProgressModal('جاري إنشاء النسخة الاحتياطية...');
            performBackup();
        }

        function restoreBackup() {
            document.getElementById('fileInput').accept = '.zip,.sqlite,.db';
            document.getElementById('fileInput').onchange = handleRestoreFile;
            document.getElementById('fileInput').click();
        }

        function importExcelData() {
            document.getElementById('fileInput').accept = '.xlsx,.xls';
            document.getElementById('fileInput').onchange = handleExcelFile;
            document.getElementById('fileInput').click();
        }

        function openPrinterSettings() {
            showMessage('إعدادات الطابعة', 'سيتم فتح نافذة إعدادات الطابعة...', 'info');
            // هنا يمكن إضافة كود لفتح نافذة إعدادات الطابعة
        }

        // وظائف النوافذ المنبثقة
        function showPasswordModal(title, message) {
            document.getElementById('passwordModalTitle').textContent = title;
            document.getElementById('passwordModalMessage').textContent = message;
            document.getElementById('passwordInput').value = '';
            document.getElementById('passwordModal').style.display = 'block';
        }

        function closePasswordModal() {
            document.getElementById('passwordModal').style.display = 'none';
            currentAction = null;
        }

        function confirmPassword() {
            const password = document.getElementById('passwordInput').value;
            
            if (currentAction === 'delete' && password === '12345') {
                closePasswordModal();
                performDeletion();
            } else if (currentAction === 'newSeason' && password === '12345') {
                closePasswordModal();
                performNewSeasonPreparation();
            } else {
                showMessage('خطأ', 'رمز التحقق غير صحيح!', 'error');
            }
        }

        function showConfirmModal(title, message, action) {
            document.getElementById('confirmModalTitle').textContent = title;
            document.getElementById('confirmModalMessage').textContent = message;
            document.getElementById('confirmModal').style.display = 'block';
            currentAction = action;
        }

        function closeConfirmModal() {
            document.getElementById('confirmModal').style.display = 'none';
            currentAction = null;
        }

        function confirmAction() {
            closeConfirmModal();
            if (currentAction === 'restore') {
                performRestore();
            } else if (currentAction === 'importExcel') {
                performExcelImport();
            }
        }

        function showProgressModal(text) {
            document.getElementById('progressText').textContent = text;
            document.getElementById('progressFill').style.width = '0%';
            document.getElementById('progressPercent').textContent = '0%';
            document.getElementById('progressModal').style.display = 'block';
        }

        function updateProgress(percent, text) {
            document.getElementById('progressFill').style.width = percent + '%';
            document.getElementById('progressPercent').textContent = percent + '%';
            if (text) {
                document.getElementById('progressText').textContent = text;
            }
        }

        function closeProgressModal() {
            document.getElementById('progressModal').style.display = 'none';
        }

        function showMessage(title, message, type = 'info') {
            document.getElementById('messageModalTitle').textContent = title;
            document.getElementById('messageModalMessage').innerHTML = message;
            document.getElementById('messageModal').style.display = 'block';
        }

        function closeMessageModal() {
            document.getElementById('messageModal').style.display = 'none';
        }

        // وظائف العمليات الرئيسية
        async function performDeletion() {
            showProgressModal('جاري حذف البيانات...');
            
            try {
                updateProgress(10, 'جاري الاتصال بقاعدة البيانات...');
                
                // محاكاة عملية الحذف
                const response = await fetch('/api/delete-all-data', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        tables: tablesToClear
                    })
                });

                updateProgress(50, 'جاري حذف البيانات...');

                if (response.ok) {
                    const result = await response.json();
                    updateProgress(80, 'جاري ضغط قاعدة البيانات...');
                    
                    // محاكاة ضغط قاعدة البيانات
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    
                    updateProgress(100, 'تم الحذف بنجاح!');
                    
                    setTimeout(() => {
                        closeProgressModal();
                        showMessage(
                            '✅ تم الحذف بنجاح', 
                            `🗑️ تم حذف جميع البيانات بنجاح!<br><br>
                            📊 تم مسح البيانات من ${result.deletedTables || tablesToClear.length} قسم في قاعدة البيانات<br>
                            🔒 تم الاحتفاظ بإعدادات المؤسسة والسنة الدراسية<br>
                            💾 تم ضغط قاعدة البيانات لتوفير المساحة<br><br>
                            🔄 سيتم إغلاق البرنامج الآن لإتمام العملية<br>
                            💡 يمكنك إعادة تشغيل البرنامج بعد ذلك`,
                            'success'
                        );
                    }, 1000);
                } else {
                    throw new Error('فشل في حذف البيانات');
                }
            } catch (error) {
                closeProgressModal();
                showMessage('خطأ', `حدث خطأ أثناء حذف البيانات: ${error.message}`, 'error');
            }
        }

        async function performNewSeasonPreparation() {
            showProgressModal('جاري تهيئة الموسم الجديد...');
            
            try {
                updateProgress(10, 'جاري الاتصال بقاعدة البيانات...');
                
                const response = await fetch('/api/prepare-new-season', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        tables: tablesToClear
                    })
                });

                updateProgress(50, 'جاري حذف بيانات الموسم السابق...');

                if (response.ok) {
                    const result = await response.json();
                    updateProgress(80, 'جاري ضغط قاعدة البيانات...');
                    
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    
                    updateProgress(100, 'تم تهيئة الموسم الجديد!');
                    
                    setTimeout(() => {
                        closeProgressModal();
                        showMessage(
                            '🎉 تم تهيئة الموسم الجديد',
                            `🔄 تمت تهيئة البرنامج لبداية موسم جديد بنجاح!<br><br>
                            🗑️ تم مسح البيانات من ${result.deletedTables || tablesToClear.length} قسم في النظام<br>
                            📚 تم الاحتفاظ بجدول بيانات الطلاب وإعدادات المؤسسة<br>
                            💾 تم ضغط قاعدة البيانات لتحسين الأداء<br><br>
                            ✅ البرنامج جاهز الآن لاستقبال بيانات الموسم الجديد<br>
                            💡 يمكنك البدء في إدخال بيانات الطلاب والأساتذة الجدد`,
                            'success'
                        );
                    }, 1000);
                } else {
                    throw new Error('فشل في تهيئة الموسم الجديد');
                }
            } catch (error) {
                closeProgressModal();
                showMessage('خطأ', `❌ خطأ أثناء تهيئة الموسم الجديد: ${error.message}`, 'error');
            }
        }

        async function performBackup() {
            try {
                updateProgress(10, 'جاري إنشاء النسخة الاحتياطية...');
                
                const response = await fetch('/api/backup-database', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                updateProgress(50, 'جاري ضغط النسخة الاحتياطية...');

                if (response.ok) {
                    const blob = await response.blob();
                    updateProgress(80, 'جاري تحضير الملف للتحميل...');
                    
                    // إنشاء رابط تحميل
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.style.display = 'none';
                    a.href = url;
                    
                    const now = new Date();
                    const timestamp = now.toISOString().replace(/[:.]/g, '-').slice(0, 19);
                    a.download = `database_backup_${timestamp}.zip`;
                    
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                    
                    updateProgress(100, 'تم إنشاء النسخة الاحتياطية!');
                    
                    setTimeout(() => {
                        closeProgressModal();
                        showMessage(
                            '💾 تم إنشاء النسخة الاحتياطية بنجاح!',
                            `📁 اسم الملف: database_backup_${timestamp}.zip<br>
                            📂 تم تحميل الملف إلى مجلد التحميلات<br>
                            🕒 التاريخ والوقت: ${now.toLocaleString('ar-EG')}<br><br>
                            ✅ تم ضغط قاعدة البيانات وتحسين الأداء<br>
                            🔒 تم حفظ النسخة الاحتياطية بأمان`,
                            'success'
                        );
                    }, 1000);
                } else {
                    throw new Error('فشل في إنشاء النسخة الاحتياطية');
                }
            } catch (error) {
                closeProgressModal();
                showMessage('خطأ', `حدث خطأ أثناء عمل نسخة احتياطية: ${error.message}`, 'error');
            }
        }

        function handleRestoreFile(event) {
            const file = event.target.files[0];
            if (!file) return;

            const fileName = file.name;
            const isZip = fileName.toLowerCase().endsWith('.zip');
            const isSqlite = fileName.toLowerCase().endsWith('.sqlite') || fileName.toLowerCase().endsWith('.db');

            if (!isZip && !isSqlite) {
                showMessage('خطأ', 'الملف المختار ليس ملف نسخة احتياطية معتمد. يرجى اختيار ملف بامتداد ZIP أو SQLite.', 'error');
                return;
            }

            showConfirmModal(
                'تأكيد استعادة النسخة الاحتياطية',
                `هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟<br><br>
                📁 اسم الملف: ${fileName}<br><br>
                ⚠️ ستتم استبدال جميع البيانات الحالية بالبيانات من النسخة الاحتياطية.`,
                'restore'
            );
        }

        async function performRestore() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (!file) return;

            showProgressModal('جاري استعادة النسخة الاحتياطية...');
            
            try {
                updateProgress(10, 'جاري فحص الملف...');
                
                const formData = new FormData();
                formData.append('backup_file', file);
                
                const response = await fetch('/api/restore-backup', {
                    method: 'POST',
                    body: formData
                });

                updateProgress(50, 'جاري استبدال قاعدة البيانات...');

                if (response.ok) {
                    const result = await response.json();
                    updateProgress(90, 'جاري التحقق من النسخة المستعادة...');
                    
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    
                    updateProgress(100, 'تم استعادة النسخة الاحتياطية!');
                    
                    setTimeout(() => {
                        closeProgressModal();
                        showMessage(
                            '📂 تمت استعادة النسخة الاحتياطية بنجاح!',
                            `📁 اسم الملف المستعاد: ${file.name}<br>
                            💾 تم حفظ نسخة من قاعدة البيانات السابقة<br><br>
                            ✅ تم استبدال قاعدة البيانات بنجاح<br>
                            🔄 يرجى إعادة تشغيل البرنامج لتطبيق التغييرات`,
                            'success'
                        );
                    }, 1000);
                } else {
                    const error = await response.json();
                    throw new Error(error.message || 'فشل في استعادة النسخة الاحتياطية');
                }
            } catch (error) {
                closeProgressModal();
                showMessage('خطأ', `خطأ في استعادة النسخة الاحتياطية: ${error.message}`, 'error');
            }
        }

        function handleExcelFile(event) {
            const file = event.target.files[0];
            if (!file) return;

            const fileName = file.name;
            const isExcel = fileName.toLowerCase().endsWith('.xlsx') || fileName.toLowerCase().endsWith('.xls');

            if (!isExcel) {
                showMessage('خطأ', 'يرجى اختيار ملف Excel صالح (.xlsx أو .xls)', 'error');
                return;
            }

            showConfirmModal(
                'تأكيد استيراد البيانات',
                `هل أنت متأكد من استيراد البيانات من ملف Excel؟<br><br>
                📁 اسم الملف: ${fileName}<br><br>
                ستتم إضافة البيانات إلى جدول_البيانات مع إنشاء رموز تلاميذ تلقائية.<br><br>
                الأعمدة المطلوبة: القسم، اسم_المجموعة، اسم_التلميذ، النوع`,
                'importExcel'
            );
        }

        async function performExcelImport() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (!file) return;

            showProgressModal('جاري استيراد البيانات من ملف Excel...');
            
            try {
                updateProgress(20, 'جاري قراءة ملف Excel...');
                
                const formData = new FormData();
                formData.append('excel_file', file);
                
                const response = await fetch('/api/import-excel', {
                    method: 'POST',
                    body: formData
                });

                updateProgress(70, 'جاري معالجة البيانات...');

                if (response.ok) {
                    const result = await response.json();
                    updateProgress(100, 'تم استيراد البيانات بنجاح!');
                    
                    setTimeout(() => {
                        closeProgressModal();
                        showMessage(
                            '📊 تم استيراد البيانات من Excel بنجاح!',
                            `✅ عدد السجلات المدرجة: ${result.insertedCount || 0}<br>
                            ⏭️ عدد السجلات المتجاهلة: ${result.skippedCount || 0}<br>
                            📁 ملف المصدر: ${file.name}<br><br>
                            💾 تم حفظ جميع البيانات في قاعدة البيانات<br>
                            🔢 تم إنشاء رموز التلاميذ تلقائياً`,
                            'success'
                        );
                    }, 1000);
                } else {
                    const error = await response.json();
                    throw new Error(error.message || 'فشل في استيراد البيانات');
                }
            } catch (error) {
                closeProgressModal();
                showMessage('خطأ', `حدث خطأ أثناء استيراد البيانات: ${error.message}`, 'error');
            }
        }

        // معالجة الأحداث
        document.addEventListener('DOMContentLoaded', function() {
            // إغلاق النوافذ المنبثقة عند الضغط خارجها
            window.onclick = function(event) {
                const modals = document.querySelectorAll('.modal, .progress-modal');
                modals.forEach(modal => {
                    if (event.target === modal) {
                        modal.style.display = 'none';
                    }
                });
            };

            // معالجة ضغط مفتاح Enter في حقل كلمة المرور
            document.getElementById('passwordInput').addEventListener('keypress', function(event) {
                if (event.key === 'Enter') {
                    confirmPassword();
                }
            });
        });
    </script>
</body>
</html>
