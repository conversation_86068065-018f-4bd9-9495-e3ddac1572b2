// قائمة الأساتذة بأسماء وهمية
const teachers = [
    'أحمد محمد', 'فاطمة علي', 'محمد حسن', 'عائشة أحمد', 'علي محمود',
    'زين<PERSON> خالد', 'حسام الدين', 'نور الهدى', 'عبد الله سعد', 'مريم يوسف',
    'خالد عبد الرحمن', 'سارة محمد', 'يوسف إبراهيم', 'هدى عبد الله', 'عمر حسين',
    'ليلى أحمد', 'إبراهيم علي', 'آمنة محمد', 'سعد عبد العزيز', 'رقية حسن'
];

// متغيرات عامة
let teacherAssignments = {}; // تتبع توزيع الأساتذة
let draggedElement = null;

// تهيئة البرنامج عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    createTeacherCards();
    setupDragAndDrop();
    initializeAssignments();
});

// إنشاء بطاقات الأساتذة
function createTeacherCards() {
    const teachersPool = document.getElementById('teachersPool');
    
    teachers.forEach((teacherName, index) => {
        // إنشاء 3 بطاقات لكل أستاذ
        for (let cardNum = 1; cardNum <= 3; cardNum++) {
            const card = document.createElement('div');
            card.className = 'teacher-card';
            card.draggable = true;
            card.dataset.teacher = teacherName;
            card.dataset.cardId = `${index}-${cardNum}`;
            
            card.innerHTML = `
                ${teacherName}
                <div class="card-number">${cardNum}</div>
            `;
            
            teachersPool.appendChild(card);
        }
    });
}

// تهيئة متتبع التوزيعات
function initializeAssignments() {
    teachers.forEach(teacher => {
        teacherAssignments[teacher] = {
            day1: false,
            day2: false,
            day3: false,
            usedCards: 0
        };
    });
}

// إعداد وظائف السحب والإفلات
function setupDragAndDrop() {
    // إعداد السحب للبطاقات
    document.addEventListener('dragstart', function(e) {
        if (e.target.classList.contains('teacher-card')) {
            draggedElement = e.target;
            e.target.classList.add('dragging');
        }
    });

    document.addEventListener('dragend', function(e) {
        if (e.target.classList.contains('teacher-card')) {
            e.target.classList.remove('dragging');
            draggedElement = null;
        }
    });

    // إعداد الإفلات للمقاعد
    const seats = document.querySelectorAll('.seat');
    seats.forEach(seat => {
        seat.addEventListener('dragover', function(e) {
            e.preventDefault();
            if (!seat.classList.contains('occupied')) {
                seat.classList.add('drag-over');
            }
        });

        seat.addEventListener('dragleave', function(e) {
            seat.classList.remove('drag-over');
        });

        seat.addEventListener('drop', function(e) {
            e.preventDefault();
            seat.classList.remove('drag-over');
            
            if (draggedElement && !seat.classList.contains('occupied')) {
                handleDrop(seat, draggedElement);
            }
        });

        // إضافة إمكانية إزالة البطاقة بالنقر المزدوج
        seat.addEventListener('dblclick', function() {
            if (seat.classList.contains('occupied')) {
                removeTeacherFromSeat(seat);
            }
        });
    });

    // إعداد منطقة الأساتذة لاستقبال البطاقات المُعادة
    const teachersPool = document.getElementById('teachersPool');
    teachersPool.addEventListener('dragover', function(e) {
        e.preventDefault();
    });

    teachersPool.addEventListener('drop', function(e) {
        e.preventDefault();
        if (draggedElement && draggedElement.parentElement.classList.contains('seat')) {
            returnTeacherToPool(draggedElement);
        }
    });
}

// معالجة إفلات البطاقة في المقعد
function handleDrop(seat, teacherCard) {
    const teacherName = teacherCard.dataset.teacher;
    const hall = seat.closest('.hall');
    const day = hall.dataset.day;
    
    // التحقق من القيود
    if (!canAssignTeacher(teacherName, day)) {
        showAlert('error', `لا يمكن توزيع ${teacherName} في اليوم ${day} - الأستاذ موزع بالفعل في هذا اليوم!`);
        seat.classList.add('invalid-drop');
        setTimeout(() => seat.classList.remove('invalid-drop'), 1000);
        return;
    }

    // تنفيذ التوزيع
    assignTeacherToSeat(seat, teacherCard, day);
    showAlert('success', `تم توزيع ${teacherName} بنجاح في اليوم ${day}`);
}

// التحقق من إمكانية توزيع الأستاذ
function canAssignTeacher(teacherName, day) {
    const dayKey = `day${day}`;
    return !teacherAssignments[teacherName][dayKey];
}

// توزيع الأستاذ على المقعد
function assignTeacherToSeat(seat, teacherCard, day) {
    const teacherName = teacherCard.dataset.teacher;
    const dayKey = `day${day}`;
    
    // تحديث حالة التوزيع
    teacherAssignments[teacherName][dayKey] = true;
    teacherAssignments[teacherName].usedCards++;
    
    // نقل البطاقة إلى المقعد
    seat.appendChild(teacherCard);
    seat.classList.add('occupied');
    teacherCard.classList.add('used');
    teacherCard.draggable = true; // يمكن سحبها لإعادة توزيعها
    
    // إضافة تأثير النجاح
    seat.classList.add('drop-success');
    setTimeout(() => seat.classList.remove('drop-success'), 600);
    
    // تحديث البطاقات المتبقية
    updateRemainingCards(teacherName);
}

// إزالة الأستاذ من المقعد
function removeTeacherFromSeat(seat) {
    const teacherCard = seat.querySelector('.teacher-card');
    if (!teacherCard) return;
    
    const teacherName = teacherCard.dataset.teacher;
    const hall = seat.closest('.hall');
    const day = hall.dataset.day;
    const dayKey = `day${day}`;
    
    // تحديث حالة التوزيع
    teacherAssignments[teacherName][dayKey] = false;
    teacherAssignments[teacherName].usedCards--;
    
    // إعادة البطاقة إلى المجموعة
    returnTeacherToPool(teacherCard);
    
    showAlert('warning', `تم إزالة ${teacherName} من اليوم ${day}`);
}

// إعادة البطاقة إلى مجموعة الأساتذة
function returnTeacherToPool(teacherCard) {
    const teachersPool = document.getElementById('teachersPool');
    const seat = teacherCard.closest('.seat');
    
    if (seat) {
        const teacherName = teacherCard.dataset.teacher;
        const hall = seat.closest('.hall');
        const day = hall.dataset.day;
        const dayKey = `day${day}`;
        
        // تحديث حالة التوزيع
        teacherAssignments[teacherName][dayKey] = false;
        teacherAssignments[teacherName].usedCards--;
        
        seat.classList.remove('occupied');
    }
    
    // إعادة البطاقة إلى المجموعة
    teacherCard.classList.remove('used');
    teachersPool.appendChild(teacherCard);
    
    // تحديث البطاقات المتبقية
    updateRemainingCards(teacherCard.dataset.teacher);
}

// تحديث البطاقات المتبقية للأستاذ
function updateRemainingCards(teacherName) {
    const teacherCards = document.querySelectorAll(`[data-teacher="${teacherName}"]`);
    const usedCards = teacherAssignments[teacherName].usedCards;
    
    teacherCards.forEach((card, index) => {
        if (index < usedCards) {
            card.classList.add('used');
        } else {
            card.classList.remove('used');
        }
    });
}

// عرض رسائل التنبيه
function showAlert(type, message) {
    // إزالة التنبيهات السابقة
    const existingAlerts = document.querySelectorAll('.alert');
    existingAlerts.forEach(alert => alert.remove());
    
    // إنشاء تنبيه جديد
    const alert = document.createElement('div');
    alert.className = `alert ${type}`;
    alert.textContent = message;
    
    document.body.appendChild(alert);
    
    // إزالة التنبيه بعد 3 ثوان
    setTimeout(() => {
        alert.remove();
    }, 3000);
}

// وظائف إضافية للتحكم
function resetAllAssignments() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع التوزيعات؟')) {
        // إعادة جميع البطاقات إلى المجموعة
        const occupiedSeats = document.querySelectorAll('.seat.occupied');
        occupiedSeats.forEach(seat => {
            const teacherCard = seat.querySelector('.teacher-card');
            if (teacherCard) {
                returnTeacherToPool(teacherCard);
            }
        });
        
        // إعادة تهيئة التوزيعات
        initializeAssignments();
        
        showAlert('success', 'تم إعادة تعيين جميع التوزيعات بنجاح');
    }
}

// إضافة زر إعادة التعيين
document.addEventListener('DOMContentLoaded', function() {
    const resetButton = document.createElement('button');
    resetButton.textContent = 'إعادة تعيين الكل';
    resetButton.style.cssText = `
        position: fixed;
        bottom: 20px;
        left: 20px;
        background: #e74c3c;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 6px;
        cursor: pointer;
        font-weight: bold;
        z-index: 1000;
    `;
    resetButton.onclick = resetAllAssignments;
    document.body.appendChild(resetButton);
});

// تصدير البيانات (اختياري)
function exportAssignments() {
    const assignments = {};
    const occupiedSeats = document.querySelectorAll('.seat.occupied');
    
    occupiedSeats.forEach(seat => {
        const teacherCard = seat.querySelector('.teacher-card');
        const hall = seat.closest('.hall');
        
        if (teacherCard && hall) {
            const teacherName = teacherCard.dataset.teacher;
            const day = hall.dataset.day;
            const period = hall.dataset.period;
            const hallNum = hall.dataset.hall;
            const seatNum = seat.dataset.seat;
            
            if (!assignments[`day${day}`]) {
                assignments[`day${day}`] = {};
            }
            if (!assignments[`day${day}`][period]) {
                assignments[`day${day}`][period] = {};
            }
            if (!assignments[`day${day}`][period][`hall${hallNum}`]) {
                assignments[`day${day}`][period][`hall${hallNum}`] = {};
            }
            
            assignments[`day${day}`][period][`hall${hallNum}`][`seat${seatNum}`] = teacherName;
        }
    });
    
    console.log('التوزيعات الحالية:', assignments);
    return assignments;
}

// === نظام الاستيراد المدمج ===

// متغيرات نظام الاستيراد
const SERVER_URL = 'http://localhost:5000';
let serverConnected = false;
let currentFile = null;
let selectedFiles = [];
let shouldProceed = false;
let currentOperation = '';

// تهيئة نظام الاستيراد عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من حالة الاتصال بالخادم
    checkServerConnection();
    
    // تطبيق حجم الخط المحفوظ
    applySavedFontSize();
});

// التحقق من حالة الاتصال بالخادم
async function checkServerConnection() {
    const statusDiv = document.getElementById('connectionStatus');
    const importButtons = ['importMasarButton', 'importSecretButton', 'importTeachersButton'];
    
    try {
        const response = await fetch(`${SERVER_URL}/api/status`);
        const data = await response.json();
        
        if (data.status === 'connected') {
            statusDiv.textContent = '✅ متصل بالخادم وقاعدة البيانات - جاهز للاستيراد';
            statusDiv.className = 'connection-status connected';
            serverConnected = true;
            
            // تفعيل الأزرار
            importButtons.forEach(buttonId => {
                const button = document.getElementById(buttonId);
                if (button) {
                    button.disabled = false;
                }
            });
            
            log('✅ تم الاتصال بالخادم وقاعدة البيانات بنجاح', 'success');
        } else {
            throw new Error('فشل الاتصال');
        }
    } catch (error) {
        statusDiv.textContent = '❌ خطأ في الاتصال بالخادم - يرجى التأكد من تشغيل الخادم';
        statusDiv.className = 'connection-status error';
        serverConnected = false;
        
        // إلغاء تفعيل الأزرار
        importButtons.forEach(buttonId => {
            const button = document.getElementById(buttonId);
            if (button) {
                button.disabled = true;
            }
        });
        
        log('❌ فشل الاتصال بالخادم. يرجى التأكد من تشغيل masar_server.py', 'error');
    }
}

// بدء استيراد ملفات مسار
function startMasarImport() {
    if (!serverConnected) {
        log('❌ يجب الاتصال بالخادم أولاً', 'error');
        return;
    }
    
    currentOperation = 'masar';
    document.getElementById('excelFile').click();
    log('📂 يرجى اختيار ملف Excel الخاص بلوائح منظومة مسار...', 'info');
}

// بدء استيراد الرموز السرية
function startSecretCodesImport() {
    if (!serverConnected) {
        log('❌ يجب الاتصال بالخادم أولاً', 'error');
        return;
    }
    
    currentOperation = 'secret';
    document.getElementById('secretFilesInput').click();
    log('📂 يرجى اختيار ملفات Excel الخاصة بالرموز السرية... (يمكن اختيار عدة ملفات)', 'info');
}

// بدء استيراد بيانات الأساتذة
function startTeachersImport() {
    if (!serverConnected) {
        log('❌ يجب الاتصال بالخادم أولاً', 'error');
        return;
    }
    
    currentOperation = 'teachers';
    document.getElementById('teachersFileInput').click();
    log('📂 يرجى اختيار ملف Excel الخاص ببيانات الأساتذة...', 'info');
}

// معالجة اختيار ملف واحد (للوائح المسار)
function handleFileSelect(event) {
    const file = event.target.files[0];
    if (!file) {
        log('⚠️ تم إلغاء اختيار الملف', 'warning');
        return;
    }

    currentFile = file;
    const fileName = file.name;

    log(`📁 تم اختيار الملف: ${fileName}`, 'info');

    // التحقق من أن اسم الملف يحتوي على "ListEleve"
    if (!fileName.includes('ListEleve')) {
        const message = `الملف ${fileName} لا يحتوي على العبارة 'ListEleve' في اسمه. قد لا يكون هذا ملف لوائح منظومة مسار.`;
        log(`⚠️ ${message}`, 'warning');
        
        document.getElementById('modalMessage').textContent = message;
        document.getElementById('confirmModal').style.display = 'block';
    } else {
        shouldProceed = true;
        processMasarFile();
    }
}

// معالجة اختيار ملفات متعددة (للرموز السرية)
function handleSecretFilesSelect(event) {
    const files = Array.from(event.target.files);
    if (!files || files.length === 0) {
        log('⚠️ تم إلغاء اختيار الملفات', 'warning');
        return;
    }

    selectedFiles = files;

    if (files.length > 100) {
        log('⚠️ تم اختيار أكثر من 100 ملف. سيتم استيراد أول 100 ملف فقط.', 'warning');
        selectedFiles = files.slice(0, 100);
    }

    log(`📁 تم اختيار ${selectedFiles.length} ملف للاستيراد`, 'info');
    
    // عرض معلومات الملفات
    showFilesInfo(selectedFiles);
    
    shouldProceed = true;
    processSecretCodesFiles();
}

// معالجة اختيار ملف واحد (للأساتذة)
function handleTeachersFileSelect(event) {
    const file = event.target.files[0];
    if (!file) {
        log('⚠️ تم إلغاء اختيار الملف', 'warning');
        return;
    }

    currentFile = file;
    const fileName = file.name;

    log(`📁 تم اختيار الملف: ${fileName}`, 'info');

    // التحقق من الكلمات المفتاحية المتوقعة
    const validKeywords = ["SeancesEnseignants", "Book"];
    const isValidFile = validKeywords.some(keyword => fileName.includes(keyword));
    
    if (!isValidFile) {
        const message = `الملف ${fileName} لا يحتوي على "SeancesEnseignants" أو "Book" في اسمه. قد لا يكون هذا ملف أسماء الأساتذة صحيح.`;
        log(`⚠️ ${message}`, 'warning');
        
        document.getElementById('modalMessage').textContent = message;
        document.getElementById('confirmModal').style.display = 'block';
    } else {
        shouldProceed = true;
        processTeachersFile();
    }
}

// عرض معلومات الملفات
function showFilesInfo(files) {
    // إزالة معلومات الملفات الموجودة
    const existingInfo = document.querySelector('.files-info');
    if (existingInfo) {
        existingInfo.remove();
    }

    const filesInfo = document.createElement('div');
    filesInfo.className = 'files-info';
    filesInfo.innerHTML = `
        <strong>📁 الملفات المختارة (${files.length}):</strong><br>
        ${files.slice(0, 10).map(f => `• ${f.name}`).join('<br>')}
        ${files.length > 10 ? `<br>... و ${files.length - 10} ملف آخر` : ''}
    `;
    
    const progressContainer = document.getElementById('progressContainer');
    progressContainer.parentNode.insertBefore(filesInfo, progressContainer);
}

// تأكيد الاستيراد بعد التحذير
function confirmImport() {
    document.getElementById('confirmModal').style.display = 'none';
    log('✅ تم اختيار الاستمرار في الاستيراد رغم التحذير', 'info');
    shouldProceed = true;
    
    if (currentOperation === 'masar') {
        processMasarFile();
    } else if (currentOperation === 'teachers') {
        processTeachersFile();
    }
}

// إلغاء الاستيراد
function cancelImport() {
    document.getElementById('confirmModal').style.display = 'none';
    log('❌ تم إلغاء عملية الاستيراد بناءً على طلب المستخدم', 'info');
    currentFile = null;
    selectedFiles = [];
    shouldProceed = false;
}

// معالجة ملف مسار
async function processMasarFile() {
    if (!currentFile || !shouldProceed || !serverConnected) return;

    try {
        // عرض شريط التقدم
        document.getElementById('progressContainer').style.display = 'block';
        updateProgress(0, 'جاري رفع الملف إلى الخادم...');

        // تحضير بيانات النموذج
        const formData = new FormData();
        formData.append('file', currentFile);

        // رفع ومعالجة الملف
        const response = await fetch(`${SERVER_URL}/api/import-excel`, {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (result.status === 'success') {
            // معالجة الرسائل
            for (const msg of result.messages) {
                if (msg.type === 'progress' && msg.progress) {
                    updateProgress(msg.progress, msg.message.split(' - ')[1] || msg.message);
                }
                log(msg.message, msg.type);
            }

            // عرض الملخص
            if (result.summary) {
                showMasarSummary(result.summary);
            }

            log('🎉 تم الانتهاء من استيراد البيانات بنجاح!', 'success');
            log('✨ نتمنى لك التوفيق والنجاح في عملك!', 'success');

        } else {
            handleImportError(result);
        }

        // إخفاء شريط التقدم بعد تأخير
        setTimeout(() => {
            document.getElementById('progressContainer').style.display = 'none';
        }, 3000);

    } catch (error) {
        log(`❌ خطأ في الاتصال بالخادم: ${error.message}`, 'error');
        updateProgress(0, 'خطأ في الاتصال');
        setTimeout(() => {
            document.getElementById('progressContainer').style.display = 'none';
        }, 3000);
    }
}

// معالجة ملفات الرموز السرية
async function processSecretCodesFiles() {
    if (!selectedFiles || selectedFiles.length === 0 || !shouldProceed || !serverConnected) return;

    try {
        // عرض شريط التقدم
        document.getElementById('progressContainer').style.display = 'block';
        updateProgress(0, 'جاري رفع الملفات إلى الخادم...');

        // تحضير بيانات النموذج
        const formData = new FormData();
        selectedFiles.forEach(file => {
            formData.append('files', file);
        });

        // رفع ومعالجة الملفات
        const response = await fetch(`${SERVER_URL}/api/import-secret-codes`, {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (result.status === 'success') {
            // معالجة الرسائل
            for (const msg of result.messages) {
                if (msg.type === 'progress' && msg.progress) {
                    updateProgress(msg.progress, msg.message.split(' - ')[1] || msg.message);
                }
                log(msg.message, msg.type);
            }

            // عرض الملخص
            if (result.summary) {
                showSecretCodesSummary(result.summary);
            }

            log('🎉 تم الانتهاء من استيراد الرموز السرية بنجاح!', 'success');

        } else {
            handleImportError(result);
        }

        // إخفاء شريط التقدم بعد تأخير
        setTimeout(() => {
            document.getElementById('progressContainer').style.display = 'none';
        }, 3000);

    } catch (error) {
        log(`❌ خطأ في الاتصال بالخادم: ${error.message}`, 'error');
        updateProgress(0, 'خطأ في الاتصال');
        setTimeout(() => {
            document.getElementById('progressContainer').style.display = 'none';
        }, 3000);
    }
}

// معالجة ملف الأساتذة
async function processTeachersFile() {
    if (!currentFile || !shouldProceed || !serverConnected) return;

    try {
        // عرض شريط التقدم
        document.getElementById('progressContainer').style.display = 'block';
        updateProgress(0, 'جاري رفع الملف إلى الخادم...');

        // تحضير بيانات النموذج
        const formData = new FormData();
        formData.append('file', currentFile);

        // رفع ومعالجة الملف
        const response = await fetch(`${SERVER_URL}/api/import-teachers`, {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (result.status === 'success') {
            // معالجة الرسائل
            for (const msg of result.messages) {
                if (msg.type === 'progress' && msg.progress) {
                    updateProgress(msg.progress, msg.message.split(' - ')[1] || msg.message);
                }
                log(msg.message, msg.type);
            }

            // عرض الملخص
            if (result.summary) {
                showTeachersSummary(result.summary);
            }

            log('🎉 تم الانتهاء من استيراد بيانات الأساتذة بنجاح!', 'success');

        } else {
            handleImportError(result);
        }

        // إخفاء شريط التقدم بعد تأخير
        setTimeout(() => {
            document.getElementById('progressContainer').style.display = 'none';
        }, 3000);

    } catch (error) {
        log(`❌ خطأ في الاتصال بالخادم: ${error.message}`, 'error');
        updateProgress(0, 'خطأ في الاتصال');
        setTimeout(() => {
            document.getElementById('progressContainer').style.display = 'none';
        }, 3000);
    }
}

// إضافة رسالة إلى السجل
function log(message, type = 'info') {
    const logContainer = document.getElementById('logContainer');
    if (!logContainer) return;

    const timestamp = new Date().toLocaleTimeString('ar-SA');
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry log-${type}`;
    logEntry.textContent = `[${timestamp}] ${message}`;
    
    logContainer.appendChild(logEntry);
    logContainer.scrollTop = logContainer.scrollHeight;
}

// تحديث شريط التقدم
function updateProgress(percentage, text) {
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');
    
    if (progressBar) {
        progressBar.style.width = `${percentage}%`;
        progressBar.textContent = `${percentage}%`;
    }
    
    if (progressText && text) {
        progressText.textContent = text;
    }
}

// عرض ملخص استيراد مسار
function showMasarSummary(summary) {
    log('=== ملخص عملية الاستيراد ===', 'info');
    log(`📊 إجمالي الطلاب المستوردين: ${summary.total_students || 0}`, 'success');
    log(`🏫 عدد الأقسام: ${summary.classes_count || 0}`, 'info');
    log(`📚 عدد المواد: ${summary.subjects_count || 0}`, 'info');
    
    if (summary.classes) {
        log('🎓 الأقسام المستوردة:', 'info');
        summary.classes.forEach(className => {
            log(`  • ${className}`, 'info');
        });
    }
}

// عرض ملخص الرموز السرية
function showSecretCodesSummary(summary) {
    log('=== ملخص استيراد الرموز السرية ===', 'info');
    log(`🔐 إجمالي الرموز المستوردة: ${summary.total_codes || 0}`, 'success');
    log(`📁 عدد الملفات المعالجة: ${summary.files_processed || 0}`, 'info');
    log(`✅ الرموز الصحيحة: ${summary.valid_codes || 0}`, 'success');
    log(`❌ الرموز غير الصحيحة: ${summary.invalid_codes || 0}`, 'warning');
}

// عرض ملخص الأساتذة
function showTeachersSummary(summary) {
    log('=== ملخص استيراد الأساتذة ===', 'info');
    log(`👨‍🏫 إجمالي الأساتذة: ${summary.total_teachers || 0}`, 'success');
    log(`📚 عدد المواد: ${summary.subjects_count || 0}`, 'info');
    log(`📊 إجمالي الحصص: ${summary.total_sessions || 0}`, 'info');
}

// معالجة أخطاء الاستيراد
function handleImportError(result) {
    log(`❌ فشل في عملية الاستيراد: ${result.message || 'خطأ غير معروف'}`, 'error');
    
    if (result.messages) {
        result.messages.forEach(msg => {
            log(msg.message, msg.type);
        });
    }
    
    if (result.details) {
        log(`تفاصيل الخطأ: ${result.details}`, 'error');
    }
}

// === وظائف التحكم في الخط ===

// زيادة حجم الخط
function increaseFontSize() {
    const currentSize = getCurrentFontSize();
    const newSize = Math.min(currentSize + 1, 20);
    setFontSize(newSize);
}

// تقليل حجم الخط
function decreaseFontSize() {
    const currentSize = getCurrentFontSize();
    const newSize = Math.max(currentSize - 1, 10);
    setFontSize(newSize);
}

// إعادة تعيين حجم الخط
function resetFontSize() {
    setFontSize(13);
}

// الحصول على حجم الخط الحالي
function getCurrentFontSize() {
    const saved = localStorage.getItem('fontSize');
    return saved ? parseInt(saved) : 13;
}

// تعيين حجم الخط
function setFontSize(size) {
    document.body.style.fontSize = `${size}px`;
    
    // تحديث عرض حجم الخط
    const display = document.getElementById('fontSizeDisplay');
    if (display) {
        display.textContent = `${size}px`;
    }
    
    // حفظ التفضيل
    localStorage.setItem('fontSize', size);
    
    log(`📝 تم تغيير حجم الخط إلى ${size}px`, 'info');
}

// تطبيق حجم الخط المحفوظ
function applySavedFontSize() {
    const savedSize = getCurrentFontSize();
    setFontSize(savedSize);
}
