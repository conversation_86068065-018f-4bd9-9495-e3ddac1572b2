import sys
import sqlite3
import os
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QPushButton

class TableStructureWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("استخراج بنية جدول من قاعدة البيانات")
        self.setGeometry(100, 100, 300, 100)

        layout = QVBoxLayout()

        self.btn = QPushButton("📋 طباعة بنية ")
        self.btn.clicked.connect(self.print_structure)
        layout.addWidget(self.btn)

        self.setLayout(layout)

    def print_structure(self):
        try:
            db_path = os.path.join(os.path.dirname(__file__), "data.db")
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            cursor.execute("PRAGMA table_info('بيانات_المؤسسة')")
            rows = cursor.fetchall()

            if not rows:
                print("⚠️ لم يتم العثور على الجدول أو الجدول فارغ.")
                return

            print("📋 بنية الجدول: جدول_البيانات")
            for row in rows:
                print(f"- الاسم: {row[1]}, النوع: {row[2]}, أساسي: {'نعم' if row[5] else 'لا'}")

            conn.close()
        except Exception as e:
            print(f"❌ حدث خطأ أثناء الاتصال بقاعدة البيانات: {e}")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = TableStructureWindow()
    window.show()
    sys.exit(app.exec_())
