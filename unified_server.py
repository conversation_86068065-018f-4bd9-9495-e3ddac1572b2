"""
الخادم الموحد لمنظومة إدارة التعليم
خادم واحد منظم يجمع جميع الوظائف مع فصل المسؤوليات
"""

from flask import Flask, request, jsonify, send_from_directory, send_file
from flask_cors import CORS
import sqlite3
import pandas as pd
import os
import json
from datetime import datetime
import traceback
import shutil
import zipfile
import tempfile
from openpyxl import load_workbook
import base64
import re

app = Flask(__name__)
CORS(app)

# ===================================
# إعدادات المشروع
# ===================================

# مسار قاعدة البيانات
DB_PATH = "data.db"

# قائمة الجداول المراد حذفها في عمليات الصيانة
TABLES_TO_CLEAR = [
    'الحساب_الرئيسي', 'المسحوبات', 'registration_fees',
    'احصائيات_الغياب_الشهرية', 'احصائيات_الغياب_السنوية',
    'الاساتذة', 'المصاريف', 'الموازنة_السنوية',
    'الواجبات_الشهرية', 'تدوين_الغياب', 'جدول_الاداءات',
    'جدول_البيانات', 'جدول_المواد_والاقسام',
    'tasks_appointments', 'واجبات_التسجيل', 'monthly_duties'
]

# ===================================
# مساعدات الأدوات
# ===================================

def log_message(message, status="info"):
    """إنشاء رسالة مع الطابع الزمني"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    icons = {
        "info": "ℹ️", "success": "✅", "error": "❌",
        "warning": "⚠️", "progress": "🔄"
    }
    return f"{icons.get(status, 'ℹ️')} {timestamp} - {message}"

def get_db_connection():
    """إنشاء اتصال بقاعدة البيانات مع إرجاع النتائج كقاموس"""
    try:
        if not os.path.exists(DB_PATH):
            conn = sqlite3.connect(DB_PATH)
            conn.close()
        
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row
        return conn
    except Exception as e:
        print(f"خطأ في الاتصال بقاعدة البيانات: {str(e)}")
        return None

def create_backup(backup_name=None):
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    try:
        if not os.path.exists(DB_PATH):
            return {'status': 'error', 'message': 'قاعدة البيانات غير موجودة'}
        
        # إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
        backup_dir = "backups"
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)
        
        # تحديد اسم النسخة الاحتياطية
        if not backup_name:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"backup_{timestamp}.db"
        
        backup_path = os.path.join(backup_dir, backup_name)
        
        # نسخ قاعدة البيانات
        shutil.copy2(DB_PATH, backup_path)
        
        return {
            'status': 'success',
            'message': f'تم إنشاء النسخة الاحتياطية: {backup_name}',
            'backup_path': backup_path
        }
    except Exception as e:
        return {'status': 'error', 'message': f'خطأ في إنشاء النسخة الاحتياطية: {str(e)}'}

# ===================================
# وظائف قاعدة البيانات الأساسية
# ===================================

class DatabaseManager:
    """إدارة عمليات قاعدة البيانات"""
    
    @staticmethod
    def execute_query(query, params=None, fetch=True):
        """تنفيذ استعلام قاعدة البيانات"""
        try:
            conn = get_db_connection()
            if not conn:
                return {'status': 'error', 'message': 'فشل في الاتصال بقاعدة البيانات'}
            
            cursor = conn.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            if fetch:
                if query.strip().upper().startswith('SELECT'):
                    result = [dict(row) for row in cursor.fetchall()]
                else:
                    result = cursor.rowcount
            else:
                result = cursor.rowcount
            
            conn.commit()
            conn.close()
            
            return {'status': 'success', 'data': result}
        except Exception as e:
            return {'status': 'error', 'message': str(e)}
    
    @staticmethod
    def get_table_info(table_name):
        """الحصول على معلومات الجدول"""
        try:
            conn = get_db_connection()
            if not conn:
                return {'status': 'error', 'message': 'فشل في الاتصال بقاعدة البيانات'}
            
            cursor = conn.cursor()
            
            # الحصول على أسماء الأعمدة
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = [row[1] for row in cursor.fetchall()]
            
            # الحصول على عدد الصفوف
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            
            conn.close()
            
            return {
                'status': 'success',
                'columns': columns,
                'count': count
            }
        except Exception as e:
            return {'status': 'error', 'message': str(e)}
    
    @staticmethod
    def get_all_tables():
        """الحصول على جميع الجداول في قاعدة البيانات"""
        try:
            conn = get_db_connection()
            if not conn:
                return {'status': 'error', 'message': 'فشل في الاتصال بقاعدة البيانات'}
            
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            conn.close()
            
            return {'status': 'success', 'tables': tables}
        except Exception as e:
            return {'status': 'error', 'message': str(e)}

# ===================================
# واجهات برمجة التطبيقات الأساسية
# ===================================

@app.route('/')
def index():
    """عرض الصفحة الرئيسية"""
    return send_from_directory('.', 'index.html')

@app.route('/api/test')
def test_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    try:
        conn = get_db_connection()
        if conn is None:
            return jsonify({'error': 'لا يمكن الاتصال بقاعدة البيانات'}), 500
        
        cursor = conn.cursor()
        cursor.execute('SELECT COUNT(*) as count FROM sqlite_master WHERE type="table"')
        result = cursor.fetchone()
        conn.close()
        
        return jsonify({
            'status': 'success',
            'message': 'تم الاتصال بقاعدة البيانات بنجاح',
            'total_tables': result[0]
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'خطأ في الاتصال: {str(e)}'
        }), 500

@app.route('/api/database/info')
def database_info():
    """معلومات قاعدة البيانات"""
    tables_info = DatabaseManager.get_all_tables()
    if tables_info['status'] == 'error':
        return jsonify(tables_info), 500
    
    return jsonify({
        'status': 'success',
        'database_path': DB_PATH,
        'exists': os.path.exists(DB_PATH),
        'tables': tables_info['tables'],
        'tables_count': len(tables_info['tables'])
    })

@app.route('/api/database/query', methods=['POST'])
def execute_database_query():
    """تنفيذ استعلام مخصص على قاعدة البيانات"""
    try:
        data = request.get_json()
        query = data.get('query', '')
        params = data.get('params', None)
        
        if not query:
            return jsonify({'status': 'error', 'message': 'لم يتم تحديد الاستعلام'}), 400
        
        result = DatabaseManager.execute_query(query, params)
        return jsonify(result)
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/api/table/<table_name>/info')
def table_info(table_name):
    """معلومات جدول محدد"""
    result = DatabaseManager.get_table_info(table_name)
    return jsonify(result)

@app.route('/api/table/<table_name>/data')
def table_data(table_name):
    """الحصول على بيانات جدول محدد"""
    limit = request.args.get('limit', 100, type=int)
    offset = request.args.get('offset', 0, type=int)
    
    query = f"SELECT * FROM {table_name} LIMIT {limit} OFFSET {offset}"
    result = DatabaseManager.execute_query(query)
    
    return jsonify(result)

@app.route('/api/check-status')
def check_status():
    """التحقق من حالة الخادم وقاعدة البيانات"""
    try:
        conn = get_db_connection()
        if conn is None:
            return jsonify({
                'status': 'error',
                'message': 'لا يمكن الاتصال بقاعدة البيانات',
                'server_status': 'running',
                'database_status': 'disconnected'
            }), 500
        
        cursor = conn.cursor()
        cursor.execute('SELECT COUNT(*) as count FROM sqlite_master WHERE type="table"')
        result = cursor.fetchone()
        conn.close()
        
        return jsonify({
            'status': 'success',
            'message': 'النظام يعمل بشكل طبيعي',
            'server_status': 'running',
            'database_status': 'connected',
            'total_tables': result[0],
            'database_file': DB_PATH
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'خطأ في التحقق من الحالة: {str(e)}',
            'server_status': 'running',
            'database_status': 'error'
        }), 500

# ===================================
# وظائف الصيانة والنسخ الاحتياطي
# ===================================

@app.route('/api/maintenance/backup', methods=['POST'])
def create_database_backup():
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    try:
        data = request.get_json() or {}
        backup_name = data.get('backup_name', None)
        
        result = create_backup(backup_name)
        return jsonify(result)
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'خطأ في إنشاء النسخة الاحتياطية: {str(e)}'
        }), 500

@app.route('/api/maintenance/restore', methods=['POST'])
def restore_database():
    """استعادة قاعدة البيانات من نسخة احتياطية"""
    try:
        data = request.get_json()
        backup_path = data.get('backup_path', '')
        
        if not backup_path or not os.path.exists(backup_path):
            return jsonify({
                'status': 'error',
                'message': 'ملف النسخة الاحتياطية غير موجود'
            }), 400
        
        # إنشاء نسخة احتياطية من قاعدة البيانات الحالية قبل الاستعادة
        current_backup = create_backup(f"before_restore_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db")
        
        # استعادة قاعدة البيانات
        shutil.copy2(backup_path, DB_PATH)
        
        return jsonify({
            'status': 'success',
            'message': 'تم استعادة قاعدة البيانات بنجاح',
            'backup_created': current_backup['backup_path']
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'خطأ في استعادة قاعدة البيانات: {str(e)}'
        }), 500

@app.route('/api/maintenance/clear-tables', methods=['POST'])
def clear_tables():
    """حذف محتويات الجداول المحددة"""
    try:
        data = request.get_json() or {}
        tables_to_clear = data.get('tables', TABLES_TO_CLEAR)
        
        # إنشاء نسخة احتياطية قبل الحذف
        backup_result = create_backup(f"before_clear_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db")
        
        conn = get_db_connection()
        if not conn:
            return jsonify({'status': 'error', 'message': 'فشل في الاتصال بقاعدة البيانات'}), 500
        
        cursor = conn.cursor()
        cleared_tables = []
        
        for table in tables_to_clear:
            try:
                cursor.execute(f"DELETE FROM {table}")
                cleared_tables.append(table)
            except sqlite3.OperationalError:
                # الجدول غير موجود
                continue
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'status': 'success',
            'message': f'تم حذف محتويات {len(cleared_tables)} جدول',
            'cleared_tables': cleared_tables,
            'backup_created': backup_result['backup_path']
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'خطأ في حذف الجداول: {str(e)}'
        }), 500

# ===================================
# وظائف استيراد البيانات
# ===================================

@app.route('/api/import/excel', methods=['POST'])
def import_excel_data():
    """استيراد بيانات من ملف Excel"""
    try:
        if 'file' not in request.files:
            return jsonify({'status': 'error', 'message': 'لم يتم رفع أي ملف'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'status': 'error', 'message': 'لم يتم اختيار ملف'}), 400
        
        messages = []
        messages.append({'message': '🔄 بدء عملية تحليل الملف...', 'type': 'progress', 'progress': 10})
        
        # قراءة الملف
        try:
            df = pd.read_excel(file)
            messages.append({'message': f'✅ تم قراءة الملف بنجاح - {len(df)} صف', 'type': 'success', 'progress': 30})
        except Exception as e:
            return jsonify({
                'status': 'error',
                'message': f'خطأ في قراءة ملف Excel: {str(e)}'
            }), 400
        
        messages.append({'message': '🔄 إنشاء نسخة احتياطية...', 'type': 'progress', 'progress': 40})
        
        # إنشاء نسخة احتياطية قبل الاستيراد
        backup_result = create_backup(f"before_import_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db")
        messages.append({'message': f'✅ تم إنشاء نسخة احتياطية: {backup_result["backup_path"]}', 'type': 'success', 'progress': 50})
        
        messages.append({'message': '🔄 استيراد البيانات إلى قاعدة البيانات...', 'type': 'progress', 'progress': 70})
        
        # استيراد البيانات إلى قاعدة البيانات
        conn = get_db_connection()
        df.to_sql('جدول_البيانات', conn, if_exists='replace', index=False)
        conn.close()
        
        messages.append({'message': f'✅ تم استيراد {len(df)} صف بنجاح', 'type': 'success', 'progress': 100})
        
        # معلومات الملف
        file_info = {
            'filename': file.filename,
            'rows': len(df),
            'columns': list(df.columns),
            'columns_count': len(df.columns)
        }
        
        summary = {
            'total_imported': len(df),
            'backup_created': backup_result['backup_path'],
            'file_info': file_info
        }
        
        return jsonify({
            'status': 'success',
            'message': 'تم استيراد البيانات بنجاح',
            'messages': messages,
            'summary': summary,
            'imported_rows': len(df),
            'backup_created': backup_result['backup_path']
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'خطأ في معالجة الملف: {str(e)}'
        }), 500

@app.route('/api/import/students', methods=['POST'])
def import_students_data():
    """استيراد بيانات الطلاب من Excel إلى قاعدة البيانات"""
    try:
        if 'file' not in request.files:
            return jsonify({'status': 'error', 'message': 'لم يتم رفع أي ملف'}), 400
        
        file = request.files['file']
        table_name = request.form.get('table_name', 'جدول_البيانات')
        
        # قراءة الملف
        df = pd.read_excel(file)
        
        # إنشاء نسخة احتياطية قبل الاستيراد
        backup_result = create_backup(f"before_import_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db")
        
        # استيراد البيانات إلى قاعدة البيانات
        conn = get_db_connection()
        df.to_sql(table_name, conn, if_exists='replace', index=False)
        conn.close()
        
        return jsonify({
            'status': 'success',
            'message': f'تم استيراد {len(df)} صف إلى جدول {table_name}',
            'imported_rows': len(df),
            'backup_created': backup_result['backup_path']
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'خطأ في استيراد البيانات: {str(e)}'
        }), 500

@app.route('/api/import/masar', methods=['POST'])
def import_masar_data():
    """استيراد بيانات من منظومة مسار"""
    try:
        if 'file' not in request.files:
            return jsonify({'status': 'error', 'message': 'لم يتم رفع أي ملف'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'status': 'error', 'message': 'لم يتم اختيار ملف'}), 400
        
        # قراءة الملف
        try:
            df = pd.read_excel(file)
        except Exception as e:
            return jsonify({
                'status': 'error',
                'message': f'خطأ في قراءة ملف Excel: {str(e)}'
            }), 400
        
        # إنشاء نسخة احتياطية قبل الاستيراد
        backup_result = create_backup(f"before_masar_import_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db")
        
        # استيراد البيانات إلى جدول البيانات الرئيسي
        conn = get_db_connection()
        if not conn:
            return jsonify({'status': 'error', 'message': 'فشل في الاتصال بقاعدة البيانات'}), 500
        
        # استيراد البيانات مع استبدال الجدول الموجود
        df.to_sql('جدول_البيانات', conn, if_exists='replace', index=False)
        conn.close()
        
        return jsonify({
            'status': 'success',
            'message': f'تم استيراد {len(df)} سجل من منظومة مسار بنجاح',
            'imported_rows': len(df),
            'backup_created': backup_result['backup_path']
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'خطأ في استيراد بيانات مسار: {str(e)}'
        }), 500

# ===================================
# وظائف إدارة بيانات المؤسسة
# ===================================

@app.route('/api/institution/setup', methods=['POST'])
def setup_institution_table():
    """إنشاء جدول بيانات المؤسسة إذا لم يكن موجوداً"""
    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({'status': 'error', 'message': 'فشل في الاتصال بقاعدة البيانات'}), 500
        
        cursor = conn.cursor()
        
        # إنشاء الجدول
        cursor.execute('''CREATE TABLE IF NOT EXISTS بيانات_المؤسسة (
            الأكاديمية TEXT,
            المديرية TEXT,
            الجماعة TEXT,
            المؤسسة TEXT,
            السنة_الدراسية TEXT,
            البلدة TEXT,
            المدير TEXT,
            الحارس_العام TEXT,
            السلك TEXT,
            رقم_الحراسة TEXT,
            رقم_التسجيل TEXT,
            الأسدس TEXT,
            ImagePath1 TEXT
        )''')
        
        # التحقق من وجود سجلات
        cursor.execute("SELECT COUNT(*) FROM بيانات_المؤسسة")
        count = cursor.fetchone()[0]
        
        # إذا لم يكن هناك سجلات، إنشاء سجل فارغ
        if count == 0:
            cursor.execute("""
                INSERT INTO بيانات_المؤسسة (
                    الأكاديمية, المديرية, الجماعة, المؤسسة,
                    السنة_الدراسية, البلدة, المدير, الحارس_العام,
                    السلك, رقم_الحراسة, رقم_التسجيل, الأسدس, ImagePath1
                ) VALUES (
                    '', '', '', '',
                    '', '', '', '',
                    '', '', '', '', ''
                )
            """)
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'status': 'success',
            'message': 'تم إعداد جدول بيانات المؤسسة بنجاح',
            'records_count': count
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'خطأ في إعداد جدول بيانات المؤسسة: {str(e)}'
        }), 500

@app.route('/api/institution/data', methods=['GET'])
def get_institution_data():
    """الحصول على بيانات المؤسسة"""
    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({'status': 'error', 'message': 'فشل في الاتصال بقاعدة البيانات'}), 500
        
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM بيانات_المؤسسة LIMIT 1")
        row = cursor.fetchone()
        
        if row:
            # تحويل النتيجة إلى قاموس
            columns = [description[0] for description in cursor.description]
            data = dict(zip(columns, row))
        else:
            data = None
        
        conn.close()
        
        return jsonify({
            'status': 'success',
            'data': data
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'خطأ في استرجاع بيانات المؤسسة: {str(e)}'
        }), 500

@app.route('/api/institution/save', methods=['POST'])
def save_institution_data():
    """حفظ أو تحديث بيانات المؤسسة"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'status': 'error', 'message': 'لا توجد بيانات للحفظ'}), 400
        
        conn = get_db_connection()
        if not conn:
            return jsonify({'status': 'error', 'message': 'فشل في الاتصال بقاعدة البيانات'}), 500
        
        cursor = conn.cursor()
        
        # التحقق من وجود بيانات
        cursor.execute("SELECT COUNT(*) FROM بيانات_المؤسسة")
        count = cursor.fetchone()[0]
        
        if count > 0:
            # تحديث البيانات الموجودة
            cursor.execute("""
                UPDATE بيانات_المؤسسة SET 
                الأكاديمية = ?, المديرية = ?, الجماعة = ?, المؤسسة = ?,
                السنة_الدراسية = ?, البلدة = ?, المدير = ?, الحارس_العام = ?,
                السلك = ?, رقم_الحراسة = ?, رقم_التسجيل = ?, الأسدس = ?, ImagePath1 = ?
                WHERE rowid = 1
            """, (
                data.get('الأكاديمية', ''),
                data.get('المديرية', ''),
                data.get('الجماعة', ''),
                data.get('المؤسسة', ''),
                data.get('السنة_الدراسية', ''),
                data.get('البلدة', ''),
                data.get('المدير', ''),
                data.get('الحارس_العام', ''),
                data.get('السلك', ''),
                data.get('رقم_الحراسة', ''),
                data.get('رقم_التسجيل', ''),
                data.get('الأسدس', ''),
                data.get('ImagePath1', '')
            ))
        else:
            # إدراج بيانات جديدة
            cursor.execute("""
                INSERT INTO بيانات_المؤسسة (
                    الأكاديمية, المديرية, الجماعة, المؤسسة,
                    السنة_الدراسية, البلدة, المدير, الحارس_العام,
                    السلك, رقم_الحراسة, رقم_التسجيل, الأسدس, ImagePath1
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                data.get('الأكاديمية', ''),
                data.get('المديرية', ''),
                data.get('الجماعة', ''),
                data.get('المؤسسة', ''),
                data.get('السنة_الدراسية', ''),
                data.get('البلدة', ''),
                data.get('المدير', ''),
                data.get('الحارس_العام', ''),
                data.get('السلك', ''),
                data.get('رقم_الحراسة', ''),
                data.get('رقم_التسجيل', ''),
                data.get('الأسدس', ''),
                data.get('ImagePath1', '')
            ))
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'status': 'success',
            'message': 'تم حفظ بيانات المؤسسة بنجاح'
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'خطأ في حفظ بيانات المؤسسة: {str(e)}'
        }), 500

# ===================================
# وظائف الإحصائيات المتقدمة
# ===================================

@app.route('/api/debug/tables', methods=['GET'])
def debug_tables():
    """فحص تفصيلي للجداول والأعمدة المتاحة"""
    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({'status': 'error', 'message': 'فشل في الاتصال بقاعدة البيانات'}), 500
        
        cursor = conn.cursor()
        
        # الحصول على جميع الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        tables_info = {}
        
        for table in tables:
            try:
                # معلومات الأعمدة
                cursor.execute(f"PRAGMA table_info({table})")
                columns = [{'name': row[1], 'type': row[2]} for row in cursor.fetchall()]
                
                # عدد الصفوف
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                row_count = cursor.fetchone()[0]
                
                # عينة من البيانات (أول 3 صفوف)
                cursor.execute(f"SELECT * FROM {table} LIMIT 3")
                sample_data = [dict(zip([col['name'] for col in columns], row)) for row in cursor.fetchall()]
                
                tables_info[table] = {
                    'columns': columns,
                    'row_count': row_count,
                    'sample_data': sample_data
                }
                
            except Exception as e:
                tables_info[table] = {'error': str(e)}
        
        conn.close()
        
        return jsonify({
            'status': 'success',
            'data': {
                'tables_count': len(tables),
                'tables': tables_info
            }
        })
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'خطأ في فحص الجداول: {str(e)}'
        }), 500

@app.route('/api/statistics/academic-year', methods=['GET'])
def get_academic_year():
    """الحصول على السنة الدراسية من جدول بيانات_المؤسسة"""
    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({'status': 'error', 'message': 'فشل في الاتصال بقاعدة البيانات'}), 500
        
        cursor = conn.cursor()
        
        # التحقق من وجود جدول بيانات_المؤسسة
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='بيانات_المؤسسة'")
        if not cursor.fetchone():
            return jsonify({
                'status': 'warning',
                'message': 'جدول بيانات_المؤسسة غير موجود',
                'academic_year': ''
            })
        
        # جلب السنة الدراسية
        cursor.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
        result = cursor.fetchone()
        
        conn.close()
        
        academic_year = result[0] if result and result[0] else ''
        
        return jsonify({
            'status': 'success',
            'academic_year': academic_year,
            'message': 'تم جلب السنة الدراسية بنجاح' if academic_year else 'لا توجد سنة دراسية محددة'
        })
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'خطأ في جلب السنة الدراسية: {str(e)}'
        }), 500

@app.route('/api/statistics/general', methods=['GET'])
def get_general_statistics():
    """الحصول على الإحصائيات العامة للمؤسسة - محدث ليتبع منطق النظام الأصلي"""
    try:
        academic_year = request.args.get('academic_year', '')
        print(f"📊 طلب إحصائيات - السنة الدراسية: '{academic_year}'")
        
        conn = get_db_connection()
        if not conn:
            print("❌ فشل في الاتصال بقاعدة البيانات")
            return jsonify({'status': 'error', 'message': 'فشل في الاتصال بقاعدة البيانات'}), 500
        
        cursor = conn.cursor()
        
        # إذا لم تُحدد السنة الدراسية، جلبها من جدول بيانات_المؤسسة
        if not academic_year:
            try:
                cursor.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
                result = cursor.fetchone()
                if result and result[0]:
                    academic_year = result[0]
                    print(f"📅 تم جلب السنة الدراسية من جدول بيانات_المؤسسة: {academic_year}")
            except Exception as e:
                print(f"❌ خطأ في جلب السنة الدراسية: {e}")
        
        # استخدام منطق النظام الأصلي - البحث في البنية_التربوية أولاً
        statistics = {
            "levels": 0,
            "sections": 0,
            "students": 0,
            "males": 0,
            "females": 0
        }
        
        # فحص الجداول المتاحة
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        available_tables = [row[0] for row in cursor.fetchall()]
        print(f"📋 الجداول المتاحة: {available_tables}")
        
        # تحديد شرط السنة الدراسية
        year_condition = f" WHERE السنة_الدراسية = '{academic_year}'" if academic_year else ""
        year_params = [academic_year] if academic_year else []
        
        # استخراج الإحصائيات العامة من البنية_التربوية
        if 'البنية_التربوية' in available_tables:
            print("✅ استخدام جدول البنية_التربوية")
            
            # عدد المستويات
            try:
                query = f"SELECT COUNT(DISTINCT المستوى) FROM البنية_التربوية{year_condition}"
                print(f"🔍 استعلام المستويات: {query}")
                cursor.execute(query, year_params)
                result = cursor.fetchone()
                statistics["levels"] = result[0] or 0
                print(f"� عدد المستويات: {statistics['levels']}")
            except Exception as e:
                print(f"❌ خطأ في حساب المستويات: {e}")
            
            # عدد الأقسام
            try:
                query = f"SELECT COUNT(DISTINCT القسم) FROM البنية_التربوية{year_condition}"
                print(f"🔍 استعلام الأقسام: {query}")
                cursor.execute(query, year_params)
                result = cursor.fetchone()
                statistics["sections"] = result[0] or 0
                print(f"🏫 عدد الأقسام: {statistics['sections']}")
            except Exception as e:
                print(f"❌ خطأ في حساب الأقسام: {e}")
            
            # إجمالي التلاميذ
            try:
                query = f"SELECT SUM(مجموع_التلاميذ) FROM البنية_التربوية{year_condition}"
                print(f"🔍 استعلام مجموع التلاميذ: {query}")
                cursor.execute(query, year_params)
                result = cursor.fetchone()
                statistics["students"] = result[0] or 0
                print(f"� عدد التلاميذ: {statistics['students']}")
            except Exception as e:
                print(f"❌ خطأ في حساب عدد التلاميذ: {e}")
        
        # استخراج إحصائيات النوع من السجل_العام + اللوائح
        if 'السجل_العام' in available_tables and 'اللوائح' in available_tables:
            print("✅ استخدام جداول السجل_العام + اللوائح")
            
            try:
                gender_query = """
                    SELECT النوع, COUNT(*)
                    FROM السجل_العام s
                    JOIN اللوائح l ON s.الرمز = l.الرمز
                """
                if academic_year:
                    gender_query += " WHERE l.السنة_الدراسية = ?"
                    gender_query += " GROUP BY النوع"
                    cursor.execute(gender_query, [academic_year])
                else:
                    gender_query += " GROUP BY النوع"
                    cursor.execute(gender_query)
                
                print(f"🔍 استعلام النوع: {gender_query}")
                gender_stats = cursor.fetchall()
                print(f"📊 نتائج النوع: {gender_stats}")
                
                for gender, count in gender_stats:
                    if gender and gender.strip().lower() in ['ذكر', 'ذ', 'm', 'male']:
                        statistics["males"] += count
                    elif gender and gender.strip().lower() in ['أنثى', 'ا', 'f', 'female']:
                        statistics["females"] += count
                
                print(f"👨 عدد الذكور: {statistics['males']}")
                print(f"👩 عدد الإناث: {statistics['females']}")
                
            except Exception as e:
                print(f"❌ خطأ في استرجاع إحصائيات النوع: {e}")
        
        # في حالة عدم وجود الجداول الأساسية، محاولة استخدام جدول_البيانات
        elif 'جدول_البيانات' in available_tables:
            print("⚠️ استخدام جدول_البيانات كبديل")
            
            # إجمالي الطلاب
            try:
                query = f"SELECT COUNT(*) FROM جدول_البيانات{year_condition}"
                cursor.execute(query, year_params)
                result = cursor.fetchone()
                statistics['students'] = result[0] if result else 0
            except Exception as e:
                print(f"❌ خطأ في حساب عدد الطلاب: {e}")
            
            # عدد الأقسام والمستويات
            try:
                query = f"SELECT COUNT(DISTINCT القسم) FROM جدول_البيانات{year_condition}"
                cursor.execute(query, year_params)
                result = cursor.fetchone()
                statistics['sections'] = result[0] if result else 0
                
                query = f"SELECT COUNT(DISTINCT المستوى) FROM جدول_البيانات{year_condition}"
                cursor.execute(query, year_params)
                result = cursor.fetchone()
                statistics['levels'] = result[0] if result else 0
            except Exception as e:
                print(f"❌ خطأ في حساب الأقسام/المستويات: {e}")
            
            # إحصائيات النوع
            try:
                # محاولة استخدام عمود الجنس
                gender_column = None
                cursor.execute("PRAGMA table_info(جدول_البيانات)")
                columns = [row[1] for row in cursor.fetchall()]
                
                if 'الجنس' in columns:
                    gender_column = 'الجنس'
                elif 'النوع' in columns:
                    gender_column = 'النوع'
                
                if gender_column:
                    for gender_value in ['ذكر', 'أنثى']:
                        if academic_year:
                            gender_condition = f" WHERE {gender_column} = ? AND السنة_الدراسية = ?"
                            cursor.execute(f"SELECT COUNT(*) FROM جدول_البيانات{gender_condition}", [gender_value, academic_year])
                        else:
                            gender_condition = f" WHERE {gender_column} = ?"
                            cursor.execute(f"SELECT COUNT(*) FROM جدول_البيانات{gender_condition}", [gender_value])
                        
                        result = cursor.fetchone()
                        count = result[0] if result else 0
                        
                        if gender_value == 'ذكر':
                            statistics['males'] = count
                        else:
                            statistics['females'] = count
            except Exception as e:
                print(f"❌ خطأ في حساب إحصائيات النوع: {e}")
        
        else:
            print("❌ لم يتم العثور على الجداول المطلوبة")
        
        conn.close()
        
        print(f"✅ الإحصائيات النهائية: {statistics}")
        
        return jsonify({
            'status': 'success',
            'data': statistics,
            'academic_year': academic_year,
            'message': 'تم جلب الإحصائيات بنجاح'
        })
        
    except Exception as e:
        print(f"❌ خطأ عام في الإحصائيات: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'status': 'error',
            'message': f'خطأ في الحصول على الإحصائيات: {str(e)}'
        }), 500

@app.route('/api/statistics/by-level', methods=['GET'])
def get_statistics_by_level():
    """الحصول على الإحصائيات حسب المستوى"""
    try:
        academic_year = request.args.get('academic_year', '')
        
        conn = get_db_connection()
        if not conn:
            return jsonify({'status': 'error', 'message': 'فشل في الاتصال بقاعدة البيانات'}), 500
        
        cursor = conn.cursor()
        
        # تحديد شرط السنة الدراسية
        year_condition = f"WHERE السنة_الدراسية = '{academic_year}'" if academic_year else ""
        
        # الحصول على الإحصائيات حسب المستوى
        query = f"""
        SELECT 
            المستوى,
            COUNT(*) as total_students,
            SUM(CASE WHEN الجنس = 'ذكر' THEN 1 ELSE 0 END) as males,
            SUM(CASE WHEN الجنس = 'أنثى' THEN 1 ELSE 0 END) as females
        FROM جدول_البيانات 
        {year_condition}
        GROUP BY المستوى
        ORDER BY المستوى
        """
        
        cursor.execute(query)
        results = cursor.fetchall()
        
        statistics_by_level = []
        for row in results:
            statistics_by_level.append({
                'level': row[0],
                'total_students': row[1],
                'males': row[2],
                'females': row[3]
            })
        
        conn.close()
        
        return jsonify({
            'status': 'success',
            'data': statistics_by_level
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'خطأ في الحصول على إحصائيات المستويات: {str(e)}'
        }), 500

@app.route('/api/statistics/age-groups', methods=['POST'])
def get_age_group_statistics():
    """حساب إحصائيات الأعمار حسب تاريخ مرجعي"""
    try:
        data = request.get_json()
        reference_date = data.get('reference_date')
        academic_year = data.get('academic_year', '')
        
        if not reference_date:
            return jsonify({
                'status': 'error',
                'message': 'يجب تحديد تاريخ المرجع'
            }), 400
        
        conn = get_db_connection()
        if not conn:
            return jsonify({'status': 'error', 'message': 'فشل في الاتصال بقاعدة البيانات'}), 500
        
        cursor = conn.cursor()
        
        # تحديد شرط السنة الدراسية
        year_condition = f"AND السنة_الدراسية = '{academic_year}'" if academic_year else ""
        
        # الحصول على تواريخ الميلاد وحساب الأعمار
        query = f"""
        SELECT تاريخ_الميلاد, الجنس
        FROM جدول_البيانات 
        WHERE تاريخ_الميلاد IS NOT NULL AND تاريخ_الميلاد != ''
        {year_condition}
        """
        
        cursor.execute(query)
        results = cursor.fetchall()
        
        age_groups = {
            'أقل من 15': {'total': 0, 'males': 0, 'females': 0},
            '15-16': {'total': 0, 'males': 0, 'females': 0},
            '17-18': {'total': 0, 'males': 0, 'females': 0},
            'أكثر من 18': {'total': 0, 'males': 0, 'females': 0}
        }
        
        # معالجة البيانات وحساب الأعمار
        from datetime import datetime
        reference_datetime = datetime.strptime(reference_date, '%Y-%m-%d')
        
        for row in results:
            birth_date_str = row[0]
            gender = row[1]
            
            try:
                # محاولة تحليل تاريخ الميلاد بصيغ مختلفة
                birth_date = None
                for date_format in ['%Y-%m-%d', '%d/%m/%Y', '%m/%d/%Y']:
                    try:
                        birth_date = datetime.strptime(birth_date_str, date_format)
                        break
                    except ValueError:
                        continue
                
                if birth_date:
                    # حساب العمر
                    age = reference_datetime.year - birth_date.year
                    if reference_datetime.month < birth_date.month or \
                       (reference_datetime.month == birth_date.month and reference_datetime.day < birth_date.day):
                        age -= 1
                    
                    # تصنيف العمر
                    if age < 15:
                        group = 'أقل من 15'
                    elif 15 <= age <= 16:
                        group = '15-16'
                    elif 17 <= age <= 18:
                        group = '17-18'
                    else:
                        group = 'أكثر من 18'
                    
                    # إضافة إلى الإحصائيات
                    age_groups[group]['total'] += 1
                    if gender == 'ذكر':
                        age_groups[group]['males'] += 1
                    elif gender == 'أنثى':
                        age_groups[group]['females'] += 1
                        
            except Exception as e:
                print(f"خطأ في معالجة تاريخ الميلاد {birth_date_str}: {e}")
                continue
        
        conn.close()
        
        return jsonify({
            'status': 'success',
            'data': age_groups,
            'reference_date': reference_date
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'خطأ في حساب إحصائيات الأعمار: {str(e)}'
        }), 500

# ===================================
# وظائف استيراد متخصصة إضافية
# ===================================

@app.route('/api/import/secret-codes', methods=['POST'])
def import_secret_codes():
    """استيراد الرموز السرية من Excel إلى قاعدة البيانات"""
    try:
        if 'files' not in request.files:
            return jsonify({'status': 'error', 'message': 'لم يتم رفع أي ملف'}), 400
        
        files = request.files.getlist('files')
        if not files or len(files) == 0:
            return jsonify({'status': 'error', 'message': 'لم يتم اختيار ملفات'}), 400
        
        messages = []
        messages.append({'message': f'🔄 بدء عملية تحليل {len(files)} ملف...', 'type': 'progress', 'progress': 10})
        
        # إنشاء نسخة احتياطية قبل الاستيراد
        backup_result = create_backup(f"before_secret_import_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db")
        messages.append({'message': f'✅ تم إنشاء نسخة احتياطية', 'type': 'success', 'progress': 20})
        
        conn = get_db_connection()
        if not conn:
            return jsonify({'status': 'error', 'message': 'فشل في الاتصال بقاعدة البيانات'}), 500
        
        # إنشاء جدول الرموز السرية إذا لم يكن موجوداً
        cursor = conn.cursor()
        cursor.execute('''CREATE TABLE IF NOT EXISTS الرموز_السرية (
            رقم_التسجيل TEXT PRIMARY KEY,
            الرمز_السري TEXT,
            تاريخ_الإنشاء TEXT DEFAULT CURRENT_TIMESTAMP
        )''')
        
        # حذف البيانات الموجودة
        cursor.execute("DELETE FROM الرموز_السرية")
        messages.append({'message': '🔄 تنظيف البيانات السابقة...', 'type': 'progress', 'progress': 30})
        
        total_imported = 0
        progress_step = 60 / len(files)  # توزيع 60% من التقدم على الملفات
        
        for i, file in enumerate(files):
            try:
                # قراءة الملف
                df = pd.read_excel(file)
                
                # إدراج البيانات الجديدة
                for _, row in df.iterrows():
                    try:
                        cursor.execute("""
                            INSERT OR REPLACE INTO الرموز_السرية 
                            (رقم_التسجيل, الرمز_السري) 
                            VALUES (?, ?)
                        """, (str(row.iloc[0]), str(row.iloc[1])))
                        total_imported += 1
                    except Exception as e:
                        print(f"خطأ في إدراج الصف: {e}")
                        continue
                
                progress = 30 + (i + 1) * progress_step
                messages.append({'message': f'✅ تم معالجة ملف {file.filename}: {len(df)} صف', 'type': 'success', 'progress': int(progress)})
                
            except Exception as e:
                messages.append({'message': f'❌ خطأ في معالجة ملف {file.filename}: {str(e)}', 'type': 'error'})
                continue
        
        conn.commit()
        conn.close()
        
        messages.append({'message': f'✅ تم استيراد {total_imported} رمز سري بنجاح', 'type': 'success', 'progress': 100})
        
        summary = {
            'total_imported': total_imported,
            'files_processed': len(files),
            'backup_created': backup_result['backup_path']
        }
        
        return jsonify({
            'status': 'success',
            'message': f'تم استيراد {total_imported} رمز سري بنجاح',
            'messages': messages,
            'summary': summary,
            'imported_rows': total_imported,
            'backup_created': backup_result['backup_path']
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'خطأ في استيراد الرموز السرية: {str(e)}'
        }), 500

@app.route('/api/import/teachers', methods=['POST'])
def import_teachers():
    """استيراد بيانات الأساتذة من Excel إلى قاعدة البيانات"""
    try:
        if 'file' not in request.files:
            return jsonify({'status': 'error', 'message': 'لم يتم رفع أي ملف'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'status': 'error', 'message': 'لم يتم اختيار ملف'}), 400
        
        messages = []
        messages.append({'message': '🔄 بدء عملية تحليل ملف الأساتذة...', 'type': 'progress', 'progress': 10})
        
        # قراءة الملف
        try:
            df = pd.read_excel(file)
            messages.append({'message': f'✅ تم قراءة الملف بنجاح - {len(df)} صف', 'type': 'success', 'progress': 30})
        except Exception as e:
            return jsonify({
                'status': 'error',
                'message': f'خطأ في قراءة ملف Excel: {str(e)}'
            }), 400
        
        messages.append({'message': '🔄 إنشاء نسخة احتياطية...', 'type': 'progress', 'progress': 40})
        
        # إنشاء نسخة احتياطية قبل الاستيراد
        backup_result = create_backup(f"before_teachers_import_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db")
        messages.append({'message': f'✅ تم إنشاء نسخة احتياطية', 'type': 'success', 'progress': 50})
        
        # استيراد البيانات إلى جدول الأساتذة
        conn = get_db_connection()
        if not conn:
            return jsonify({'status': 'error', 'message': 'فشل في الاتصال بقاعدة البيانات'}), 500
        
        messages.append({'message': '🔄 إعداد جدول الأساتذة...', 'type': 'progress', 'progress': 60})
        
        # حذف الجدول القديم وإنشاء جديد
        cursor = conn.cursor()
        cursor.execute("DROP TABLE IF EXISTS الاساتذة")
        
        # إنشاء الجدول بناءً على أعمدة الملف
        columns = df.columns.tolist()
        columns_sql = ', '.join([f'"{col}" TEXT' for col in columns])
        cursor.execute(f'CREATE TABLE الاساتذة ({columns_sql})')
        
        messages.append({'message': '🔄 استيراد بيانات الأساتذة...', 'type': 'progress', 'progress': 80})
        
        # إدراج البيانات
        for _, row in df.iterrows():
            placeholders = ', '.join(['?' for _ in columns])
            cursor.execute(f'INSERT INTO الاساتذة VALUES ({placeholders})', tuple(row.values))
        
        conn.commit()
        conn.close()
        
        messages.append({'message': f'✅ تم استيراد {len(df)} سجل أستاذ بنجاح', 'type': 'success', 'progress': 100})
        
        summary = {
            'total_imported': len(df),
            'columns': columns,
            'backup_created': backup_result['backup_path']
        }
        
        return jsonify({
            'status': 'success',
            'message': f'تم استيراد {len(df)} سجل أستاذ بنجاح',
            'messages': messages,
            'summary': summary,
            'imported_rows': len(df),
            'backup_created': backup_result['backup_path']
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'خطأ في استيراد بيانات الأساتذة: {str(e)}'
        }), 500

# ===================================
# endpoints عناوين الأوراق والملاحظات
# ===================================

@app.route('/api/documents/all', methods=['GET'])
def get_all_documents():
    """الحصول على جميع عناوين الأوراق والملاحظات"""
    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({'status': 'error', 'message': 'فشل في الاتصال بقاعدة البيانات'}), 500
        
        cursor = conn.cursor()
        
        # تأكد من وجود الجدول وإنشاؤه إذا لم يكن موجوداً
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS "تعديل_المسميات" (
                ID INTEGER PRIMARY KEY AUTOINCREMENT,
                الاسم TEXT NOT NULL,
                العنوان TEXT,
                ملاحظات TEXT
            )
        ''')
        
        # إضافة البيانات الافتراضية إذا كان الجدول فارغاً
        cursor.execute('SELECT COUNT(*) FROM "تعديل_المسميات"')
        count = cursor.fetchone()[0]
        
        if count == 0:
            # إدراج البيانات الافتراضية
            default_data = [
                # الأوراق العامة (1-6)
                (1, 'ورقة 1', 'عنوان افتراضي', 'ملاحظات عامة'),
                (2, 'ورقة 2', 'عنوان افتراضي', 'ملاحظات عامة'),
                (3, 'ورقة 3', 'عنوان افتراضي', 'ملاحظات عامة'),
                (4, 'ورقة 4', 'عنوان افتراضي', 'ملاحظات عامة'),
                (5, 'ورقة 5', 'عنوان افتراضي', 'ملاحظات عامة'),
                (6, 'ورقة 6', 'عنوان افتراضي', 'ملاحظات عامة'),
                # المخالفات (7-16)
                (7, 'مخالفة 1', 'عنوان افتراضي', None),
                (8, 'مخالفة 2', 'عنوان افتراضي', None),
                (9, 'مخالفة 3', 'عنوان افتراضي', None),
                (10, 'مخالفة 4', 'عنوان افتراضي', None),
                (11, 'مخالفة 5', 'عنوان افتراضي', None),
                (12, 'مخالفة 6', 'عنوان افتراضي', None),
                (13, 'مخالفة 7', 'عنوان افتراضي', None),
                (14, 'مخالفة 8', 'عنوان افتراضي', None),
                (15, 'مخالفة 9', 'عنوان افتراضي', None),
                (16, 'مخالفة 10', 'عنوان افتراضي', None),
                # الإجراءات (17-20)
                (17, 'إجراء 1', 'عنوان افتراضي', None),
                (18, 'إجراء 2', 'عنوان افتراضي', None),
                (19, 'إجراء 3', 'عنوان افتراضي', None),
                (20, 'إجراء 4', 'عنوان افتراضي', None)
            ]
            
            for item in default_data:
                cursor.execute('''
                    INSERT OR REPLACE INTO "تعديل_المسميات" 
                    (ID, الاسم, العنوان, ملاحظات) 
                    VALUES (?, ?, ?, ?)
                ''', item)
            
            conn.commit()
            print("تم إنشاء البيانات الافتراضية لجدول المستندات")
        
        # جلب البيانات المصنفة
        # الأوراق العامة (1-6)
        cursor.execute('''
            SELECT ID, الاسم, العنوان, ملاحظات 
            FROM "تعديل_المسميات" 
            WHERE ID BETWEEN 1 AND 6 
            ORDER BY ID
        ''')
        general_docs = []
        for row in cursor.fetchall():
            general_docs.append({
                'ID': row[0],
                'الاسم': row[1],
                'العنوان': row[2],
                'ملاحظات': row[3]
            })
        
        # المخالفات (7-16)
        cursor.execute('''
            SELECT ID, الاسم, العنوان 
            FROM "تعديل_المسميات" 
            WHERE ID BETWEEN 7 AND 16 
            ORDER BY ID
        ''')
        violations = []
        for row in cursor.fetchall():
            violations.append({
                'ID': row[0],
                'الاسم': row[1],
                'العنوان': row[2]
            })
        
        # الإجراءات (17-20)
        cursor.execute('''
            SELECT ID, الاسم, العنوان 
            FROM "تعديل_المسميات" 
            WHERE ID BETWEEN 17 AND 20 
            ORDER BY ID
        ''')
        procedures = []
        for row in cursor.fetchall():
            procedures.append({
                'ID': row[0],
                'الاسم': row[1],
                'العنوان': row[2]
            })
        
        conn.close()
        
        return jsonify({
            'status': 'success',
            'data': {
                'general': general_docs,
                'violations': violations,
                'procedures': procedures
            }
        })
        
    except Exception as e:
        print(f"خطأ في جلب بيانات المستندات: {e}")
        return jsonify({
            'status': 'error',
            'message': f'خطأ في جلب البيانات: {str(e)}'
        }), 500

@app.route('/api/documents/update', methods=['POST'])
def update_document():
    """تحديث بيانات مستند معين"""
    try:
        data = request.get_json()
        
        if not data or 'id' not in data or 'column' not in data:
            return jsonify({'status': 'error', 'message': 'بيانات غير كاملة'}), 400
        
        document_id = data['id']
        column_name = data['column']
        new_value = data.get('value', '')
        
        # التحقق من صحة اسم العمود
        allowed_columns = ['العنوان', 'ملاحظات']
        if column_name not in allowed_columns:
            return jsonify({'status': 'error', 'message': 'اسم العمود غير صحيح'}), 400
        
        conn = get_db_connection()
        if not conn:
            return jsonify({'status': 'error', 'message': 'فشل في الاتصال بقاعدة البيانات'}), 500
        
        cursor = conn.cursor()
        
        # تحديث البيانات
        query = f'UPDATE "تعديل_المسميات" SET "{column_name}" = ? WHERE ID = ?'
        cursor.execute(query, (new_value, document_id))
        
        if cursor.rowcount == 0:
            conn.close()
            return jsonify({'status': 'error', 'message': 'لم يتم العثور على المستند'}), 404
        
        conn.commit()
        conn.close()
        
        print(f"تم تحديث المستند {document_id}: {column_name} = {new_value}")
        
        return jsonify({
            'status': 'success',
            'message': 'تم تحديث البيانات بنجاح'
        })
        
    except Exception as e:
        print(f"خطأ في تحديث المستند: {e}")
        return jsonify({
            'status': 'error',
            'message': f'خطأ في تحديث البيانات: {str(e)}'
        }), 500

# ===================================
# تشغيل الخادم
# ===================================

if __name__ == '__main__':
    # إنشاء مجلد النسخ الاحتياطية
    if not os.path.exists("backups"):
        os.makedirs("backups")
    
    print("=" * 50)
    print("🚀 الخادم الموحد لمنظومة إدارة التعليم")
    print("=" * 50)
    print(f"📍 قاعدة البيانات: {DB_PATH}")
    print(f"📊 عدد الجداول المراقبة: {len(TABLES_TO_CLEAR)}")
    print("=" * 50)
    
    app.run(host='0.0.0.0', port=5000, debug=True)
