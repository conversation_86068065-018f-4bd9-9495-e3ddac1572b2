<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>بيانات المؤسسة التعليمية</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: '<PERSON><PERSON>ri', 'Tahoma', sans-serif;
            font-size: 13px;
            font-weight: bold;
            background-color: #f5f5f5;
            color: #333;
            direction: rtl;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #1976d2, #1565C0);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }

        .back-button {
            position: absolute;
            top: 15px;
            right: 20px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 5px;
            padding: 8px 15px;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .back-button:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .content {
            padding: 20px;
        }

        /* منطقة الشعار */
        .logo-section {
            text-align: center;
            margin-bottom: 20px;
        }

        .logo-container {
            width: 400px;
            height: 150px;
            border: 2px solid #1976d2;
            background-color: #ffffff;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            position: relative;
            overflow: hidden;
        }

        .logo-image {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        .logo-placeholder {
            color: #666;
            font-size: 14px;
        }

        .logo-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 8px 15px;
            border: none;
            border-radius: 8px;
            font-family: 'Calibri';
            font-size: 13px;
            font-weight: bold;
            min-width: 130px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-upload {
            background-color: #28a745;
            color: white;
        }

        .btn-upload:hover {
            background-color: #218838;
        }

        .btn-save {
            background-color: #007bff;
            color: white;
        }

        .btn-save:hover {
            background-color: #0056b3;
        }

        .btn-refresh {
            background-color: #17a2b8;
            color: white;
        }

        .btn-refresh:hover {
            background-color: #138496;
        }

        .btn-info {
            background-color: #6c757d;
            color: white;
        }

        .btn-info:hover {
            background-color: #5a6268;
        }

        /* رمز المؤسسة */
        .institution-code {
            background-color: #f8f9fa;
            color: #2c3e50;
            border: 1px solid #bdc3c7;
            border-radius: 5px;
            padding: 8px;
            margin: 10px auto;
            text-align: center;
            font-weight: bold;
            max-width: 400px;
        }

        /* نموذج البيانات */
        .form-section {
            background: white;
            border: 2px solid #1976d2;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }

        .form-title {
            color: #1976d2;
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 20px;
            text-align: center;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            align-items: start;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: bold;
            margin-bottom: 5px;
            color: #2c3e50;
            font-size: 12px;
        }

        .form-group input,
        .form-group select {
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-family: 'Calibri';
            font-size: 12px;
            background-color: #ffffff;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #1976d2;
            box-shadow: 0 0 5px rgba(25, 118, 210, 0.3);
        }

        /* رسائل التنبيه */
        .alert {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            text-align: center;
        }

        .alert-success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .alert-warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }

        .alert-error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .alert-info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }

        /* النوافذ المنبثقة */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 10% auto;
            padding: 20px;
            border-radius: 10px;
            width: 80%;
            max-width: 600px;
            position: relative;
            direction: rtl;
        }

        .modal-header {
            background: linear-gradient(135deg, #1976d2, #1565C0);
            color: white;
            padding: 15px;
            border-radius: 8px 8px 0 0;
            margin: -20px -20px 20px -20px;
            text-align: center;
            font-weight: bold;
        }

        .close {
            position: absolute;
            top: 10px;
            left: 15px;
            color: white;
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            opacity: 0.7;
        }

        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }

        .info-table th,
        .info-table td {
            border: 1px solid #dee2e6;
            padding: 10px;
            text-align: right;
        }

        .info-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }

        .info-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        /* استجابة للشاشات الصغيرة */
        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .logo-container {
                width: 100%;
                max-width: 350px;
            }
            
            .logo-buttons {
                flex-direction: column;
                align-items: center;
            }
        }

        /* تحسينات إضافية */
        .loading {
            display: none;
            text-align: center;
            color: #1976d2;
            font-weight: bold;
            margin: 20px 0;
        }

        .hidden {
            display: none;
        }

        .text-center {
            text-align: center;
        }

        .mt-20 {
            margin-top: 20px;
        }

        .mb-20 {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <a href="main_window0.html" class="back-button">← العودة للقائمة الرئيسية</a>
            <h1>🏢 بيانات المؤسسة التعليمية</h1>
            <p>إدارة المعلومات الأساسية والشعار الرسمي للمؤسسة</p>
        </div>

        <div class="content">
            <!-- منطقة الشعار -->
            <div class="logo-section">
                <div class="logo-container" id="logoContainer">
                    <div class="logo-placeholder" id="logoPlaceholder">لا يوجد شعار</div>
                    <img class="logo-image hidden" id="logoImage" alt="شعار المؤسسة">
                </div>
                
                <div class="logo-buttons">
                    <button class="btn btn-upload" onclick="uploadLogo()">📷 تحميل الشعار</button>
                    <button class="btn btn-save" onclick="saveInstitutionData()">💾 حفظ البيانات</button>
                    <button class="btn btn-refresh" onclick="refreshData()">🔄 تحديث البيانات</button>
                    <button class="btn btn-info" onclick="showInstitutionInfo()">ℹ️ معلومات المؤسسة</button>
                </div>

                <!-- رمز المؤسسة -->
                <div class="institution-code" id="institutionCode">
                    رمز المؤسسة: غير متوفر
                </div>
            </div>

            <!-- منطقة الرسائل -->
            <div id="alertContainer"></div>

            <!-- نموذج البيانات -->
            <div class="form-section">
                <div class="form-title">بيانات المؤسسة</div>
                
                <form id="institutionForm">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="academy">الأكاديمية:</label>
                            <input type="text" id="academy" name="academy">
                        </div>

                        <div class="form-group">
                            <label for="directorate">المديرية:</label>
                            <input type="text" id="directorate" name="directorate">
                        </div>

                        <div class="form-group">
                            <label for="community">الجماعة:</label>
                            <input type="text" id="community" name="community">
                        </div>

                        <div class="form-group">
                            <label for="institution">المؤسسة:</label>
                            <input type="text" id="institution" name="institution">
                        </div>

                        <div class="form-group">
                            <label for="academicYear">السنة الدراسية:</label>
                            <select id="academicYear" name="academicYear">
                                <option value="">اختر السنة</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="city">البلدة:</label>
                            <input type="text" id="city" name="city" required>
                        </div>

                        <div class="form-group">
                            <label for="principal">المدير:</label>
                            <select id="principal" name="principal">
                                <option value="من مدير">من مدير</option>
                                <option value="من مديرة">من مديرة</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="generalGuard">الحراسة العامة:</label>
                            <select id="generalGuard" name="generalGuard">
                                <option value="من حارس عام">من حارس عام</option>
                                <option value="من حارسة عامة">من حارسة عامة</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="educationLevel">السلك:</label>
                            <select id="educationLevel" name="educationLevel">
                                <option value="التعليم الابتدائي">التعليم الابتدائي</option>
                                <option value="الثانوي الإعدادي">الثانوي الإعدادي</option>
                                <option value="الثانوي التأهيلي">الثانوي التأهيلي</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="guardNumber">رقم الحراسة:</label>
                            <select id="guardNumber" name="guardNumber">
                                <option value="حراسة رقم 1">حراسة رقم 1</option>
                                <option value="حراسة رقم 2">حراسة رقم 2</option>
                                <option value="حراسة رقم 3">حراسة رقم 3</option>
                                <option value="حراسة رقم 4">حراسة رقم 4</option>
                                <option value="حراسة رقم 5">حراسة رقم 5</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="registrationNumber">رقم التسجيل:</label>
                            <input type="text" id="registrationNumber" name="registrationNumber">
                        </div>

                        <div class="form-group">
                            <label for="semester">الأسدس:</label>
                            <select id="semester" name="semester">
                                <option value="الأول">الأول</option>
                                <option value="الثاني">الثاني</option>
                            </select>
                        </div>
                    </div>
                </form>
            </div>

            <div class="loading" id="loadingIndicator">
                🔄 جاري المعالجة...
            </div>
        </div>
    </div>

    <!-- نافذة منبثقة لعرض معلومات المؤسسة -->
    <div id="infoModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <span class="close" onclick="closeModal()">&times;</span>
                <h2>معلومات المؤسسة</h2>
            </div>
            <div id="modalBody">
                <!-- سيتم ملؤها ديناميكياً -->
            </div>
        </div>
    </div>

    <!-- حقل إدخال الملف المخفي -->
    <input type="file" id="logoFileInput" accept="image/*" style="display: none;">

    <script>
        // الإعدادات العامة
        const API_BASE_URL = 'http://localhost:5000';
        let currentLogoPath = null;

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadAcademicYears();
            loadInstitutionData();
            loadLogo();
            updateInstitutionCode();
        });

        // تحميل السنوات الدراسية
        async function loadAcademicYears() {
            try {
                const response = await fetch(`${API_BASE_URL}/api/database/query`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: 'SELECT DISTINCT السنة_الدراسية FROM البنية_التربوية ORDER BY السنة_الدراسية DESC'
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    const yearSelect = document.getElementById('academicYear');
                    
                    // مسح الخيارات الحالية
                    yearSelect.innerHTML = '<option value="">اختر السنة</option>';
                    
                    // إضافة السنوات من قاعدة البيانات
                    if (result.status === 'success' && result.data) {
                        result.data.forEach(row => {
                            if (row.السنة_الدراسية) {
                                const option = document.createElement('option');
                                option.value = row.السنة_الدراسية;
                                option.textContent = row.السنة_الدراسية;
                                yearSelect.appendChild(option);
                            }
                        });
                    }
                }
            } catch (error) {
                console.error('خطأ في تحميل السنوات الدراسية:', error);
            }
        }

        // تحميل بيانات المؤسسة
        async function loadInstitutionData() {
            try {
                const response = await fetch(`${API_BASE_URL}/api/database/query`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: 'SELECT * FROM بيانات_المؤسسة LIMIT 1'
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    if (result.status === 'success' && result.data && result.data.length > 0) {
                        const data = result.data[0];
                        
                        // ملء الحقول بالبيانات
                        document.getElementById('academy').value = data.الأكاديمية || '';
                        document.getElementById('directorate').value = data.المديرية || '';
                        document.getElementById('community').value = data.الجماعة || '';
                        document.getElementById('institution').value = data.المؤسسة || '';
                        document.getElementById('academicYear').value = data.السنة_الدراسية || '';
                        document.getElementById('city').value = data.البلدة || '';
                        document.getElementById('principal').value = data.المدير || 'من مدير';
                        document.getElementById('generalGuard').value = data.الحارس_العام || 'من حارس عام';
                        document.getElementById('educationLevel').value = data.السلك || 'التعليم الابتدائي';
                        document.getElementById('guardNumber').value = data.رقم_الحراسة || 'حراسة رقم 1';
                        document.getElementById('registrationNumber').value = data.رقم_التسجيل || '';
                        document.getElementById('semester').value = data.الأسدس || 'الأول';
                        
                        // حفظ مسار الشعار
                        currentLogoPath = data.ImagePath1 || null;
                    }
                }
            } catch (error) {
                console.error('خطأ في تحميل بيانات المؤسسة:', error);
                showAlert('خطأ في تحميل البيانات', 'error');
            }
        }

        // تحميل الشعار
        function loadLogo() {
            if (currentLogoPath) {
                const logoImage = document.getElementById('logoImage');
                const logoPlaceholder = document.getElementById('logoPlaceholder');
                
                logoImage.src = currentLogoPath;
                logoImage.classList.remove('hidden');
                logoPlaceholder.classList.add('hidden');
            }
        }

        // تحديث رمز المؤسسة
        function updateInstitutionCode() {
            const academy = document.getElementById('academy').value || 'غير متوفر';
            const directorate = document.getElementById('directorate').value || 'غير متوفر';
            const institution = document.getElementById('institution').value || 'غير متوفر';
            
            // توليد رمز فريد بناءً على البيانات
            const combinedText = `${academy}-${directorate}-${institution}`;
            let hashValue = 0;
            
            for (let i = 0; i < combinedText.length; i++) {
                hashValue = (hashValue * 31 + combinedText.charCodeAt(i)) & 0xFFFFFFFF;
            }
            
            let numericCode = hashValue % 10000000000;
            if (numericCode < 1000000000) {
                numericCode += 1000000000;
            }
            
            const institutionCodeElement = document.getElementById('institutionCode');
            institutionCodeElement.textContent = `رمز المؤسسة: ${numericCode.toString().padStart(10, '0')}`;
            institutionCodeElement.title = `الأكاديمية: ${academy}\nالمديرية: ${directorate}\nالمؤسسة: ${institution}`;
        }

        // تحميل الشعار
        function uploadLogo() {
            document.getElementById('logoFileInput').click();
        }

        // معالجة اختيار الملف
        document.getElementById('logoFileInput').addEventListener('change', function(event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const logoImage = document.getElementById('logoImage');
                    const logoPlaceholder = document.getElementById('logoPlaceholder');
                    
                    logoImage.src = e.target.result;
                    logoImage.classList.remove('hidden');
                    logoPlaceholder.classList.add('hidden');
                    
                    // حفظ الشعار في localStorage مؤقتاً
                    localStorage.setItem('institutionLogo', e.target.result);
                    currentLogoPath = e.target.result;
                    
                    showAlert('تم تحميل الشعار بنجاح', 'success');
                };
                reader.readAsDataURL(file);
            }
        });

        // حفظ بيانات المؤسسة
        async function saveInstitutionData() {
            // التحقق من صحة البيانات
            const city = document.getElementById('city').value.trim();
            if (!city) {
                showAlert('يجب إدخال اسم البلدة قبل حفظ البيانات', 'warning');
                document.getElementById('city').focus();
                return;
            }

            // التحقق من وجود الشعار
            if (!currentLogoPath) {
                showAlert('يجب تحميل شعار المؤسسة قبل حفظ البيانات', 'warning');
                return;
            }

            // التحقق من كود التفعيل
            if (!verifyActivationCode()) {
                showAlert('كود التفعيل غير صحيح. الرجاء التأكد من رقم التسجيل المدخل.', 'error');
                return;
            }

            try {
                showLoading(true);

                // جمع البيانات من النموذج
                const formData = {
                    الأكاديمية: document.getElementById('academy').value,
                    المديرية: document.getElementById('directorate').value,
                    الجماعة: document.getElementById('community').value,
                    المؤسسة: document.getElementById('institution').value,
                    السنة_الدراسية: document.getElementById('academicYear').value,
                    البلدة: document.getElementById('city').value,
                    المدير: document.getElementById('principal').value,
                    الحارس_العام: document.getElementById('generalGuard').value,
                    السلك: document.getElementById('educationLevel').value,
                    رقم_الحراسة: document.getElementById('guardNumber').value,
                    رقم_التسجيل: document.getElementById('registrationNumber').value,
                    الأسدس: document.getElementById('semester').value,
                    ImagePath1: currentLogoPath
                };

                // التحقق من وجود بيانات في الجدول
                const checkResponse = await fetch(`${API_BASE_URL}/api/database/query`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: 'SELECT COUNT(*) as count FROM بيانات_المؤسسة'
                    })
                });

                const checkResult = await checkResponse.json();
                const recordExists = checkResult.status === 'success' && 
                                  checkResult.data && 
                                  checkResult.data[0] && 
                                  checkResult.data[0].count > 0;

                let query;
                let params;

                if (recordExists) {
                    // تحديث البيانات الموجودة
                    query = `UPDATE بيانات_المؤسسة SET 
                        الأكاديمية = ?, المديرية = ?, الجماعة = ?, المؤسسة = ?,
                        السنة_الدراسية = ?, البلدة = ?, المدير = ?, الحارس_العام = ?,
                        السلك = ?, رقم_الحراسة = ?, رقم_التسجيل = ?, الأسدس = ?, ImagePath1 = ?
                        WHERE rowid = 1`;
                    params = Object.values(formData);
                } else {
                    // إدراج بيانات جديدة
                    query = `INSERT INTO بيانات_المؤسسة (
                        الأكاديمية, المديرية, الجماعة, المؤسسة,
                        السنة_الدراسية, البلدة, المدير, الحارس_العام,
                        السلك, رقم_الحراسة, رقم_التسجيل, الأسدس, ImagePath1
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;
                    params = Object.values(formData);
                }

                const response = await fetch(`${API_BASE_URL}/api/database/query`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: query,
                        params: params
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    if (result.status === 'success') {
                        showAlert('تم حفظ بيانات المؤسسة بنجاح', 'success');
                        updateInstitutionCode();
                    } else {
                        showAlert('فشل في حفظ البيانات: ' + result.message, 'error');
                    }
                } else {
                    showAlert('خطأ في الاتصال بالخادم', 'error');
                }
            } catch (error) {
                console.error('خطأ في حفظ البيانات:', error);
                showAlert('حدث خطأ أثناء حفظ البيانات', 'error');
            } finally {
                showLoading(false);
            }
        }

        // التحقق من كود التفعيل
        function verifyActivationCode() {
            try {
                const institutionCodeText = document.getElementById('institutionCode').textContent;
                const schoolCodeParts = institutionCodeText.split(': ');
                if (schoolCodeParts.length !== 2) return false;

                const schoolCode = schoolCodeParts[1].trim();
                if (!/^\d+$/.test(schoolCode)) return false;

                const registrationNumber = document.getElementById('registrationNumber').value.trim();
                if (!/^\d+$/.test(registrationNumber)) return false;

                const schoolCodeInt = parseInt(schoolCode);
                const firstThreeDigits = parseInt(schoolCode.substring(0, 3));
                const expectedRegistration = (schoolCodeInt * 98) + (firstThreeDigits * 71);

                return parseInt(registrationNumber) === expectedRegistration;
            } catch (error) {
                console.error('خطأ في التحقق من كود التفعيل:', error);
                return false;
            }
        }

        // تحديث البيانات
        async function refreshData() {
            try {
                showLoading(true);
                await loadAcademicYears();
                await loadInstitutionData();
                loadLogo();
                updateInstitutionCode();
                showAlert('تم تحديث البيانات بنجاح', 'success');
            } catch (error) {
                console.error('خطأ في تحديث البيانات:', error);
                showAlert('حدث خطأ أثناء تحديث البيانات', 'error');
            } finally {
                showLoading(false);
            }
        }

        // عرض معلومات المؤسسة
        async function showInstitutionInfo() {
            try {
                const response = await fetch(`${API_BASE_URL}/api/database/query`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: 'SELECT الأكاديمية, المديرية, المؤسسة, رقم_التسجيل FROM بيانات_المؤسسة WHERE rowid=1'
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    if (result.status === 'success' && result.data && result.data.length > 0) {
                        const data = result.data[0];
                        const institutionCodeText = document.getElementById('institutionCode').textContent;
                        const institutionCode = institutionCodeText.split(': ')[1] || 'غير متوفر';

                        const modalBody = document.getElementById('modalBody');
                        modalBody.innerHTML = `
                            <table class="info-table">
                                <tr>
                                    <th>البيان</th>
                                    <th>القيمة</th>
                                </tr>
                                <tr>
                                    <td>الأكاديمية</td>
                                    <td>${data.الأكاديمية || 'غير متوفر'}</td>
                                </tr>
                                <tr>
                                    <td>المديرية</td>
                                    <td>${data.المديرية || 'غير متوفر'}</td>
                                </tr>
                                <tr>
                                    <td>المؤسسة</td>
                                    <td>${data.المؤسسة || 'غير متوفر'}</td>
                                </tr>
                                <tr>
                                    <td>رمز المؤسسة</td>
                                    <td>${institutionCode}</td>
                                </tr>
                                <tr>
                                    <td>رقم التسجيل</td>
                                    <td>${data.رقم_التسجيل || 'غير متوفر'}</td>
                                </tr>
                            </table>
                            <p style="text-align: center; margin-top: 20px; font-style: italic; color: #6c757d;">
                                لتغيير هذه المعلومات، استخدم نموذج بيانات المؤسسة
                            </p>
                        `;

                        document.getElementById('infoModal').style.display = 'block';
                    } else {
                        showAlert('لم يتم العثور على بيانات المؤسسة', 'warning');
                    }
                } else {
                    showAlert('خطأ في الاتصال بالخادم', 'error');
                }
            } catch (error) {
                console.error('خطأ في عرض معلومات المؤسسة:', error);
                showAlert('حدث خطأ أثناء استرجاع البيانات', 'error');
            }
        }

        // إغلاق النافذة المنبثقة
        function closeModal() {
            document.getElementById('infoModal').style.display = 'none';
        }

        // إغلاق النافذة عند النقر خارجها
        window.onclick = function(event) {
            const modal = document.getElementById('infoModal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        }

        // عرض الرسائل
        function showAlert(message, type) {
            const alertContainer = document.getElementById('alertContainer');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;
            
            alertContainer.innerHTML = '';
            alertContainer.appendChild(alert);
            
            // إخفاء الرسالة بعد 5 ثوانِ
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 5000);
        }

        // عرض مؤشر التحميل
        function showLoading(show) {
            const loadingIndicator = document.getElementById('loadingIndicator');
            loadingIndicator.style.display = show ? 'block' : 'none';
        }

        // تحديث رمز المؤسسة عند تغيير البيانات
        document.getElementById('academy').addEventListener('input', updateInstitutionCode);
        document.getElementById('directorate').addEventListener('input', updateInstitutionCode);
        document.getElementById('institution').addEventListener('input', updateInstitutionCode);
    </script>
</body>
</html>
