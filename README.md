# نظام إدارة بيانات الطلاب

نظام إدارة بيانات الطلاب المحول من PyQt5 إلى واجهة ويب باللغة العربية، مع ربط بقاعدة بيانات SQLite.

## المتطلبات

- Python 3.7+
- متصفح ويب حديث

## التثبيت والتشغيل

### 1. تثبيت المتطلبات

```cmd
pip install -r requirements.txt
```

### 2. تشغيل الخادم

```cmd
python server.py
```

### 3. فتح الواجهة

افتح متصفح الويب واذهب إلى:
```
http://localhost:5000/api/test
```
للتأكد من أن الخادم يعمل، ثم افتح:
```
index.html
```
في المتصفح

## ملفات النظام

- `index.html` - الواجهة الرئيسية
- `style.css` - تنسيق الصفحة
- `student-management.js` - منطق التطبيق
- `server.py` - خادم API (Flask)
- `data.db` - قاعدة البيانات (ستُنشأ تلقائياً)

## الوظائف المتاحة

### واجهة الويب
- عرض قائمة الطلاب في جدول
- البحث بالاسم
- تصفية حسب المجموعة والقسم والشهر
- عرض الإحصائيات
- واجهة عربية متجاوبة

### API المتاحة

- `GET /api/test` - اختبار الاتصال
- `GET /api/students` - جلب جميع الطلاب
- `GET /api/groups` - جلب المجموعات
- `GET /api/sections` - جلب الأقسام  
- `GET /api/filter?search=&group=&section=&month=` - تصفية الطلاب
- `GET /api/student/<id>` - جلب بيانات طالب محدد
- `GET /api/statistics` - جلب الإحصائيات

## بنية قاعدة البيانات

جدول `جدول_البيانات`:
- `id` - الرقم التسلسلي
- `اسم_الطالب` - اسم الطالب
- `المجموعة` - المجموعة
- `القسم` - القسم
- `الشهر` - الشهر
- `حالة_الدفع` - حالة الدفع (مدفوع/غير مدفوع)
- `تاريخ_التسجيل` - تاريخ التسجيل
- `رقم_الهاتف` - رقم الهاتف
- `العنوان` - العنوان
- `ملاحظات` - ملاحظات

## البيانات التجريبية

عند التشغيل لأول مرة، سيتم إنشاء قاعدة البيانات وإضافة 10 طلاب تجريبيين للاختبار.

## استكشاف الأخطاء

### إذا لم يعمل الخادم:
1. تأكد من تثبيت Python وFlask
2. تحقق من رسائل الخطأ في الطرفية
3. تأكد من أن المنفذ 5000 غير مستخدم

### إذا لم تظهر البيانات:
1. تأكد من أن الخادم يعمل
2. افتح أدوات المطور في المتصفح وتحقق من تبويب Network
3. تأكد من أن قاعدة البيانات تحتوي على بيانات

## تطوير إضافي

يمكن إضافة المزيد من الوظائف مثل:
- إضافة طلاب جدد
- تحديث بيانات الطلاب
- حذف الطلاب
- تصدير البيانات
- طباعة التقارير
