<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إعدادات الخطوط - مع النمط والسُمك</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            direction: rtl;
            text-align: right;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
        }
        .font-test {
            margin: 10px 0;
        }
        .calibri { font-family: Calibri, Arial, sans-serif; }
        .arial { font-family: Arial, sans-serif; }
        .tahoma { font-family: Tahoma, sans-serif; }
        .verdana { font-family: Verdana, sans-serif; }
        .cairo { font-family: Cairo, Arial, sans-serif; }
        .amiri { font-family: <PERSON><PERSON>, seri<PERSON>; }
        .noto { font-family: '<PERSON><PERSON>', Arial, sans-serif; }
    </style>
</head>
<body>
    <h1>🔤 اختبار الخطوط العربية</h1>
    
    <div class="test-section">
        <h2>أنواع الخطوط المختلفة:</h2>
        
        <div class="font-test calibri">
            <strong>Calibri:</strong> نظام إدارة بيانات الطلاب - عنوان رئيسي
        </div>
        
        <div class="font-test arial">
            <strong>Arial:</strong> نظام إدارة بيانات الطلاب - عنوان رئيسي
        </div>
        
        <div class="font-test tahoma">
            <strong>Tahoma:</strong> نظام إدارة بيانات الطلاب - عنوان رئيسي
        </div>
        
        <div class="font-test verdana">
            <strong>Verdana:</strong> نظام إدارة بيانات الطلاب - عنوان رئيسي
        </div>
        
        <div class="font-test cairo">
            <strong>Cairo:</strong> نظام إدارة بيانات الطلاب - عنوان رئيسي
        </div>
        
        <div class="font-test amiri">
            <strong>Amiri:</strong> نظام إدارة بيانات الطلاب - عنوان رئيسي
        </div>
        
        <div class="font-test noto">
            <strong>Noto Sans Arabic:</strong> نظام إدارة بيانات الطلاب - عنوان رئيسي
        </div>
    </div>
    
    <div class="test-section">
        <h2>أحجام الخطوط المختلفة:</h2>
        
        <div style="font-size: 12px;">حجم 12px: هذا نص تجريبي باللغة العربية</div>
        <div style="font-size: 14px;">حجم 14px: هذا نص تجريبي باللغة العربية</div>
        <div style="font-size: 16px;">حجم 16px: هذا نص تجريبي باللغة العربية</div>
        <div style="font-size: 18px;">حجم 18px: هذا نص تجريبي باللغة العربية</div>
        <div style="font-size: 20px;">حجم 20px: هذا نص تجريبي باللغة العربية</div>
    </div>
    
    <div class="test-section">
        <h2>العناوين بأحجام مختلفة:</h2>
        
        <h1 style="font-size: 1.8em;">عنوان رئيسي - 1.8em</h1>
        <h1 style="font-size: 2em;">عنوان رئيسي - 2em</h1>
        <h1 style="font-size: 2.2em;">عنوان رئيسي - 2.2em</h1>
        <h1 style="font-size: 2.5em;">عنوان رئيسي - 2.5em</h1>
        
        <h3 style="font-size: 1.2em;">عنوان فرعي - 1.2em</h3>
        <h3 style="font-size: 1.4em;">عنوان فرعي - 1.4em</h3>
        <h3 style="font-size: 1.6em;">عنوان فرعي - 1.6em</h3>
        <h3 style="font-size: 1.8em;">عنوان فرعي - 1.8em</h3>
    </div>
    
    <p style="margin-top: 20px; font-size: 14px; color: #666;">
        💡 هذا الملف لاختبار كيفية ظهور الخطوط المختلفة في المتصفح. 
        يمكن استخدامه للتأكد من وضوح الخطوط قبل تطبيقها في النظام الرئيسي.
    </p>
</body>
</html>
