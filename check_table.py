import sqlite3
import os

try:
    db_path = os.path.join(os.path.dirname(__file__), "data.db")
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # فحص وجود الجدول
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='بيانات_المؤسسة'")
    table_exists = cursor.fetchone()
    
    if table_exists:
        print("✅ جدول 'بيانات_المؤسسة' موجود")
        
        # فحص بنية الجدول
        cursor.execute("PRAGMA table_info('بيانات_المؤسسة')")
        columns = cursor.fetchall()
        
        print("\n📋 بنية جدول 'بيانات_المؤسسة':")
        for col in columns:
            print(f"  - {col[1]} ({col[2]})")
            
        # فحص البيانات الموجودة
        cursor.execute("SELECT COUNT(*) FROM بيانات_المؤسسة")
        count = cursor.fetchone()[0]
        print(f"\n📊 عدد السجلات: {count}")
        
        if count > 0:
            cursor.execute("SELECT * FROM بيانات_المؤسسة LIMIT 1")
            sample = cursor.fetchone()
            print(f"\n📝 مثال على البيانات: {sample}")
    else:
        print("❌ جدول 'بيانات_المؤسسة' غير موجود")
        
        # عرض الجداول الموجودة
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print("\n📋 الجداول الموجودة:")
        for table in tables:
            print(f"  - {table[0]}")

    conn.close()
    
except Exception as e:
    print(f"❌ خطأ: {e}")
