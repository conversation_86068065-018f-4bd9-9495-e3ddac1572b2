<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإحصائيات العامة للمؤسسة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Calibri', 'Tahoma', sans-serif;
            font-size: 13px;
            font-weight: bold;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2196f3, #1976d2);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }

        .back-button {
            position: absolute;
            top: 15px;
            right: 20px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 5px;
            padding: 8px 15px;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .back-button:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .main-content {
            padding: 30px;
        }

        .stats-title {
            font-size: 16px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 20px;
            color: #333;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stats-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            border-bottom: 4px solid;
        }

        .stats-card:hover {
            transform: translateY(-5px);
        }

        .stats-card.students {
            border-bottom-color: #2196f3;
        }

        .stats-card.sections {
            border-bottom-color: #4caf50;
        }

        .stats-card.levels {
            border-bottom-color: #ff9800;
        }

        .stats-card.average {
            border-bottom-color: #9c27b0;
        }

        .stats-card.males {
            border-bottom-color: #3f51b5;
        }

        .stats-card.females {
            border-bottom-color: #e91e63;
        }

        .stats-card.total {
            border-bottom-color: #009688;
        }

        .stats-card.percentage {
            border-bottom-color: #ffc107;
        }

        .card-title {
            font-size: 13px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }

        .card-value {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .students .card-value { color: #2196f3; }
        .sections .card-value { color: #4caf50; }
        .levels .card-value { color: #ff9800; }
        .average .card-value { color: #9c27b0; }
        .males .card-value { color: #3f51b5; }
        .females .card-value { color: #e91e63; }
        .total .card-value { color: #009688; }
        .percentage .card-value { color: #ffc107; }

        .section-title {
            font-size: 14px;
            font-weight: bold;
            text-align: center;
            margin: 30px 0 20px 0;
            color: #333;
        }

        .control-panel {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }

        .btn {
            background: #1976d2;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 12px 25px;
            font-size: 13px;
            font-weight: bold;
            cursor: pointer;
            transition: background 0.3s;
            margin: 5px;
            min-width: 150px;
        }

        .btn:hover {
            background: #1565c0;
        }

        .btn.secondary {
            background: #4caf50;
        }

        .btn.secondary:hover {
            background: #45a049;
        }

        .btn.warning {
            background: #ff9800;
        }

        .btn.warning:hover {
            background: #f57c00;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .error-message {
            background: #ffebee;
            color: #c62828;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #c62828;
            margin: 20px 0;
            display: none;
        }

        .success-message {
            background: #e8f5e8;
            color: #2e7d32;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #4caf50;
            margin: 20px 0;
            display: none;
        }

        .date-picker {
            margin: 15px 0;
        }

        .date-picker input {
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            width: 200px;
        }        .date-picker label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }

        .connection-status {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
            font-size: 12px;
            border-left: 4px solid #2196f3;
        }

        .connection-status.error {
            background: #ffebee;
            color: #c62828;
            border-left-color: #f44336;
        }

        .connection-status.success {
            background: #e8f5e8;
            color: #2e7d32;
            border-left-color: #4caf50;
        }

        /* تحسينات للأجهزة الصغيرة */
        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 15px;
            }
            
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .main-content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <a href="main_window0.html" class="back-button">← العودة للقائمة الرئيسية</a>
            <h1>📊 الإحصائيات العامة للمؤسسة</h1>
            <p>إحصائيات شاملة حسب السنة الدراسية</p>
        </div>        <div class="main-content">
            <div class="connection-status" id="connectionStatus">
                🔄 جاري فحص الاتصال بالخادم...
            </div>

            <div class="loading" id="loading">
                🔄 جاري تحميل البيانات...
            </div>

            <div class="error-message" id="errorMessage"></div>
            <div class="success-message" id="successMessage"></div>

            <div class="stats-title" id="statsTitle">
                إحصائيات عامة حسب السنة الدراسية
            </div>

            <!-- الإحصائيات الأساسية -->
            <div class="stats-grid">
                <div class="stats-card students">
                    <div class="card-title">إجمالي التلاميذ</div>
                    <div class="card-value" id="studentsCount">0</div>
                </div>

                <div class="stats-card sections">
                    <div class="card-title">عدد الأقسام</div>
                    <div class="card-value" id="sectionsCount">0</div>
                </div>

                <div class="stats-card levels">
                    <div class="card-title">عدد المستويات</div>
                    <div class="card-value" id="levelsCount">0</div>
                </div>

                <div class="stats-card average">
                    <div class="card-title">معدل التلاميذ/قسم</div>
                    <div class="card-value" id="averagePerSection">0</div>
                </div>
            </div>

            <div class="section-title">إحصائيات حسب النوع</div>

            <div class="stats-grid">
                <div class="stats-card males">
                    <div class="card-title">عدد الذكور</div>
                    <div class="card-value" id="malesCount">0</div>
                </div>

                <div class="stats-card females">
                    <div class="card-title">عدد الإناث</div>
                    <div class="card-value" id="femalesCount">0</div>
                </div>

                <div class="stats-card total">
                    <div class="card-title">الإجمالي</div>
                    <div class="card-value" id="totalGender">0</div>
                </div>

                <div class="stats-card percentage">
                    <div class="card-title">نسبة الإناث</div>
                    <div class="card-value" id="femalePercentage">0%</div>
                </div>
            </div>

            <div class="control-panel">
                <div class="date-picker">
                    <label for="referenceDate">تاريخ المرجع لحساب الأعمار:</label>
                    <input type="date" id="referenceDate" />
                </div>

                <button class="btn" onclick="updateStatistics()">
                    🔄 تحديث الإحصائيات
                </button>

                <button class="btn secondary" onclick="generateReport()">
                    📄 إنشاء تقرير PDF
                </button>                <button class="btn warning" onclick="exportData()">
                    📊 تصدير البيانات
                </button>                <button class="btn" onclick="checkServerConnection()" style="background: #607d8b;">
                    🔄 إعادة فحص الاتصال
                </button>

                <button class="btn" onclick="debugDatabase()" style="background: #9c27b0;">
                    🔍 فحص قاعدة البيانات
                </button>
            </div>
        </div>
    </div>

    <script>
        // الإعدادات الأساسية
        const API_BASE_URL = 'http://localhost:5000';
        let currentAcademicYear = '';
        let statisticsData = {};        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // تعيين التاريخ الحالي كتاريخ مرجع افتراضي
            document.getElementById('referenceDate').value = new Date().toISOString().split('T')[0];
            
            // فحص الاتصال بالخادم أولاً
            checkServerConnection();
        });        // فحص الاتصال بالخادم
        async function checkServerConnection() {
            const statusElement = document.getElementById('connectionStatus');
            statusElement.textContent = '🔄 جاري فحص الاتصال بالخادم...';
            statusElement.className = 'connection-status';
            
            try {
                console.log('🔄 محاولة الاتصال بالخادم...');
                
                const response = await fetch(`${API_BASE_URL}/api/check-status`);
                
                if (!response.ok) {
                    throw new Error(`خطأ HTTP: ${response.status}`);
                }

                const data = await response.json();
                console.log('📡 استجابة الخادم:', data);
                
                if (data.status === 'success') {
                    statusElement.textContent = `✅ متصل بالخادم - ${data.total_tables} جدول موجود`;
                    statusElement.className = 'connection-status success';
                    console.log('✅ الخادم متصل:', data.message);
                    
                    // بعد التأكد من الاتصال، تحميل البيانات
                    await loadInitialData();
                } else {
                    throw new Error(data.message || 'فشل في فحص حالة الخادم');
                }
                
            } catch (error) {
                console.error('❌ خطأ في الاتصال بالخادم:', error);
                statusElement.innerHTML = `❌ فشل الاتصال بالخادم: ${error.message}<br>
                💡 تأكد من تشغيل الخادم بالأمر: python unified_server.py`;
                statusElement.className = 'connection-status error';
                showLoading(false);
            }
        }

        // تحميل البيانات الأولية
        async function loadInitialData() {
            showLoading(true);
            try {
                // إعداد جدول بيانات المؤسسة
                await setupInstitutionTable();
                
                // تحميل البيانات الإحصائية
                await loadStatistics();
                
                showLoading(false);
            } catch (error) {
                showLoading(false);
                showError('خطأ في تحميل البيانات: ' + error.message);
            }
        }

        // إعداد جدول بيانات المؤسسة
        async function setupInstitutionTable() {
            try {
                const response = await fetch(`${API_BASE_URL}/api/institution/setup`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error('فشل في إعداد جدول بيانات المؤسسة');
                }

                const data = await response.json();
                console.log('تم إعداد جدول بيانات المؤسسة:', data.message);
            } catch (error) {
                console.error('خطأ في إعداد جدول بيانات المؤسسة:', error);
            }
        }        // تحميل الإحصائيات
        async function loadStatistics(academicYear = '') {
            try {
                console.log('📊 بدء تحميل الإحصائيات...', { academicYear });
                
                // إذا لم يتم تمرير السنة الدراسية، جلبها من الخادم
                if (!academicYear) {
                    currentAcademicYear = await getAcademicYear();
                } else {
                    currentAcademicYear = academicYear;
                }
                
                console.log('📅 السنة الدراسية المستخدمة:', currentAcademicYear);
                
                // تحديث العنوان
                const titleElement = document.getElementById('statsTitle');
                if (titleElement) {
                    titleElement.textContent = `إحصائيات عامة حسب السنة الدراسية ${currentAcademicYear || ''}`;
                }

                // الحصول على الإحصائيات
                statisticsData = await getStatistics(currentAcademicYear);
                
                // تحديث واجهة المستخدم
                updateStatisticsDisplay(statisticsData);
                
                showSuccess('تم تحميل الإحصائيات بنجاح');
            } catch (error) {
                console.error('❌ خطأ في loadStatistics:', error);
                showError('خطأ في تحميل الإحصائيات: ' + error.message);
            }
        }        // الحصول على السنة الدراسية
        async function getAcademicYear() {
            try {
                console.log('📅 جلب السنة الدراسية من endpoint الجديد...');
                const response = await fetch(`${API_BASE_URL}/api/academic-year`);
                
                if (!response.ok) {
                    console.warn('⚠️ فشل في جلب السنة الدراسية من endpoint الجديد، محاولة البديل...');
                    // محاولة البديل
                    const fallbackResponse = await fetch(`${API_BASE_URL}/api/institution/data`);
                    if (fallbackResponse.ok) {
                        const fallbackData = await fallbackResponse.json();
                        return fallbackData.data ? fallbackData.data['السنة_الدراسية'] || '' : '';
                    }
                    throw new Error('فشل في الحصول على السنة الدراسية');
                }

                const data = await response.json();
                console.log('📅 استجابة السنة الدراسية:', data);
                
                if (data.status === 'success') {
                    console.log('✅ تم جلب السنة الدراسية:', data.academic_year);
                    return data.academic_year || '';
                } else {
                    console.warn('⚠️ لم يتم العثور على السنة الدراسية');
                    return '';
                }
            } catch (error) {
                console.error('❌ خطأ في الحصول على السنة الدراسية:', error);
                return '';
            }
        }// الحصول على الإحصائيات
        async function getStatistics(academicYear = '') {
            try {
                console.log('📊 بدء جلب الإحصائيات...', { academicYear });
                
                // أولاً فحص نوع الجداول المتاحة
                console.log('🔍 فحص الجداول المتاحة...');
                const debugResponse = await fetch(`${API_BASE_URL}/api/debug/tables`);
                
                if (debugResponse.ok) {
                    const debugData = await debugResponse.json();
                    console.log('📋 الجداول المتاحة:', debugData.tables);
                    
                    // إذا لم يكن هناك بيانات في الجدول الرئيسي
                    if (!debugData.جدول_البيانات || !debugData.جدول_البيانات.exists || debugData.جدول_البيانات.count === 0) {
                        console.log('⚠️ لا توجد بيانات في جدول البيانات الرئيسي');
                        showError('لا توجد بيانات في النظام. يرجى استيراد البيانات أولاً من نافذة الاستيراد.');
                        
                        // إرجاع قيم افتراضية
                        return {
                            students: 0,
                            sections: 0,
                            levels: 0,
                            males: 0,
                            females: 0
                        };
                    }
                }
                
                // استخدام endpoint مخصص للإحصائيات العامة
                const url = academicYear ? 
                    `${API_BASE_URL}/api/statistics/general?academic_year=${academicYear}` :
                    `${API_BASE_URL}/api/statistics/general`;

                console.log('🌐 URL للإحصائيات:', url);

                const response = await fetch(url);
                console.log('📡 استجابة طلب الإحصائيات:', response.status, response.statusText);
                
                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('❌ خطأ في الاستجابة:', errorText);
                    throw new Error(`فشل في الحصول على الإحصائيات من الخادم (${response.status})`);
                }

                const data = await response.json();
                console.log('📊 بيانات الإحصائيات المستلمة:', data);
                
                if (data.status !== 'success') {
                    console.error('❌ حالة خطأ من الخادم:', data);
                    throw new Error(data.message || 'خطأ في الحصول على الإحصائيات');
                }

                console.log('✅ تم جلب الإحصائيات بنجاح:', data.data);
                return data.data;
            } catch (error) {
                console.error('❌ خطأ في getStatistics:', error);
                throw new Error('فشل في الحصول على الإحصائيات: ' + error.message);
            }
        }// تحديث عرض الإحصائيات
        function updateStatisticsDisplay(data) {
            console.log('🎨 تحديث عرض الإحصائيات:', data);
            
            if (!data) {
                console.error('❌ لا توجد بيانات لعرضها');
                showError('لا توجد بيانات إحصائية للعرض');
                return;
            }

            try {
                // الإحصائيات الأساسية
                document.getElementById('studentsCount').textContent = data.students || 0;
                document.getElementById('sectionsCount').textContent = data.sections || 0;
                document.getElementById('levelsCount').textContent = data.levels || 0;
                
                // حساب معدل التلاميذ لكل قسم
                const averagePerSection = (data.sections && data.sections > 0) ? Math.floor(data.students / data.sections) : 0;
                document.getElementById('averagePerSection').textContent = averagePerSection;

                // إحصائيات النوع
                document.getElementById('malesCount').textContent = data.males || 0;
                document.getElementById('femalesCount').textContent = data.females || 0;
                
                const totalGender = (data.males || 0) + (data.females || 0);
                document.getElementById('totalGender').textContent = totalGender;
                
                // حساب نسبة الإناث
                const femalePercentage = totalGender > 0 ? ((data.females / totalGender) * 100).toFixed(1) : 0;
                document.getElementById('femalePercentage').textContent = femalePercentage + '%';
                
                console.log('✅ تم تحديث العرض بنجاح');
            } catch (error) {
                console.error('❌ خطأ في تحديث العرض:', error);
                showError('خطأ في تحديث عرض الإحصائيات');
            }
        }        // تحديث الإحصائيات
        async function updateStatistics() {
            showLoading(true);
            try {
                // جلب السنة الدراسية أولاً
                console.log('📅 جلب السنة الدراسية...');
                let academicYear = '';
                
                try {
                    const yearResponse = await fetch(`${API_BASE_URL}/api/academic-year`);
                    if (yearResponse.ok) {
                        const yearData = await yearResponse.json();
                        if (yearData.status === 'success' && yearData.academic_year) {
                            academicYear = yearData.academic_year;
                            console.log('✅ تم جلب السنة الدراسية:', academicYear);
                            
                            // تحديث العنوان
                            const titleElement = document.querySelector('.stats-title');
                            if (titleElement) {
                                titleElement.textContent = `إحصائيات عامة حسب السنة الدراسية ${academicYear}`;
                            }
                        }
                    }
                } catch (yearError) {
                    console.warn('⚠️ خطأ في جلب السنة الدراسية:', yearError);
                }
                
                // جلب الإحصائيات باستخدام السنة الدراسية
                await loadStatistics(academicYear);
                showLoading(false);
            } catch (error) {
                showLoading(false);
                showError('خطأ في تحديث الإحصائيات: ' + error.message);
            }
        }

        // إنشاء تقرير PDF
        async function generateReport() {
            try {
                const referenceDate = document.getElementById('referenceDate').value;
                if (!referenceDate) {
                    showError('يرجى اختيار تاريخ المرجع لحساب الأعمار');
                    return;
                }

                showLoading(true);
                
                // هنا يمكن إضافة استدعاء API لإنشاء التقرير
                // في الوقت الحالي نعرض رسالة تأكيد
                setTimeout(() => {
                    showLoading(false);
                    showSuccess('تم إنشاء التقرير بنجاح! (الوظيفة قيد التطوير)');
                }, 2000);

            } catch (error) {
                showLoading(false);
                showError('خطأ في إنشاء التقرير: ' + error.message);
            }
        }

        // تصدير البيانات
        async function exportData() {
            try {
                // تحويل البيانات إلى CSV
                const csvData = convertToCSV(statisticsData);
                
                // إنشاء ملف وتحميله
                const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                const url = URL.createObjectURL(blob);
                
                link.setAttribute('href', url);
                link.setAttribute('download', `احصائيات_${new Date().toISOString().split('T')[0]}.csv`);
                link.style.visibility = 'hidden';
                
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                showSuccess('تم تصدير البيانات بنجاح');
            } catch (error) {
                showError('خطأ في تصدير البيانات: ' + error.message);
            }
        }

        // تحويل البيانات إلى CSV
        function convertToCSV(data) {
            const totalGender = data.males + data.females;
            const femalePercentage = totalGender > 0 ? ((data.females / totalGender) * 100).toFixed(1) : 0;
            const averagePerSection = data.sections > 0 ? Math.floor(data.students / data.sections) : 0;

            const csvContent = `البيان,القيمة
إجمالي التلاميذ,${data.students}
عدد الأقسام,${data.sections}
عدد المستويات,${data.levels}
معدل التلاميذ لكل قسم,${averagePerSection}
عدد الذكور,${data.males}
عدد الإناث,${data.females}
إجمالي حسب النوع,${totalGender}
نسبة الإناث,${femalePercentage}%
السنة الدراسية,${currentAcademicYear}
تاريخ التقرير,${new Date().toLocaleDateString('ar')}`;

            return '\ufeff' + csvContent; // إضافة BOM لدعم UTF-8
        }

        // فحص قاعدة البيانات للتشخيص
        async function debugDatabase() {
            try {
                console.log('🔍 بدء فحص قاعدة البيانات...');
                
                const response = await fetch(`${API_BASE_URL}/api/debug/tables`);
                
                if (!response.ok) {
                    throw new Error(`خطأ HTTP: ${response.status}`);
                }

                const data = await response.json();
                console.log('📊 نتائج فحص قاعدة البيانات:', data);
                
                if (data.status === 'success') {
                    let message = `📋 الجداول الموجودة (${data.tables_count}):\n`;
                    data.tables.forEach(table => {
                        message += `• ${table}\n`;
                    });
                    
                    if (data.جدول_البيانات && data.جدول_البيانات.exists) {
                        message += `\n📊 جدول البيانات الرئيسي:\n`;
                        message += `• عدد السجلات: ${data.جدول_البيانات.count}\n`;
                        if (data.جدول_البيانات.columns) {
                            message += `• الأعمدة: ${data.جدول_البيانات.columns.join(', ')}\n`;
                        }
                    } else {
                        message += `\n❌ جدول البيانات الرئيسي غير موجود!\n`;
                    }
                    
                    if (data.بيانات_المؤسسة && data.بيانات_المؤسسة.exists) {
                        message += `\n🏛️ بيانات المؤسسة موجودة (${data.بيانات_المؤسسة.count} سجل)\n`;
                    }
                    
                    alert(message);
                    showSuccess('تم فحص قاعدة البيانات - راجع Console للتفاصيل');
                } else {
                    throw new Error(data.message || 'فشل في فحص قاعدة البيانات');
                }
                
            } catch (error) {
                console.error('❌ خطأ في فحص قاعدة البيانات:', error);
                showError(`خطأ في فحص قاعدة البيانات: ${error.message}`);
            }
        }

        // عرض حالة التحميل
        function showLoading(show) {
            const loadingElement = document.getElementById('loading');
            loadingElement.style.display = show ? 'block' : 'none';
        }

        // عرض رسالة خطأ
        function showError(message) {
            const errorElement = document.getElementById('errorMessage');
            errorElement.textContent = message;
            errorElement.style.display = 'block';
            
            setTimeout(() => {
                errorElement.style.display = 'none';
            }, 5000);
        }

        // عرض رسالة نجاح
        function showSuccess(message) {
            const successElement = document.getElementById('successMessage');
            successElement.textContent = message;
            successElement.style.display = 'block';
            
            setTimeout(() => {
                successElement.style.display = 'none';
            }, 3000);
        }
    </script>
</body>
</html>
