"""
خادم Python المركزي لمنظومة إدارة التعليم
يتعامل مع جميع وظائف النظام: استيراد اللوائح، الإعدادات، إدارة الطلاب، وغيرها
يتعامل مع قاعدة البيانات data.db ومعالجة ملفات Excel
"""

from flask import Flask, request, jsonify, send_from_directory, send_file
from flask_cors import CORS
import sqlite3
import pandas as pd
import os
import json
from datetime import datetime
import traceback
import shutil
import zipfile
import tempfile
from openpyxl import load_workbook
import base64

app = Flask(__name__)
CORS(app)

# مسار قاعدة البيانات
DB_PATH = "data.db"

# قائمة الجداول المراد حذفها في عمليات الصيانة
TABLES_TO_CLEAR = [
    'الحساب_الرئيسي',
    'المسحوبات',
    'registration_fees',
    'احصائيات_الغياب_الشهرية',
    'احصائيات_الغياب_السنوية',
    'الاساتذة',
    'المصاريف',
    'الموازنة_السنوية',
    'الواجبات_الشهرية',
    'تدوين_الغياب',
    'جدول_الاداءات',
    'جدول_البيانات',
    'جدول_المواد_والاقسام',
    'tasks_appointments',
    'واجبات_التسجيل',
    'monthly_duties'
]

def log_message(message, status="info"):
    """إنشاء رسالة مع الطابع الزمني"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    icons = {
        "info": "ℹ️",
        "success": "✅", 
        "error": "❌",
        "warning": "⚠️",
        "progress": "🔄"
    }
    return f"{icons.get(status, 'ℹ️')} {timestamp} - {message}"

def get_db_connection():
    """إنشاء اتصال بقاعدة البيانات مع إرجاع النتائج كقاموس"""
    try:
        if not os.path.exists(DB_PATH):
            # إنشاء قاعدة بيانات فارغة إذا لم تكن موجودة
            conn = sqlite3.connect(DB_PATH)
            conn.close()
        
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row  # لإرجاع النتائج كقاموس
        return conn
    except Exception as e:
        print(f"خطأ في الاتصال بقاعدة البيانات: {str(e)}")
        return None

@app.route('/')
def index():
    """عرض الصفحة الرئيسية"""
    return send_from_directory('.', 'import_data_system.html')

@app.route('/api/check-database')
def check_database():
    """التحقق من وجود قاعدة البيانات"""
    try:
        if os.path.exists(DB_PATH):
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()
            
            # التحقق من وجود الجداول
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            conn.close()
            
            return jsonify({
                'status': 'success',
                'message': log_message('تم الاتصال بقاعدة البيانات بنجاح', 'success'),
                'tables': tables
            })
        else:
            return jsonify({
                'status': 'info',
                'message': log_message('قاعدة البيانات غير موجودة، سيتم إنشاؤها عند الاستيراد', 'info'),
                'tables': []
            })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': log_message(f'خطأ في الاتصال بقاعدة البيانات: {str(e)}', 'error')        })

@app.route('/api/import-excel', methods=['POST'])
def import_excel():
    """استيراد البيانات من ملف Excel"""
    try:
        if 'file' not in request.files:
            return jsonify({
                'status': 'error',
                'message': log_message('لم يتم اختيار ملف', 'error')
            })
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({
                'status': 'error', 
                'message': log_message('اسم الملف فارغ', 'error')
            })
        
        # حفظ الملف مؤقتاً
        temp_path = f"temp_{file.filename}"
        file.save(temp_path)
        
        try:
            # معالجة الملف
            result = process_excel_file(temp_path, file.filename)
            return jsonify(result)
        finally:
            # حذف الملف المؤقت
            if os.path.exists(temp_path):
                os.remove(temp_path)
                
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': log_message(f'خطأ في معالجة الملف: {str(e)}', 'error'),
            'error_details': traceback.format_exc()
        })

def process_excel_file(file_path, filename):
    """معالجة ملف Excel وحفظ البيانات في قاعدة البيانات"""
    messages = []
    
    try:
        # التحقق من اسم الملف
        if "ListEleve" not in filename:
            messages.append({
                'message': log_message(f'تحذير: الملف {filename} لا يحتوي على "ListEleve" في اسمه', 'warning'),
                'type': 'warning'
            })
        
        messages.append({
            'message': log_message('جاري قراءة ملف Excel...', 'progress'),
            'type': 'progress',
            'progress': 10
        })
        
        # قراءة ملف Excel
        sheets_dict = pd.read_excel(file_path, sheet_name=None)
        
        messages.append({
            'message': log_message('جاري استخراج البيانات...', 'progress'),
            'type': 'progress', 
            'progress': 20
        })
        
        # استخراج السنة الدراسية
        current_academic_year = None
        for sheet_name, df in sheets_dict.items():
            if "Unnamed: 6" in df.columns and len(df) > 5:
                current_academic_year = df.iloc[5]["Unnamed: 6"]
                break
        
        # معالجة البيانات
        all_data = []
        total_sheets = len(sheets_dict)
        
        for i, (sheet_name, df) in enumerate(sheets_dict.items()):
            progress = 20 + int((i / total_sheets) * 30)
            messages.append({
                'message': log_message(f'جاري معالجة القسم {sheet_name}...', 'progress'),
                'type': 'progress',
                'progress': progress
            })
            
            # معالجة بيانات الشيت
            level_rows = df[df["Unnamed: 0"].astype(str).str.strip() == ": المستوى"]
            level_value = level_rows.iloc[0]["Unnamed: 2"] if not level_rows.empty else None
            year_value = df.iloc[5]["Unnamed: 6"] if "Unnamed: 6" in df.columns and len(df) > 5 else None
            
            df["القسم"] = sheet_name
            df["المستوى"] = level_value  
            df["السنة الدراسية"] = year_value
            
            all_data.append(df)
        
        # دمج البيانات
        combined_df = pd.concat(all_data, ignore_index=True)
        
        messages.append({
            'message': log_message('جاري حفظ البيانات في قاعدة البيانات...', 'progress'),
            'type': 'progress',
            'progress': 60
        })
        
        # حفظ في قاعدة البيانات
        save_to_database(combined_df, current_academic_year, messages)
        
        # إنشاء ملخص النتائج
        summary = create_import_summary(current_academic_year)
        
        messages.append({
            'message': log_message('تم الانتهاء من استيراد البيانات بنجاح!', 'success'),
            'type': 'success',
            'progress': 100
        })
        
        return {
            'status': 'success',
            'messages': messages,
            'summary': summary,
            'academic_year': current_academic_year
        }
        
    except Exception as e:
        messages.append({
            'message': log_message(f'خطأ في معالجة الملف: {str(e)}', 'error'),
            'type': 'error'
        })
        return {
            'status': 'error',
            'messages': messages,
            'error': str(e)
        }

def save_to_database(combined_df, academic_year, messages):
    """حفظ البيانات في قاعدة البيانات"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    try:
        # حذف السجلات الافتراضية
        messages.append({
            'message': log_message('جاري حذف السجلات الافتراضية...', 'progress'),
            'type': 'progress',
            'progress': 65
        })
        
        delete_default_records(cursor)
        
        # إنشاء الجداول
        messages.append({
            'message': log_message('جاري إنشاء الجداول...', 'progress'), 
            'type': 'progress',
            'progress': 70
        })
        
        create_tables(cursor)
        
        # حفظ البيانات الأولية
        combined_df.to_sql("السجل الاولي", conn, if_exists='replace', index=False)
        
        # تحديث بيانات المؤسسة
        messages.append({
            'message': log_message('جاري تحديث بيانات المؤسسة...', 'progress'),
            'type': 'progress', 
            'progress': 75
        })
        
        update_school_info(cursor)
        
        # تحديث جدول اللوائح
        messages.append({
            'message': log_message('جاري تحديث جدول اللوائح...', 'progress'),
            'type': 'progress',
            'progress': 80
        })
        
        update_lists_table(cursor, academic_year)
        
        # تحديث السجل العام
        messages.append({
            'message': log_message('جاري تحديث السجل العام...', 'progress'),
            'type': 'progress',
            'progress': 85
        })
        
        update_general_record(cursor)
        
        # تحديث البنية التربوية
        messages.append({
            'message': log_message('جاري تحديث البنية التربوية...', 'progress'),
            'type': 'progress',
            'progress': 90
        })
        
        update_educational_structure(cursor, academic_year)
        
        # مهام إضافية
        update_levels_order(cursor)
        assign_all_to_guard1(cursor, academic_year)
        
        conn.commit()
        
        messages.append({
            'message': log_message('تم حفظ جميع البيانات بنجاح', 'success'),
            'type': 'success',
            'progress': 95
        })
        
    finally:
        conn.close()

def delete_default_records(cursor):
    """حذف السجلات الافتراضية"""
    try:
        cursor.execute("DELETE FROM السجل_العام WHERE الرمز = 'A12345678'")
        cursor.execute("DELETE FROM اللوائح WHERE الرمز = 'A12345678'")
        cursor.execute("DROP TABLE IF EXISTS 'السجل الاولي'")
    except:
        pass

def create_tables(cursor):
    """إنشاء جميع الجداول المطلوبة"""
    
    # جدول اللوائح
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS 'اللوائح' (
            'السنة_الدراسية' TEXT,
            'القسم' TEXT,
            'المستوى' TEXT, 
            'الرمز' TEXT,
            'رت' TEXT,
            'مجموع التلاميذ' INTEGER DEFAULT 0,
            PRIMARY KEY('السنة_الدراسية', 'الرمز')
        )
    """)
    
    # جدول السجل العام
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS 'السجل_العام' (
            'الرمز' TEXT PRIMARY KEY,
            'الاسم_والنسب' TEXT,
            'النوع' TEXT,
            'تاريخ_الازدياد' TEXT,
            'مكان_الازدياد' TEXT
        )
    """)
    
    # جدول البنية التربوية
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS 'البنية_التربوية' (
            'السنة_الدراسية' TEXT,
            'القسم' TEXT,
            'المستوى' TEXT,
            'مجموع_التلاميذ' INTEGER DEFAULT 0,
            'ترتيب_المستويات' INTEGER DEFAULT 99,
            'الأقسام_المسندة' TEXT DEFAULT '',
            PRIMARY KEY('السنة_الدراسية', 'القسم', 'المستوى')
        )
    """)
    
    # جدول بيانات المؤسسة
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS 'بيانات_المؤسسة' (
            'الأكاديمية' TEXT,
            'المديرية' TEXT,
            'الجماعة' TEXT,
            'المؤسسة' TEXT,
            'السنة_الدراسية' TEXT,
            'البلدة' TEXT,
            'المدير' TEXT,
            'الحارس_العام' TEXT,
            'السلك' TEXT,
            'رقم_الحراسة' TEXT,
            'رقم_التسجيل' TEXT,
            'الأسدس' TEXT,
            'ImagePath1' TEXT
        )
    """)

def update_school_info(cursor):
    """تحديث بيانات المؤسسة"""
    try:
        # استخراج البيانات من السجل الأولي
        cursor.execute("""
            SELECT "Unnamed: 2", "Unnamed: 6"
            FROM "السجل الاولي"
            LIMIT 1 OFFSET 3
        """)
        academy_row = cursor.fetchone()
        
        cursor.execute("""
            SELECT "Unnamed: 2", "Unnamed: 6"
            FROM "السجل الاولي"
            LIMIT 1 OFFSET 4
        """)
        directorate_row = cursor.fetchone()
        
        cursor.execute("""
            SELECT "Unnamed: 6"
            FROM "السجل الاولي"
            LIMIT 1 OFFSET 5
        """)
        year_row = cursor.fetchone()
        
        # حذف السجلات الموجودة وإدراج جديدة
        cursor.execute("DELETE FROM بيانات_المؤسسة")
        
        if academy_row and directorate_row and year_row:
            cursor.execute("""
                INSERT INTO بيانات_المؤسسة 
                (الأكاديمية, المديرية, الجماعة, المؤسسة, السنة_الدراسية, الأسدس)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (
                academy_row[0] or '',
                directorate_row[0] or '',
                academy_row[1] or '',
                directorate_row[1] or '',
                year_row[0] or '',
                'الأول'
            ))
    except:
        pass

def update_lists_table(cursor, academic_year):
    """تحديث جدول اللوائح"""
    # حذف السجلات القديمة
    if academic_year:
        cursor.execute("DELETE FROM اللوائح WHERE السنة_الدراسية = ?", (academic_year,))
    
    # إدراج البيانات الجديدة
    cursor.execute("""
        INSERT OR IGNORE INTO "اللوائح" ("السنة_الدراسية", "القسم", "المستوى", "الرمز", "رت")
        SELECT "السنة الدراسية", "القسم", "المستوى", "Unnamed: 1", "Unnamed: 0"
        FROM "السجل الاولي"
        WHERE "Unnamed: 1" IS NOT NULL AND TRIM("Unnamed: 1") <> '' AND "Unnamed: 1" <> 'الرمز'
    """)
    
    # تحديث مجموع التلاميذ
    if academic_year:
        cursor.execute("""
            UPDATE "اللوائح" 
            SET "مجموع التلاميذ" = (
                SELECT COUNT(*)
                FROM "اللوائح" AS l2
                WHERE l2."القسم" = "اللوائح"."القسم"
                AND l2."السنة_الدراسية" = "اللوائح"."السنة_الدراسية"
            )
            WHERE "السنة_الدراسية" = ?
        """, (academic_year,))

def update_general_record(cursor):
    """تحديث السجل العام"""
    cursor.execute("""
        INSERT OR IGNORE INTO "السجل_العام" 
        ("الرمز", "الاسم_والنسب", "النوع", "تاريخ_الازدياد", "مكان_الازدياد")
        SELECT "Unnamed: 1",
               COALESCE("Unnamed: 2", '') || ' ' || COALESCE("Unnamed: 3", ''),
               "Unnamed: 4",
               "Unnamed: 5", 
               "Unnamed: 6"
        FROM "السجل الاولي"
        WHERE "Unnamed: 1" IS NOT NULL 
        AND TRIM("Unnamed: 1") <> '' 
        AND "Unnamed: 1" <> 'الرمز'
    """)

def update_educational_structure(cursor, academic_year):
    """تحديث البنية التربوية"""
    # إدراج البيانات الجديدة
    cursor.execute("""
        INSERT OR IGNORE INTO "البنية_التربوية" ("السنة_الدراسية", "القسم", "المستوى")
        SELECT DISTINCT "السنة_الدراسية", "القسم", "المستوى"
        FROM "اللوائح"
        WHERE "السنة_الدراسية" IS NOT NULL
    """)
    
    # تحديث مجموع التلاميذ
    if academic_year:
        cursor.execute("""
            UPDATE "البنية_التربوية"
            SET "مجموع_التلاميذ" = (
                SELECT COUNT(*)
                FROM "اللوائح" AS l
                WHERE l."السنة_الدراسية" = "البنية_التربوية"."السنة_الدراسية"
                AND l."القسم" = "البنية_التربوية"."القسم"
                AND l."المستوى" = "البنية_التربوية"."المستوى"
            )
            WHERE "السنة_الدراسية" = ?
        """, (academic_year,))

def update_levels_order(cursor):
    """تحديث ترتيب المستويات"""
    cursor.execute("UPDATE البنية_التربوية SET ترتيب_المستويات = 99")
    cursor.execute("UPDATE البنية_التربوية SET ترتيب_المستويات = 1 WHERE المستوى LIKE '%جذع مشترك%' OR المستوى LIKE '%الجذع%'")
    cursor.execute("UPDATE البنية_التربوية SET ترتيب_المستويات = 2 WHERE المستوى LIKE '%الأولى بكالوريا%' OR المستوى LIKE '%سنة أولى بكالوريا%'")
    cursor.execute("UPDATE البنية_التربوية SET ترتيب_المستويات = 3 WHERE المستوى LIKE '%الثانية بكالوريا%' OR المستوى LIKE '%سنة ثانية بكالوريا%'")

def assign_all_to_guard1(cursor, academic_year):
    """تعيين جميع الأقسام لحراسة رقم 1"""
    if academic_year:
        cursor.execute("UPDATE البنية_التربوية SET الأقسام_المسندة = 'حراسة رقم 1' WHERE السنة_الدراسية = ?", (academic_year,))

def create_import_summary(academic_year):
    """إنشاء ملخص عملية الاستيراد"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        summary = {}
        
        if academic_year:
            summary['academic_year'] = academic_year
            
            # عدد الأقسام
            cursor.execute("SELECT DISTINCT القسم FROM اللوائح WHERE السنة_الدراسية = ? ORDER BY القسم", (academic_year,))
            sections = [row[0] for row in cursor.fetchall()]
            summary['sections'] = sections
            summary['sections_count'] = len(sections)
            
            # عدد التلاميذ
            cursor.execute("SELECT COUNT(*) FROM اللوائح WHERE السنة_الدراسية = ?", (academic_year,))
            students_count = cursor.fetchone()[0]
            summary['students_count'] = students_count
        
        conn.close()
        return summary
        
    except Exception as e:
        return {'error': str(e)}

@app.route('/api/import-secret-codes', methods=['POST'])
def import_secret_codes():
    """استيراد الرمز السري وتحيينه دفعة واحدة من ملفات Excel متعددة"""
    try:
        if 'files' not in request.files:
            return jsonify({
                'status': 'error',
                'message': log_message('لم يتم اختيار ملفات', 'error')
            })
        
        files = request.files.getlist('files')
        if not files or all(file.filename == '' for file in files):
            return jsonify({
                'status': 'error',
                'message': log_message('لم يتم اختيار ملفات صالحة', 'error')
            })
        
        # التحقق من عدد الملفات
        if len(files) > 100:
            files = files[:100]
        
        # معالجة الملفات
        result = process_secret_codes_files(files)
        return jsonify(result)
                
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': log_message(f'خطأ في معالجة الملفات: {str(e)}', 'error'),
            'error_details': traceback.format_exc()
        })

def process_secret_codes_files(files):
    """معالجة ملفات Excel للرمز السري وحفظها في قاعدة البيانات"""
    messages = []
    
    try:
        messages.append({
            'message': log_message(f'تم اختيار {len(files)} ملف للاستيراد', 'info'),
            'type': 'info',
            'progress': 5
        })
        
        # فتح اتصال بقاعدة البيانات
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # التحقق من وجود جدول الرمز_السري وحذف محتوياته
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='الرمز_السري'")
        if cursor.fetchone():
            messages.append({
                'message': log_message('جاري حذف البيانات السابقة من جدول الرمز_السري...', 'progress'),
                'type': 'progress',
                'progress': 10
            })
            cursor.execute("DELETE FROM الرمز_السري")
            messages.append({
                'message': log_message('تم حذف البيانات السابقة بنجاح', 'success'),
                'type': 'success'
            })
        else:
            # إنشاء جدول الرمز_السري إذا لم يكن موجوداً
            messages.append({
                'message': log_message('جاري إنشاء جدول الرمز_السري...', 'progress'),
                'type': 'progress',
                'progress': 10
            })
            cursor.execute('''
                CREATE TABLE الرمز_السري (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    الرمز TEXT UNIQUE,
                    الرمز_السري TEXT
                )
            ''')
            messages.append({
                'message': log_message('تم إنشاء جدول الرمز_السري بنجاح', 'success'),
                'type': 'success'
            })
        
        # معالجة الملفات
        total_records_imported = 0
        temp_files = []
        
        try:
            for file_index, file in enumerate(files):
                file_name = file.filename
                progress_percent = 15 + int((file_index / len(files)) * 60)
                
                messages.append({
                    'message': log_message(f'جاري معالجة الملف {file_index + 1}/{len(files)}: {file_name}', 'progress'),
                    'type': 'progress',
                    'progress': progress_percent
                })
                
                # حفظ الملف مؤقتاً
                temp_path = f"temp_secret_{file_index}_{file_name}"
                file.save(temp_path)
                temp_files.append(temp_path)
                
                try:
                    # قراءة ملف Excel
                    df = pd.read_excel(temp_path)
                    
                    if df.empty:
                        messages.append({
                            'message': log_message(f'الملف {file_name} فارغ. تم تخطيه.', 'warning'),
                            'type': 'warning'
                        })
                        continue
                    
                    # استخراج أعمدة الرمز والرمز السري
                    secret_records = extract_secret_codes_from_df(df, file_name, messages)
                    
                    # إدراج البيانات في قاعدة البيانات
                    if secret_records:
                        cursor.executemany("""
                            INSERT OR IGNORE INTO الرمز_السري (الرمز, الرمز_السري)
                            VALUES (?, ?)
                        """, secret_records)
                        conn.commit()
                        
                        total_records_imported += len(secret_records)
                        messages.append({
                            'message': log_message(f'تم استيراد {len(secret_records)} سجل من الملف {file_name}', 'success'),
                            'type': 'success'
                        })
                    else:
                        messages.append({
                            'message': log_message(f'لا توجد بيانات صالحة في الملف {file_name}', 'warning'),
                            'type': 'warning'
                        })
                        
                except Exception as file_error:
                    messages.append({
                        'message': log_message(f'خطأ في معالجة الملف {file_name}: {str(file_error)}', 'error'),
                        'type': 'error'
                    })
            
            # تحديث جدول السجل_العام بالرموز السرية المستوردة
            messages.append({
                'message': log_message('جاري تحديث جدول السجل_العام بالرموز السرية الجديدة...', 'progress'),
                'type': 'progress',
                'progress': 80
            })
            
            updated_records = update_general_record_with_secret_codes(cursor, messages)
            
            conn.commit()
            
            # ملخص العملية
            messages.append({
                'message': log_message(f'تم استيراد وتحديث الرمز السري بنجاح', 'success'),
                'type': 'success',
                'progress': 95
            })
            
            messages.append({
                'message': log_message(f'إجمالي السجلات المستوردة: {total_records_imported}', 'success'),
                'type': 'success'
            })
            
            if updated_records is not None:
                messages.append({
                    'message': log_message(f'تم تحديث {updated_records} سجل في جدول السجل_العام', 'success'),
                    'type': 'success'
                })
            
            messages.append({
                'message': log_message('تم الانتهاء من استيراد الرمز السري بنجاح!', 'success'),
                'type': 'success',
                'progress': 100
            })
            
        finally:
            # حذف الملفات المؤقتة
            for temp_file in temp_files:
                try:
                    if os.path.exists(temp_file):
                        os.remove(temp_file)
                except:
                    pass
        
        conn.close()
        
        return {
            'status': 'success',
            'messages': messages,
            'summary': {
                'total_files': len(files),
                'total_records': total_records_imported,
                'updated_records': updated_records if updated_records is not None else 0
            }
        }
        
    except Exception as e:
        messages.append({
            'message': log_message(f'خطأ في معالجة الرمز السري: {str(e)}', 'error'),
            'type': 'error'
        })
        return {
            'status': 'error',
            'messages': messages,
            'error': str(e)
        }

def extract_secret_codes_from_df(df, file_name, messages):
    """استخراج الرمز والرمز السري من DataFrame"""
    secret_records = []
    
    try:
        columns = list(df.columns)
        messages.append({
            'message': log_message(f'الأعمدة الموجودة: {columns}', 'info'),
            'type': 'info'
        })
        
        # البحث عن العمود الذي يحتوي على أرقام التلاميذ والرمز السري
        code_column = None
        secret_column = None
        
        # البحث في الصفوف للعثور على البيانات
        student_code_row = -1
        for index, row in df.iterrows():
            # البحث عن عبارة "رقم التلميذ" أو أرقام تشبه رموز التلاميذ
            for col in columns:
                cell_value = str(row[col]).strip() if pd.notna(row[col]) else ""
                if "رقم التلميذ" in cell_value or "رقم الطالب" in cell_value:
                    student_code_row = index
                    code_column = col
                    messages.append({
                        'message': log_message(f'تم العثور على عمود الرمز في العمود: {col}، الصف: {index + 1}', 'info'),
                        'type': 'info'
                    })
                    break
                # أو البحث عن رقم يشبه رمز تلميذ (يبدأ بحرف ويتبعه أرقام)
                elif len(cell_value) > 5 and cell_value[0].isalpha() and any(c.isdigit() for c in cell_value[1:]):
                    student_code_row = index
                    code_column = col
                    messages.append({
                        'message': log_message(f'تم العثور على رمز تلميذ محتمل في العمود: {col}، الصف: {index + 1}', 'info'),
                        'type': 'info'
                    })
                    break
            if code_column:
                break
        
        # إذا لم نجد عمود الرمز، نحاول العمود الثاني (D)
        if not code_column and len(columns) >= 2:
            code_column = columns[1]  # العمود D
            messages.append({
                'message': log_message(f'لم يتم العثور على عمود الرمز تلقائياً، سيتم استخدام العمود: {code_column}', 'warning'),
                'type': 'warning'
            })        # البحث عن عمود الرمز السري - نحدد العمود H كأولوية
        if code_column:
            # أولاً نبحث عن العمود H في الأعمدة المتاحة
            if 'H' in columns:
                secret_column = 'H'  # العمود H مباشرة
                messages.append({
                    'message': log_message(f'سيتم استخدام العمود H للرمز السري', 'info'),
                    'type': 'info'
                })
            elif len(columns) >= 6:
                # إذا لم يكن العمود H متاحاً بالاسم، نجرب العمود السادس (عادة ما يكون H)
                secret_column = columns[5]  # العمود السادس (H عادة)
                messages.append({
                    'message': log_message(f'سيتم استخدام العمود السادس للرمز السري: {secret_column}', 'info'),
                    'type': 'info'
                })
            else:
                # إذا لم يكن متاحاً، نجرب العمود المجاور لعمود الرمز
                code_col_index = columns.index(code_column)
                if code_col_index + 1 < len(columns):
                    secret_column = columns[code_col_index + 1]
                elif code_col_index + 2 < len(columns):
                    secret_column = columns[code_col_index + 2]
        
        if code_column and secret_column:
            messages.append({
                'message': log_message(f'تم تحديد الأعمدة: الرمز = {code_column}, الرمز السري = {secret_column}', 'success'),
                'type': 'success'
            })
            
            # استخراج البيانات بدءاً من الصف الذي يحتوي على البيانات الفعلية
            start_row = max(0, student_code_row)
            
            for index in range(start_row, len(df)):
                try:
                    row = df.iloc[index]
                    code = str(row[code_column]).strip() if pd.notna(row[code_column]) else ""
                    secret = str(row[secret_column]).strip() if pd.notna(row[secret_column]) else ""
                    
                    # تنظيف البيانات
                    code = code.replace("nan", "").strip()
                    secret = secret.replace("nan", "").strip()
                    
                    # التحقق من صحة البيانات
                    if (code and len(code) > 3 and 
                        secret and len(secret) > 1 and 
                        code != "رقم التلميذ" and secret != "رقم التلميذ"):
                        
                        # تنظيف الرمز (إزالة المسافات والأحرف غير المرغوبة)
                        import re
                        code = re.sub(r'[^\w\d]', '', code)
                        
                        if code and secret and code != secret:
                            secret_records.append((code, secret))
                    
                except Exception as row_error:
                    continue
            
            messages.append({
                'message': log_message(f'تم تحضير {len(secret_records)} سجل صالح من أصل {len(df)} صف', 'success'),
                'type': 'success'
            })
            
        else:
            messages.append({
                'message': log_message(f'لم يتم العثور على الأعمدة المطلوبة في الملف {file_name}', 'error'),
                'type': 'error'
            })
    
    except Exception as e:
        messages.append({
            'message': log_message(f'خطأ في استخراج البيانات من الملف {file_name}: {str(e)}', 'error'),
            'type': 'error'
        })
    
    return secret_records

def update_general_record_with_secret_codes(cursor, messages):
    """تحديث جدول السجل_العام بالرموز السرية المستوردة"""
    try:
        # التحقق من وجود عمود الرمز_السري في جدول السجل_العام
        cursor.execute("PRAGMA table_info(السجل_العام)")
        columns = [col[1] for col in cursor.fetchall()]
        
        if "الرمز_السري" not in columns:
            # إضافة عمود الرمز_السري إذا لم يكن موجوداً
            cursor.execute("ALTER TABLE السجل_العام ADD COLUMN الرمز_السري TEXT")
            messages.append({
                'message': log_message('تم إضافة عمود الرمز_السري إلى جدول السجل_العام', 'info'),
                'type': 'info'
            })
        
        # تحديث جدول السجل_العام بقيم الرمز السري من جدول الرمز_السري
        cursor.execute("""
            UPDATE السجل_العام
            SET الرمز_السري = (
                SELECT الرمز_السري.الرمز_السري
                FROM الرمز_السري
                WHERE الرمز_السري.الرمز = السجل_العام.الرمز
            )
            WHERE EXISTS (
                SELECT 1
                FROM الرمز_السري
                WHERE الرمز_السري.الرمز = السجل_العام.الرمز
            )
        """)
        
        # عدد السجلات التي تم تحديثها
        updated_records = cursor.rowcount
        return updated_records
        
    except sqlite3.OperationalError as e:
        messages.append({
            'message': log_message(f'خطأ في تحديث جدول السجل_العام: {str(e)}', 'error'),
            'type': 'error'
        })
        return None

@app.route('/api/get-summary/<academic_year>')
def get_summary(academic_year):
    """الحصول على ملخص البيانات لسنة دراسية معينة"""
    try:
        summary = create_import_summary(academic_year)
        return jsonify({
            'status': 'success',
            'summary': summary
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        })

@app.route('/api/import-teachers', methods=['POST'])
def import_teachers():
    """استيراد أسماء الأساتذة والمواد المدرسة من ملف Excel"""
    try:
        if 'file' not in request.files:
            return jsonify({
                'status': 'error',
                'message': log_message('لم يتم اختيار ملف', 'error')
            })
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({
                'status': 'error',
                'message': log_message('اسم الملف فارغ', 'error')
            })
        
        # حفظ الملف مؤقتاً
        temp_path = f"temp_teachers_{file.filename}"
        file.save(temp_path)
        
        try:
            # معالجة الملف
            result = process_teachers_file(temp_path, file.filename)
            return jsonify(result)
        finally:
            # حذف الملف المؤقت
            if os.path.exists(temp_path):
                os.remove(temp_path)
                
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': log_message(f'خطأ في معالجة الملف: {str(e)}', 'error'),
            'error_details': traceback.format_exc()
        })

def process_teachers_file(file_path, filename):
    """معالجة ملف Excel للأساتذة والمواد وحفظه في قاعدة البيانات"""
    messages = []
    
    try:
        # التحقق من اسم الملف
        valid_keywords = ["SeancesEnseignants", "Book"]
        is_valid_file = any(keyword in filename for keyword in valid_keywords)
        
        if not is_valid_file:
            messages.append({
                'message': log_message(f'تحذير: الملف {filename} لا يحتوي على "SeancesEnseignants" أو "Book" في اسمه', 'warning'),
                'type': 'warning'
            })
        
        messages.append({
            'message': log_message('جاري قراءة ملف Excel...', 'progress'),
            'type': 'progress',
            'progress': 10
        })
        
        # فتح اتصال بقاعدة البيانات
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # التحقق من وجود جدول الأساتذة وحذف محتوياته
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='الأساتذة'")
        if cursor.fetchone():
            messages.append({
                'message': log_message('جاري حذف البيانات السابقة من جدول الأساتذة...', 'progress'),
                'type': 'progress',
                'progress': 20
            })
            cursor.execute("DELETE FROM الأساتذة")
            messages.append({
                'message': log_message('تم حذف البيانات السابقة بنجاح', 'success'),
                'type': 'success'
            })
        else:
            # إنشاء جدول الأساتذة إذا لم يكن موجوداً
            messages.append({
                'message': log_message('جاري إنشاء جدول الأساتذة...', 'progress'),
                'type': 'progress',
                'progress': 20
            })
            cursor.execute('''
                CREATE TABLE الأساتذة (
                    رقم_الأستاذ INTEGER PRIMARY KEY AUTOINCREMENT,
                    اسم_الأستاذ TEXT,
                    المادة TEXT,
                    الرمز TEXT
                )
            ''')
            messages.append({
                'message': log_message('تم إنشاء جدول الأساتذة بنجاح', 'success'),
                'type': 'success'
            })
        
        messages.append({
            'message': log_message(f'جاري قراءة الملف: {filename}', 'progress'),
            'type': 'progress',
            'progress': 30
        })
        
        # قراءة ملف Excel - بدون تحديد header
        df = pd.read_excel(file_path, header=None)
        
        if df.empty:
            messages.append({
                'message': log_message('الملف فارغ. لا توجد بيانات لاستيرادها.', 'warning'),
                'type': 'warning'
            })
            conn.close()
            return {
                'status': 'warning',
                'messages': messages
            }
        
        # التحقق من عدد الأعمدة
        if df.shape[1] < 4:
            messages.append({
                'message': log_message('تنسيق الملف غير صحيح. عدد الأعمدة غير كافية.', 'error'),
                'type': 'error'
            })
            conn.close()
            return {
                'status': 'error',
                'messages': messages
            }
        
        messages.append({
            'message': log_message('جاري معالجة البيانات...', 'progress'),
            'type': 'progress',
            'progress': 40
        })
        
        # استخدام الأعمدة المطلوبة (B=1, C=2, D=3)
        # B (index 1) -> المادة
        # C (index 2) -> الرمز  
        # D (index 3) -> اسم_الأستاذ
        df_selected = df[[1, 2, 3]].copy()
        df_selected.columns = ['المادة', 'الرمز', 'اسم_الأستاذ']
        
        messages.append({
            'message': log_message('جاري ملء الفراغات في عمود المادة...', 'progress'),
            'type': 'progress',
            'progress': 50
        })
        
        # ملء الفراغات في عمود المادة بالمادة التي قبلها
        previous_subject = None
        for index, row in df_selected.iterrows():
            current_subject = row['المادة']
            
            # تحقق مما إذا كانت القيمة الحالية فارغة
            if pd.isna(current_subject) or (isinstance(current_subject, str) and current_subject.strip() == ''):
                if previous_subject is not None:
                    df_selected.at[index, 'المادة'] = previous_subject
            else:
                # تحديث المادة السابقة
                previous_subject = current_subject
        
        messages.append({
            'message': log_message('جاري تنظيف البيانات...', 'progress'),
            'type': 'progress',
            'progress': 60
        })
        
        # حذف الصفوف التي تحتوي على قيم فارغة في اسم_الأستاذ
        df_selected = df_selected.dropna(subset=['اسم_الأستاذ'])
        
        # حذف الصف الذي يحتوي على "المجموع الاجمالي"
        df_selected = df_selected[~df_selected['اسم_الأستاذ'].astype(str).str.contains('المجموع الاجمالي', na=False)]
        
        # تنظيف البيانات
        df_selected['المادة'] = df_selected['المادة'].astype(str)
        df_selected['الرمز'] = df_selected['الرمز'].fillna('').astype(str)
        df_selected['اسم_الأستاذ'] = df_selected['اسم_الأستاذ'].astype(str)
        
        messages.append({
            'message': log_message(f'جاري إدراج {len(df_selected)} سجل في قاعدة البيانات...', 'progress'),
            'type': 'progress',
            'progress': 70
        })
        
        # إدراج البيانات في قاعدة البيانات
        records_inserted = 0
        
        for _, row in df_selected.iterrows():
            # التحقق من أن جميع القيم المطلوبة موجودة
            if row['اسم_الأستاذ'].strip() and row['المادة'].strip():
                try:
                    cursor.execute(
                        "INSERT INTO الأساتذة (اسم_الأستاذ, المادة, الرمز) VALUES (?, ?, ?)",
                        (row['اسم_الأستاذ'], row['المادة'], row['الرمز'])
                    )
                    records_inserted += 1
                except Exception as e:
                    messages.append({
                        'message': log_message(f'خطأ في إدراج السجل: {str(e)}', 'error'),
                        'type': 'error'
                    })
        
        # حفظ التغييرات
        conn.commit()
        conn.close()
        
        messages.append({
            'message': log_message('تم الانتهاء من استيراد أسماء الأساتذة والمواد المدرسة', 'success'),
            'type': 'success',
            'progress': 90
        })
        
        messages.append({
            'message': log_message(f'تم إدراج {records_inserted} سجل بنجاح', 'success'),
            'type': 'success',
            'progress': 100
        })
        
        return {
            'status': 'success',
            'messages': messages,
            'summary': {
                'total_records': records_inserted,
                'filename': filename
            }
        }
        
    except Exception as e:
        messages.append({
            'message': log_message(f'خطأ في معالجة الملف: {str(e)}', 'error'),
            'type': 'error'
        })
        return {            'status': 'error',
            'messages': messages,
            'error': str(e)
        }

# ===================================
# وظائف الإعدادات والصيانة
# ===================================

@app.route('/settings')
def settings_page():
    """صفحة الإعدادات"""
    return send_from_directory('.', 'settings_window.html')

@app.route('/api/delete-all-data', methods=['POST'])
def delete_all_data():
    """حذف جميع البيانات من الجداول المحددة"""
    try:
        data = request.get_json()
        tables = data.get('tables', TABLES_TO_CLEAR)
        
        conn = get_db_connection()
        if conn is None:
            return jsonify({
                'success': False,
                'message': 'لا يمكن الاتصال بقاعدة البيانات'
            }), 500
        
        cursor = conn.cursor()
        deleted_tables = []
        
        # حذف البيانات من كل جدول
        for table in tables:
            try:
                # التحقق من وجود الجدول
                cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
                if cursor.fetchone():
                    cursor.execute(f"DELETE FROM {table}")
                    deleted_tables.append(table)
                    print(f"تم حذف البيانات من جدول: {table}")
                else:
                    print(f"الجدول غير موجود: {table}")
            except Exception as table_error:
                print(f"خطأ في حذف بيانات الجدول {table}: {table_error}")
                continue
        
        # ضغط قاعدة البيانات
        try:
            cursor.execute("VACUUM")
            print("تم ضغط قاعدة البيانات بنجاح")
        except Exception as e:
            print(f"خطأ أثناء ضغط قاعدة البيانات: {str(e)}")
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': 'تم حذف جميع البيانات بنجاح',
            'deletedTables': len(deleted_tables)
        })
        
    except Exception as e:
        print(f"خطأ في حذف البيانات: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'خطأ أثناء حذف البيانات: {str(e)}'
        }), 500

@app.route('/api/prepare-new-season', methods=['POST'])
def prepare_new_season():
    """تهيئة البرنامج لبداية موسم جديد"""
    try:
        data = request.get_json()
        tables = data.get('tables', TABLES_TO_CLEAR)
        
        conn = get_db_connection()
        if conn is None:
            return jsonify({
                'success': False,
                'message': 'لا يمكن الاتصال بقاعدة البيانات'
            }), 500
        
        cursor = conn.cursor()
        deleted_tables = []
        
        # حذف البيانات من كل جدول
        for table in tables:
            try:
                cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
                if cursor.fetchone():
                    cursor.execute(f"DELETE FROM {table}")
                    deleted_tables.append(table)
                    print(f"تم حذف البيانات من جدول: {table}")
                else:
                    print(f"الجدول غير موجود: {table}")
            except Exception as table_error:
                print(f"خطأ في حذف بيانات الجدول {table}: {table_error}")
                continue
        
        # ضغط قاعدة البيانات
        try:
            cursor.execute("VACUUM")
            print("تم ضغط قاعدة البيانات بنجاح")
        except Exception as e:
            print(f"خطأ أثناء ضغط قاعدة البيانات: {str(e)}")
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': 'تم تهيئة البرنامج لبداية موسم جديد بنجاح',
            'deletedTables': len(deleted_tables)
        })
        
    except Exception as e:
        print(f"خطأ في تهيئة الموسم الجديد: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'❌ خطأ أثناء تهيئة الموسم الجديد: {str(e)}'
        }), 500

@app.route('/api/backup-database', methods=['POST'])
def backup_database():
    """عمل نسخة احتياطية لقاعدة البيانات"""
    try:
        # إنشاء مجلد مؤقت للنسخة الاحتياطية
        temp_dir = tempfile.mkdtemp()
        
        # توليد اسم ملف النسخة الاحتياطية
        current_datetime = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        backup_name = f"database_backup_{current_datetime}"
        backup_sqlite = os.path.join(temp_dir, f"{backup_name}.sqlite")
        backup_zip = os.path.join(temp_dir, f"{backup_name}.zip")
        
        # إنشاء النسخة الاحتياطية
        conn = sqlite3.connect(DB_PATH)
        
        # إصلاح وضغط قاعدة البيانات
        conn.execute("PRAGMA integrity_check")
        conn.execute("VACUUM")
        
        # إنشاء نسخة احتياطية
        backup_conn = sqlite3.connect(backup_sqlite)
        conn.backup(backup_conn)
        
        backup_conn.close()
        conn.close()
        
        # ضغط ملف النسخة الاحتياطية
        with zipfile.ZipFile(backup_zip, 'w', zipfile.ZIP_DEFLATED) as zipf:
            zipf.write(backup_sqlite, os.path.basename(backup_sqlite))
        
        # حذف الملف المؤقت
        os.remove(backup_sqlite)
        
        # إرسال الملف المضغوط
        return send_file(
            backup_zip,
            as_attachment=True,
            download_name=f"{backup_name}.zip",
            mimetype='application/zip'
        )
        
    except Exception as e:
        print(f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء عمل نسخة احتياطية: {str(e)}'
        }), 500

@app.route('/api/restore-backup', methods=['POST'])
def restore_backup():
    """استيراد نسخة احتياطية"""
    try:
        if 'backup_file' not in request.files:
            return jsonify({
                'success': False,
                'message': 'لم يتم اختيار ملف'
            }), 400
        
        file = request.files['backup_file']
        if file.filename == '':
            return jsonify({
                'success': False,
                'message': 'لم يتم اختيار ملف'
            }), 400
        
        # حفظ الملف مؤقتاً
        temp_dir = tempfile.mkdtemp()
        file_path = os.path.join(temp_dir, file.filename)
        file.save(file_path)
        
        backup_file_path = None
        
        # التحقق من نوع الملف
        if file.filename.lower().endswith('.zip'):
            # استخراج الملف المضغوط
            with zipfile.ZipFile(file_path, 'r') as zip_ref:
                file_list = zip_ref.namelist()
                backup_files = [f for f in file_list if f.endswith(('.sqlite', '.db'))]
                
                if not backup_files:
                    return jsonify({
                        'success': False,
                        'message': 'الملف المختار لا يحتوي على نسخة احتياطية صالحة'
                    }), 400
                
                zip_ref.extract(backup_files[0], temp_dir)
                backup_file_path = os.path.join(temp_dir, backup_files[0])
                
        elif file.filename.lower().endswith(('.sqlite', '.db')):
            backup_file_path = file_path
        else:
            return jsonify({
                'success': False,
                'message': 'نوع الملف غير مدعوم'
            }), 400
        
        # فحص النسخة الاحتياطية
        test_conn = sqlite3.connect(backup_file_path)
        test_cursor = test_conn.cursor()
        
        # التحقق من وجود الجداول الأساسية
        test_cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in test_cursor.fetchall()]
        
        essential_tables = ["بيانات_المؤسسة"]
        
        if not all(table in tables for table in essential_tables):
            test_conn.close()
            shutil.rmtree(temp_dir, ignore_errors=True)
            return jsonify({
                'success': False,
                'message': 'النسخة الاحتياطية غير صالحة: لا تحتوي على جميع الجداول الأساسية'
            }), 400
        
        test_conn.close()
        
        # إنشاء نسخة احتياطية من قاعدة البيانات الحالية
        current_time = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        pre_restore_backup = f"pre_restore_backup_{current_time}.db"
        backup_folder = os.path.join(os.path.dirname(os.path.abspath(DB_PATH)), "Backups")
        
        if not os.path.exists(backup_folder):
            os.makedirs(backup_folder)
        
        pre_restore_path = os.path.join(backup_folder, pre_restore_backup)
        
        # نسخ قاعدة البيانات الحالية
        if os.path.exists(DB_PATH):
            shutil.copy2(DB_PATH, pre_restore_path)
        
        # استبدال قاعدة البيانات
        shutil.copy2(backup_file_path, DB_PATH)
        
        # تنظيف المجلد المؤقت
        shutil.rmtree(temp_dir, ignore_errors=True)
        
        return jsonify({
            'success': True,
            'message': 'تمت استعادة النسخة الاحتياطية بنجاح',
            'preRestoreBackup': pre_restore_backup
        })
        
    except Exception as e:
        print(f"خطأ في استعادة النسخة الاحتياطية: {str(e)}")
        traceback.print_exc()
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء استعادة النسخة الاحتياطية: {str(e)}'
        }), 500

# ===================================
# وظائف إدارة الطلاب والبيانات العامة
# ===================================

@app.route('/api/test')
def test_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    try:
        conn = get_db_connection()
        if conn is None:
            return jsonify({'error': 'لا يمكن الاتصال بقاعدة البيانات'}), 500
        
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) as count FROM sqlite_master WHERE type='table'")
        result = cursor.fetchone()
        conn.close()
        
        return jsonify({
            'status': 'success',
            'message': 'تم الاتصال بقاعدة البيانات بنجاح',
            'tables_count': result['count'] if result else 0
        })
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'خطأ في الاتصال: {str(e)}'
        }), 500

@app.route('/api/students', methods=['GET'])
def get_students():
    """الحصول على قائمة الطلاب مع الفلترة والبحث"""
    try:
        conn = get_db_connection()
        if conn is None:
            return jsonify({'error': 'لا يمكن الاتصال بقاعدة البيانات'}), 500
        
        cursor = conn.cursor()
        
        # الحصول على معاملات البحث والفلترة
        search = request.args.get('search', '').strip()
        group = request.args.get('group', '').strip()
        section = request.args.get('section', '').strip()
        limit = request.args.get('limit', 50, type=int)
        offset = request.args.get('offset', 0, type=int)
        
        # بناء الاستعلام
        base_query = """
            SELECT الرمز, اسم_التلميذ, القسم, اسم_المجموعة, النوع, 
                   الواجب_الشهري, المدفوع, المتبقي
            FROM جدول_البيانات 
            WHERE 1=1
        """
        
        params = []
        
        # إضافة شروط البحث
        if search:
            base_query += " AND (اسم_التلميذ LIKE ? OR الرمز LIKE ?)"
            params.extend([f'%{search}%', f'%{search}%'])
        
        if group:
            base_query += " AND اسم_المجموعة = ?"
            params.append(group)
        
        if section:
            base_query += " AND القسم = ?"
            params.append(section)
        
        # إضافة الترتيب والحد
        base_query += " ORDER BY اسم_التلميذ LIMIT ? OFFSET ?"
        params.extend([limit, offset])
        
        cursor.execute(base_query, params)
        students = [dict(row) for row in cursor.fetchall()]
        
        # الحصول على العدد الإجمالي
        count_query = base_query.replace(
            "SELECT الرمز, اسم_التلميذ, القسم, اسم_المجموعة, النوع, الواجب_الشهري, المدفوع, المتبقي",
            "SELECT COUNT(*)"
        ).split("ORDER BY")[0]
        
        cursor.execute(count_query, params[:-2])  # إزالة limit و offset
        total = cursor.fetchone()[0]
        
        conn.close()
        
        return jsonify({
            'students': students,
            'total': total,
            'limit': limit,
            'offset': offset
        })
        
    except Exception as e:
        return jsonify({'error': f'خطأ في جلب البيانات: {str(e)}'}), 500

@app.route('/api/groups')
def get_groups():
    """الحصول على قائمة المجموعات المتاحة"""
    try:
        conn = get_db_connection()
        if conn is None:
            return jsonify({'error': 'لا يمكن الاتصال بقاعدة البيانات'}), 500
        
        cursor = conn.cursor()
        cursor.execute("SELECT DISTINCT اسم_المجموعة FROM جدول_البيانات WHERE اسم_المجموعة IS NOT NULL ORDER BY اسم_المجموعة")
        groups = [row[0] for row in cursor.fetchall()]
        
        conn.close()
        return jsonify({'groups': groups})
        
    except Exception as e:
        return jsonify({'error': f'خطأ في جلب المجموعات: {str(e)}'}), 500

@app.route('/api/sections')
def get_sections():
    """الحصول على قائمة الأقسام المتاحة"""
    try:
        conn = get_db_connection()
        if conn is None:
            return jsonify({'error': 'لا يمكن الاتصال بقاعدة البيانات'}), 500
        
        cursor = conn.cursor()
        cursor.execute("SELECT DISTINCT القسم FROM جدول_البيانات WHERE القسم IS NOT NULL ORDER BY القسم")
        sections = [row[0] for row in cursor.fetchall()]
        
        conn.close()
        return jsonify({'sections': sections})
        
    except Exception as e:
        return jsonify({'error': f'خطأ في جلب الأقسام: {str(e)}'}), 500

@app.route('/api/statistics')
def get_statistics():
    """الحصول على إحصائيات عامة"""
    try:
        conn = get_db_connection()
        if conn is None:
            return jsonify({'error': 'لا يمكن الاتصال بقاعدة البيانات'}), 500
        
        cursor = conn.cursor()
        
        # إحصائيات أساسية
        cursor.execute("SELECT COUNT(*) FROM جدول_البيانات")
        total_students = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(DISTINCT اسم_المجموعة) FROM جدول_البيانات WHERE اسم_المجموعة IS NOT NULL")
        total_groups = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(DISTINCT القسم) FROM جدول_البيانات WHERE القسم IS NOT NULL")
        total_sections = cursor.fetchone()[0]
        
        # إحصائيات حسب النوع
        cursor.execute("SELECT النوع, COUNT(*) FROM جدول_البيانات WHERE النوع IS NOT NULL GROUP BY النوع")
        gender_stats = dict(cursor.fetchall())
        
        # إحصائيات مالية
        cursor.execute("SELECT SUM(الواجب_الشهري), SUM(المدفوع), SUM(المتبقي) FROM جدول_البيانات")
        financial = cursor.fetchone()
        
        conn.close()
        
        return jsonify({
            'total_students': total_students,
            'total_groups': total_groups,
            'total_sections': total_sections,
            'gender_distribution': gender_stats,
            'financial_summary': {
                'total_due': financial[0] or 0,
                'total_paid': financial[1] or 0,
                'total_remaining': financial[2] or 0
            }
        })
        
    except Exception as e:
        return jsonify({'error': f'خطأ في جلب الإحصائيات: {str(e)}'}), 500

# ===================================
# الخادم الرئيسي المركزي
# ===================================

if __name__ == '__main__':
    print("🚀 بدء تشغيل الخادم المركزي لمنظومة إدارة التعليم...")
    print("📡 الخادم يعمل على: http://localhost:5000")
    print("📊 قاعدة البيانات: data.db")
    print("🔧 صفحة الإعدادات: http://localhost:5000/settings")
    print("📋 صفحة استيراد البيانات: http://localhost:5000/")
    print("=" * 60)
    app.run(debug=True, host='0.0.0.0', port=5000)
