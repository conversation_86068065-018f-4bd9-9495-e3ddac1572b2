import pandas as pd
import os

# فحص الملف
file_path = r"اللوائح\الرمز الزري\export_InfoEleve_22032025110700.xlsx"

print("بدء فحص الملف...")

try:
    if os.path.exists(file_path):
        print("الملف موجود")
        df = pd.read_excel(file_path)
        print("عدد الأعمدة:", len(df.columns))
        print("عدد الصفوف:", len(df))
        
        if len(df.columns) >= 2:
            print("العمود الأول:", df.columns[0])
            print("العمود الثاني:", df.columns[1])
            
            # فحص أول 3 صفوف
            for i in range(min(3, len(df))):
                val1 = df.iloc[i][df.columns[0]]
                val2 = df.iloc[i][df.columns[1]]
                val1_str = str(val1).strip() if pd.notna(val1) else "فارغ"
                val2_str = str(val2).strip() if pd.notna(val2) else "فارغ"
                print(f"الصف {i+1}: '{val1_str}' | '{val2_str}'")
                
                # التحقق من الطول
                if val1_str != "فارغ" and val2_str != "فارغ":
                    print(f"  الطول: {len(val1_str)} | {len(val2_str)}")
        else:
            print("عدد الأعمدة غير كافي")
    else:
        print("الملف غير موجود")
        
except Exception as e:
    print("خطأ:", str(e))

print("انتهى الفحص")
