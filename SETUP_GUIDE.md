# 🔧 دليل استخدام تبويب تهيئة البرنامج

## 📋 نظرة عامة

تم إضافة تبويب جديد باسم **"تهيئة البرنامج"** يحتوي على أدوات شاملة لإدارة واستيراد البيانات في النظام. هذا التبويب مقسم إلى تبويبات عمودية فرعية لسهولة الاستخدام.

## 🚀 الميزات الجديدة

### 📋 **1. استيراد اللوائح وتحينها**
#### الوظائف المتاحة:
- **📁 استيراد من منظومة مسار**: استيراد ملفات Excel من منظومة مسار
- **🔄 تحديث اللوائح**: تحديث وتصحيح البيانات الموجودة
- **📊 سجل العمليات**: متابعة تقدم عمليات الاستيراد في الوقت الفعلي

#### خيارات الاستيراد:
- **استيراد جديد**: حذف البيانات السابقة واستيراد بيانات جديدة
- **تحديث البيانات**: تحديث البيانات الموجودة فقط
- **دمج البيانات**: دمج البيانات الجديدة مع الموجودة

#### أدوات الصيانة:
- **🏗️ تحديث البنية التربوية**: إعادة بناء هيكل الأقسام والمستويات
- **🧮 إعادة حساب المجاميع**: تحديث عدد الطلاب في كل قسم
- **✅ التحقق من صحة البيانات**: فحص البيانات للتأكد من سلامتها

### 🔐 **2. استيراد الرموز السرية**
#### الوظائف المتاحة:
- **🔑 استيراد رموز جديدة**: استيراد كلمات المرور من ملف Excel
- **🔧 إدارة الرموز**: أدوات إدارة وصيانة الرموز السرية

#### تنسيقات الملفات المدعومة:
- **التنسيق القياسي**: (رمز، كلمة المرور)
- **التنسيق الموسع**: (رمز، اسم، كلمة المرور)

#### طرق التحديث:
- **استبدال الرموز**: استبدال جميع الرموز الموجودة
- **إضافة جديد**: إضافة الرموز الجديدة فقط
- **تحديث شامل**: تحديث الموجود وإضافة الجديد

### 👨‍🏫 **3. استيراد بيانات الأساتذة**
#### الوظائف المتاحة:
- **📚 استيراد أسماء الأساتذة**: استيراد قائمة الأساتذة والمواد
- **🔧 إدارة بيانات التدريس**: ربط الأساتذة بالمواد والأقسام

#### أنواع الملفات المدعومة:
- **ملف الحصص**: SeancesEnseignants من منظومة مسار
- **ملف المواد**: قائمة المواد والأساتذة
- **تنسيق مخصص**: تنسيقات أخرى قابلة للتخصيص

### 👥 **4. استيراد بيانات الطلاب**
#### الوظائف المتاحة:
- **📊 استيراد الوافدين والمغادرين**: إدارة حركة الطلاب
- **🔄 تحديث بيانات الطلاب**: تحديث المعلومات الشخصية

#### أنواع البيانات:
- **الطلاب الوافدين**: الطلاب الجدد المنضمين
- **الطلاب المغادرين**: الطلاب المنقولين أو المتسربين
- **البيانات المختلطة**: الوافدين والمغادرين معاً

### 🗄️ **5. إعدادات قاعدة البيانات**
#### أدوات الصيانة:
- **⚡ تحسين قاعدة البيانات**: تحسين الأداء وتقليل الحجم
- **💾 إنشاء نسخة احتياطية**: حفظ نسخة من البيانات
- **🔍 فحص سلامة البيانات**: التحقق من عدم وجود أخطاء
- **🗑️ إعادة تعيين قاعدة البيانات**: حذف جميع البيانات

#### معلومات قاعدة البيانات:
- **حجم قاعدة البيانات**: المساحة المستخدمة
- **عدد الجداول**: إجمالي الجداول
- **إجمالي السجلات**: عدد السجلات الإجمالي
- **آخر تحديث**: تاريخ آخر تعديل

## 🎯 **كيفية الاستخدام**

### 📋 **استيراد اللوائح من منظومة مسار:**

1. **اختيار نوع الاستيراد**:
   ```
   - استيراد جديد: للبداية من الصفر
   - تحديث البيانات: للتحديث الجزئي
   - دمج البيانات: لإضافة بيانات جديدة
   ```

2. **تحديد السنة الدراسية**:
   ```
   - تحديد تلقائي: استخراج من الملف
   - تحديد يدوي: 2024/2025 أو 2025/2026
   ```

3. **اختيار الملف**:
   - انقر على "📂 اختيار ملف مسار"
   - اختر ملف Excel من منظومة مسار
   - انقر على "🚀 بدء الاستيراد"

4. **متابعة التقدم**:
   - شاهد شريط التقدم
   - تابع رسائل السجل
   - انتظر رسالة النجاح

### 🔐 **استيراد الرموز السرية:**

1. **اختيار تنسيق الملف**:
   ```
   - قياسي: عمودين (رمز، كلمة مرور)
   - موسع: ثلاثة أعمدة (رمز، اسم، كلمة مرور)
   ```

2. **تحديد طريقة التحديث**:
   ```
   - استبدال: حذف القديم وإضافة الجديد
   - إضافة: إضافة الجديد فقط
   - تحديث شامل: الأفضل للاستخدام العام
   ```

3. **تنفيذ الاستيراد**:
   - اختر الملف
   - ابدأ عملية الاستيراد
   - تابع النتائج في السجل

### 🗄️ **صيانة قاعدة البيانات:**

#### **النسخ الاحتياطية:**
```bash
# انقر على "💾 إنشاء نسخة احتياطية"
# سيتم تحميل ملف backup_YYYYMMDD.db
# احفظ الملف في مكان آمن
```

#### **فحص سلامة البيانات:**
```bash
# انقر على "🔍 فحص سلامة البيانات"
# ستظهر النتائج في السجل:
# - ✅ قاعدة البيانات سليمة
# - ⚠️ تم العثور على مشاكل
```

#### **تحسين الأداء:**
```bash
# انقر على "⚡ تحسين قاعدة البيانات"
# سيتم:
# - ضغط البيانات
# - إعادة فهرسة الجداول
# - تحسين الاستعلامات
```

## 🔧 **نصائح وإرشادات**

### ✅ **أفضل الممارسات:**

1. **قبل أي استيراد**:
   - ✅ أنشئ نسخة احتياطية
   - ✅ تحقق من تنسيق الملف
   - ✅ اختبر على بيانات تجريبية

2. **أثناء الاستيراد**:
   - ⏳ لا تغلق المتصفح
   - 👁️ تابع رسائل السجل
   - 🔄 انتظر رسالة الإكمال

3. **بعد الاستيراد**:
   - ✅ تحقق من النتائج
   - 🧮 أعد حساب المجاميع إذا لزم الأمر
   - 🔍 فحص سلامة البيانات

### ⚠️ **تحذيرات مهمة:**

1. **إعادة تعيين قاعدة البيانات**:
   ```
   ⚠️ هذا سيحذف جميع البيانات نهائياً
   ⚠️ تأكد من وجود نسخة احتياطية
   ⚠️ لا يمكن التراجع عن هذا الإجراء
   ```

2. **استيراد الرموز السرية**:
   ```
   🔐 احرص على حماية ملف الرموز
   🔐 لا تشارك كلمات المرور
   🔐 استخدم رموز قوية
   ```

## 🆘 **استكشاف الأخطاء**

### ❌ **مشاكل شائعة وحلولها:**

#### **"فشل في استيراد البيانات":**
```bash
✅ تحقق من تنسيق الملف
✅ تأكد من أن الملف غير محمي بكلمة مرور
✅ جرب ملف أصغر للاختبار
```

#### **"لا يمكن الاتصال بقاعدة البيانات":**
```bash
✅ تأكد من تشغيل server.py
✅ تحقق من وجود ملف data.db
✅ أعد تشغيل الخادم
```

#### **"تم العثور على مشاكل في البيانات":**
```bash
✅ استخدم "🔍 فحص سلامة البيانات"
✅ نفذ "🧮 إعادة حساب المجاميع"
✅ جرب "⚡ تحسين قاعدة البيانات"
```

## 📞 **الحصول على المساعدة**

إذا واجهت أي مشاكل:

1. **تحقق من السجل**: ابحث عن رسائل الخطأ
2. **جرب النسخة الاحتياطية**: استرجع نسخة سابقة تعمل
3. **أعد تشغيل النظام**: أغلق وأعد فتح البرنامج
4. **تحقق من الملفات**: تأكد من صحة تنسيق ملفات Excel

---

🎉 **مبروك! الآن لديك نظام شامل لإدارة وتهيئة البرنامج!**
