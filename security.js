/**
 * نظام الحماية والأمان لتطبيق إدارة الطلاب
 */

class SecurityManager {
    constructor() {
        this.sessionTimeout = 30 * 60 * 1000; // 30 دقيقة
        this.maxAttempts = 5;
        this.lockoutTime = 15 * 60 * 1000; // 15 دقيقة
        this.isLocked = false;
        this.attempts = 0;
        this.lastActivity = Date.now();
        
        this.init();
    }

    init() {
        this.setupSessionManagement();
        this.setupDataProtection();
        this.setupAccessControl();
        this.startSecurityChecks();
    }

    // إدارة الجلسات والمهلة الزمنية
    setupSessionManagement() {
        // تتبع النشاط
        document.addEventListener('mousemove', () => this.updateActivity());
        document.addEventListener('keypress', () => this.updateActivity());
        document.addEventListener('click', () => this.updateActivity());
        
        // فحص انتهاء الجلسة كل دقيقة
        setInterval(() => this.checkSession(), 60000);
    }

    updateActivity() {
        this.lastActivity = Date.now();
    }

    checkSession() {
        const now = Date.now();
        if (now - this.lastActivity > this.sessionTimeout) {
            this.lockSystem('انتهت مهلة الجلسة. يرجى إعادة تسجيل الدخول.');
        }
    }

    // حماية البيانات
    setupDataProtection() {
        // منع النسخ من الحقول الحساسة
        const sensitiveFields = ['registration-number', 'institution-phone'];
        sensitiveFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field) {
                field.addEventListener('copy', (e) => {
                    e.preventDefault();
                    this.showSecurityAlert('منع النسخ', 'لا يمكن نسخ البيانات الحساسة');
                });
            }
        });

        // حماية من فحص العناصر
        this.preventInspection();
    }

    preventInspection() {
        // منع الزر الأيمن
        document.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            this.showSecurityAlert('تحذير أمني', 'الوصول غير مسموح');
        });

        // منع اختصارات المطور
        document.addEventListener('keydown', (e) => {
            // F12, Ctrl+Shift+I, Ctrl+U
            if (e.key === 'F12' || 
                (e.ctrlKey && e.shiftKey && e.key === 'I') ||
                (e.ctrlKey && e.key === 'u')) {
                e.preventDefault();
                this.recordSecurityViolation();
            }
        });
    }

    // التحكم في الوصول
    setupAccessControl() {
        // حماية بكلمة مرور للوظائف الحساسة
        this.protectedFunctions = [
            'delete-btn',
            'delete-record-btn',
            'verify-activation-btn'
        ];

        this.protectedFunctions.forEach(btnId => {
            const btn = document.querySelector(`.${btnId}`) || document.getElementById(btnId);
            if (btn) {
                btn.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.requestAuthentication(() => {
                        // تنفيذ الوظيفة الأصلية
                        this.executeProtectedFunction(btnId);
                    });
                });
            }
        });
    }

    requestAuthentication(callback) {
        const password = prompt('🔐 أدخل كلمة المرور للوصول لهذه الوظيفة:');
        
        if (!password) {
            this.showSecurityAlert('تم الإلغاء', 'تم إلغاء العملية');
            return;
        }

        if (this.verifyPassword(password)) {
            this.attempts = 0;
            callback();
        } else {
            this.attempts++;
            this.showSecurityAlert('خطأ', `كلمة مرور خاطئة (المحاولة ${this.attempts}/${this.maxAttempts})`);
            
            if (this.attempts >= this.maxAttempts) {
                this.lockSystem('تم تجاوز عدد المحاولات المسموح. تم قفل النظام.');
            }
        }
    }

    verifyPassword(password) {
        // كلمة مرور افتراضية - يجب تغييرها
        const defaultPassword = 'admin123';
        
        // يمكن تطوير هذا ليستخدم hash أو قاعدة بيانات
        return password === defaultPassword;
    }

    // قفل النظام
    lockSystem(reason) {
        this.isLocked = true;
        
        // إنشاء طبقة قفل
        const lockOverlay = document.createElement('div');
        lockOverlay.id = 'security-lock';
        lockOverlay.innerHTML = `
            <div class="lock-screen">
                <div class="lock-icon">🔒</div>
                <h2>تم قفل النظام</h2>
                <p>${reason}</p>
                <p>وقت القفل: ${new Date().toLocaleString('ar-EG')}</p>
                <button onclick="location.reload()" class="unlock-btn">🔄 إعادة تحميل</button>
            </div>
        `;
        
        // إضافة الستايلات
        lockOverlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.9);
            z-index: 10000;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            text-align: center;
        `;

        document.body.appendChild(lockOverlay);
        
        // منع أي تفاعل
        document.body.style.overflow = 'hidden';
    }

    // تسجيل انتهاكات الأمان
    recordSecurityViolation() {
        const violation = {
            timestamp: new Date().toISOString(),
            type: 'unauthorized_access_attempt',
            userAgent: navigator.userAgent,
            url: window.location.href
        };

        console.warn('🚨 انتهاك أمني:', violation);
        
        // يمكن إرسال هذا للخادم
        // fetch('/api/security-log', { method: 'POST', body: JSON.stringify(violation) });
    }

    showSecurityAlert(title, message) {
        const alert = document.createElement('div');
        alert.className = 'security-alert';
        alert.innerHTML = `
            <div class="alert-content">
                <h3>🛡️ ${title}</h3>
                <p>${message}</p>
                <button onclick="this.parentElement.parentElement.remove()">موافق</button>
            </div>
        `;
        
        alert.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #ff4444;
            color: white;
            padding: 20px;
            border-radius: 10px;
            z-index: 9999;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        `;

        document.body.appendChild(alert);
        
        // إزالة تلقائية بعد 5 ثواني
        setTimeout(() => {
            if (alert.parentElement) {
                alert.remove();
            }
        }, 5000);
    }

    executeProtectedFunction(functionId) {
        this.showSecurityAlert('تم التحقق', `تم تنفيذ العملية: ${functionId}`);
        // هنا يمكن تنفيذ الوظيفة الفعلية
    }

    startSecurityChecks() {
        // فحوصات دورية كل 5 دقائق
        setInterval(() => {
            if (this.isLocked) return;
            
            // فحص حالة النظام
            this.performSecurityCheck();
        }, 5 * 60 * 1000);
    }

    performSecurityCheck() {
        // فحص integráty البيانات
        const criticalElements = ['students-table', 'institution-form'];
        
        criticalElements.forEach(elementId => {
            const element = document.getElementById(elementId);
            if (!element) {
                this.recordSecurityViolation();
                this.showSecurityAlert('تحذير', `عنصر مهم مفقود: ${elementId}`);
            }
        });
    }

    // تصدير البيانات بشكل آمن
    exportSecureData(data, filename) {
        // إضافة watermark أو تشفير خفيف
        const secureData = {
            ...data,
            exportedAt: new Date().toISOString(),
            exportedBy: 'نظام إدارة الطلاب',
            security: 'protected'
        };

        const blob = new Blob([JSON.stringify(secureData, null, 2)], 
            { type: 'application/json' });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.click();
        URL.revokeObjectURL(url);
    }
}

// تهيئة نظام الحماية
document.addEventListener('DOMContentLoaded', () => {
    window.securityManager = new SecurityManager();
});

// منع التلاعب بـ Console
(function() {
    'use strict';
    const devtools = {
        open: false,
        orientation: null
    };
    
    const threshold = 160;
    
    setInterval(function() {
        if (window.outerHeight - window.innerHeight > threshold || 
            window.outerWidth - window.innerWidth > threshold) {
            if (!devtools.open) {
                devtools.open = true;
                console.clear();
                console.log('%c🚨 تحذير أمني', 'color: red; font-size: 20px; font-weight: bold;');
                console.log('%cاستخدام أدوات المطور غير مسموح', 'color: red; font-size: 14px;');
            }
        } else {
            devtools.open = false;
        }
    }, 500);
})();
