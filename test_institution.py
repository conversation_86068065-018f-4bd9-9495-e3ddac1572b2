"""
اختبار ربط بيانات المؤسسة
"""
import sqlite3
import os

def test_institution_data():
    """اختبار قراءة وكتابة بيانات المؤسسة"""
    
    db_path = os.path.join(os.path.dirname(__file__), "data.db")
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row  # لتسهيل الوصول للأعمدة بالاسم
        cursor = conn.cursor()
        
        print("🔍 اختبار قراءة بيانات المؤسسة...")
        
        # قراءة البيانات الحالية
        cursor.execute("""
            SELECT المؤسسة, السنة_الدراسية, اسم_المسؤول, المدينة, رقم_التسجيل, رقم_الهاتف, ImagePath1
            FROM بيانات_المؤسسة 
            LIMIT 1
        """)
        
        result = cursor.fetchone()
        
        if result:
            print("✅ تم العثور على بيانات المؤسسة:")
            print(f"   المؤسسة: {result['المؤسسة']}")
            print(f"   السنة الدراسية: {result['السنة_الدراسية']}")
            print(f"   المسؤول: {result['اسم_المسؤول']}")
            print(f"   المدينة: {result['المدينة']}")
            print(f"   رقم التسجيل: {result['رقم_التسجيل']}")
            print(f"   رقم الهاتف: {result['رقم_الهاتف']}")
            print(f"   مسار الشعار: {result['ImagePath1']}")
        else:
            print("❌ لم يتم العثور على بيانات المؤسسة")
        
        # اختبار تحديث البيانات
        print("\n🔄 اختبار تحديث البيانات...")
        
        test_data = {
            'المؤسسة': 'مؤسسة تعليمية تجريبية',
            'السنة_الدراسية': '2024/2025',
            'اسم_المسؤول': 'محمد أحمد',
            'المدينة': 'الرباط',
            'رقم_التسجيل': 'TEST123456',
            'رقم_الهاتف': '0612345678'
        }
        
        cursor.execute("""
            UPDATE بيانات_المؤسسة 
            SET المؤسسة = ?, السنة_الدراسية = ?, اسم_المسؤول = ?, 
                المدينة = ?, رقم_التسجيل = ?, رقم_الهاتف = ?
            WHERE rowid = 1
        """, (
            test_data['المؤسسة'], test_data['السنة_الدراسية'], 
            test_data['اسم_المسؤول'], test_data['المدينة'], 
            test_data['رقم_التسجيل'], test_data['رقم_الهاتف']
        ))
        
        conn.commit()
        
        # قراءة البيانات المحدثة
        cursor.execute("""
            SELECT المؤسسة, السنة_الدراسية, اسم_المسؤول, المدينة, رقم_التسجيل, رقم_الهاتف
            FROM بيانات_المؤسسة 
            LIMIT 1
        """)
        
        updated_result = cursor.fetchone()
        
        if updated_result:
            print("✅ تم تحديث البيانات بنجاح:")
            print(f"   المؤسسة: {updated_result['المؤسسة']}")
            print(f"   السنة الدراسية: {updated_result['السنة_الدراسية']}")
            print(f"   المسؤول: {updated_result['اسم_المسؤول']}")
            print(f"   المدينة: {updated_result['المدينة']}")
            print(f"   رقم التسجيل: {updated_result['رقم_التسجيل']}")
            print(f"   رقم الهاتف: {updated_result['رقم_الهاتف']}")
        
        print("\n✅ اختبار الربط مع قاعدة البيانات نجح!")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار البيانات: {e}")
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    test_institution_data()
