"""
ملف استيراد اللوائح من منظومة مسار
====================

هذا الملف مخصص لاستيراد البيانات من منظومة مسار باللغة العربية
يتضمن جميع وظائف معالجة البيانات وتحديث قاعدة البيانات

المؤلف: تم فصل هذا الكود من sub1_window.py لتحسين التنظيم
التاريخ: 2024
"""

import sqlite3
import pandas as pd
from PyQt5.QtWidgets import QApplication, QProgressDialog, QMessageBox, QFileDialog
from PyQt5.QtCore import Qt, QDateTime
from PyQt5.QtGui import QIcon
import os

class MasarImporter:
    """
    فئة مخصصة لاستيراد البيانات من منظومة مسار
    تتضمن جميع الوظائف اللازمة لمعالجة وتحديث البيانات
    """
    
    def __init__(self, log_callback=None, parent_widget=None):
        """
        تهيئة كائن المستورد
        
        Args:
            log_callback: دالة تسجيل الأحداث
            parent_widget: النافذة الأب لعرض مربعات الحوار
        """
        self.log_callback = log_callback
        self.parent_widget = parent_widget
        self.db_path = "data.db"
        
    def log(self, message, status="info"):
        """تسجيل الرسائل مع الطوابع الزمنية"""
        if self.log_callback:
            self.log_callback(message, status)
    
    def import_masar_data(self):
        """
        الدالة الرئيسية لاستيراد البيانات من منظومة مسار
        تدير العملية الكاملة من اختيار الملف حتى حفظ البيانات
        """
        self.log("جاري استيراد البيانات من منظومة مسار...", "progress")
        
        conn = None
        try:
            # إنشاء اتصال بقاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # حذف السجلات الافتراضية قبل الاستيراد
            self._delete_default_records(cursor)
            
            # اختيار ملف Excel
            file_path = self._select_excel_file()
            if not file_path:
                self.log("تم إلغاء عملية الاستيراد", "warning")
                return False
            
            # التحقق من اسم الملف
            if not self._validate_file_name(file_path):
                return False
            
            # إنشاء شريط التقدم
            progress_dialog = self._create_progress_dialog()
            
            # قراءة ومعالجة البيانات
            sheets_dict = pd.read_excel(file_path, sheet_name=None)
            current_academic_year = self._extract_academic_year(sheets_dict)
            
            # تحديث شريط التقدم
            progress_dialog.setValue(10)
            progress_dialog.setLabelText("جاري تحضير البيانات...")
            QApplication.processEvents()
            
            # تحضير البيانات
            self._prepare_sheets_data(sheets_dict, progress_dialog)
            
            # دمج وحفظ البيانات
            combined_df = pd.concat(sheets_dict.values(), ignore_index=True)
            combined_df.to_sql("السجل الاولي", conn, if_exists='append', index=False)
            
            # تحديث قواعد البيانات
            progress_dialog.setValue(50)
            progress_dialog.setLabelText("جاري تحديث قواعد البيانات...")
            QApplication.processEvents()
            
            self._process_data_silently(cursor, current_academic_year, progress_dialog)
            
            # الانتهاء والتنظيف
            conn.commit()
            progress_dialog.setValue(100)
            progress_dialog.setLabelText("تم الانتهاء من استيراد البيانات بنجاح!")
            QApplication.processEvents()
            
            # عرض ملخص النتائج
            self._display_import_summary(current_academic_year)
            
            # المهام الإضافية
            self._update_levels_order()
            self._assign_all_to_guard1()
            self._update_sub11_window()
            
            self.log("✅ تم استيراد البيانات بنجاح!", "success")
            return True
            
        except Exception as e:
            self.log(f"حدث خطأ أثناء استيراد البيانات: {str(e)}", "error")
            self._show_error_dialog(f"حدث خطأ أثناء استيراد البيانات:\n{str(e)}")
            return False
        finally:
            if conn:
                try:
                    conn.close()
                except:
                    pass
    
    def _delete_default_records(self, cursor):
        """حذف السجلات الافتراضية قبل الاستيراد"""
        self.log("جاري حذف السجلات الافتراضية قبل الاستيراد...", "progress")
        try:
            # حذف من جدول السجل_العام
            cursor.execute("DELETE FROM السجل_العام WHERE الرمز = 'A12345678'")
            deleted_count = cursor.rowcount
            if deleted_count > 0:
                self.log(f"تم حذف {deleted_count} سجل افتراضي من جدول السجل_العام", "info")
            
            # حذف من جدول اللوائح
            cursor.execute("DELETE FROM اللوائح WHERE الرمز = 'A12345678'")
            deleted_count = cursor.rowcount
            if deleted_count > 0:
                self.log(f"تم حذف {deleted_count} سجل افتراضي من جدول اللوائح", "info")
            
            # حذف جدول السجل الاولي
            cursor.execute("DROP TABLE IF EXISTS 'السجل الاولي'")
            
        except Exception as e:
            self.log(f"حدث خطأ أثناء حذف السجلات الافتراضية: {str(e)}", "warning")
    
    def _select_excel_file(self):
        """اختيار ملف Excel للاستيراد"""
        file_path, _ = QFileDialog.getOpenFileName(
            self.parent_widget, 
            "اختر ملف الاكسل", 
            "", 
            "Excel Files (*.xlsx *.xls)"
        )
        return file_path
    
    def _validate_file_name(self, file_path):
        """التحقق من صحة اسم الملف"""
        file_name = file_path.split('/')[-1].split('\\')[-1]
        if "ListEleve" not in file_name:
            warning_message = f"الملف {file_name} لا يحتوي على العبارة 'ListEleve' في اسمه. قد لا يكون هذا ملف لوائح منظومة مسار."
            self.log(warning_message, "warning")
            
            # عرض رسالة تأكيد للمستخدم
            return self._show_confirmation_dialog(warning_message)
        return True
    
    def _show_confirmation_dialog(self, warning_message):
        """عرض مربع حوار التأكيد"""
        msg_box = QMessageBox(self.parent_widget)
        msg_box.setWindowTitle("تنبيه: ملف غير معروف")
        
        # إضافة أيقونة البرنامج
        icon_path = "01.ico"
        if os.path.exists(icon_path):
            msg_box.setWindowIcon(QIcon(icon_path))
        
        # تعيين النص الرئيسي
        msg_box.setText("<p style='font-family: Calibri; font-size: 13pt; color: #0D47A1; font-weight: bold;'>تنبيه: ملف غير معروف</p>")
        msg_box.setInformativeText(f"<p style='font-family: Calibri; font-size: 11pt;'>{warning_message}</p><p style='font-family: Calibri; font-size: 11pt;'>هل تريد الاستمرار في استيراد هذا الملف على أي حال؟</p>")
        msg_box.setIcon(QMessageBox.Warning)
        
        # إنشاء أزرار مخصصة
        continue_button = msg_box.addButton("نعم، استمر في الاستيراد", QMessageBox.YesRole)
        cancel_button = msg_box.addButton("لا، إلغاء الاستيراد", QMessageBox.NoRole)
        
        # تخصيص أنماط الأزرار
        self._style_buttons(continue_button, cancel_button)
        msg_box.setDefaultButton(cancel_button)
        
        # عرض مربع الحوار
        msg_box.exec_()
        
        if msg_box.clickedButton() == cancel_button:
            self.log("تم إلغاء عملية الاستيراد بناءً على طلب المستخدم", "info")
            return False
        else:
            self.log("تم اختيار الاستمرار في الاستيراد رغم التحذير", "info")
            return True
    
    def _style_buttons(self, continue_button, cancel_button):
        """تنسيق أزرار مربع الحوار"""
        continue_button.setStyleSheet("""
            QPushButton {
                font-family: Calibri;
                font-size: 13pt;
                font-weight: bold;
                color: white;
                background-color: #0D47A1;
                border: none;
                border-radius: 5px;
                padding: 5px 15px;
                min-width: 140px;
            }
            QPushButton:hover {
                background-color: #1565C0;
            }
            QPushButton:pressed {
                background-color: #0D47A1;
            }
        """)
        
        cancel_button.setStyleSheet("""
            QPushButton {
                font-family: Calibri;
                font-size: 13pt;
                font-weight: bold;
                color: #0D47A1;
                background-color: #E3F2FD;
                border: 1px solid #0D47A1;
                border-radius: 5px;
                padding: 5px 15px;
                min-width: 140px;
            }
            QPushButton:hover {
                background-color: #BBDEFB;
            }
            QPushButton:pressed {
                background-color: #E3F2FD;
            }
        """)
    
    def _create_progress_dialog(self):
        """إنشاء نافذة شريط التقدم"""
        progress_dialog = QProgressDialog(
            "جاري استيراد البيانات من منظومة مسار...", 
            None, 0, 100, 
            self.parent_widget
        )
        progress_dialog.setWindowTitle("استيراد البيانات")
        
        # إضافة أيقونة البرنامج
        icon_path = "01.ico"
        if os.path.exists(icon_path):
            progress_dialog.setWindowIcon(QIcon(icon_path))
        
        # تخصيص مظهر شريط التقدم
        progress_dialog.setStyleSheet("""
            QProgressDialog {
                background-color: white;
                font-family: Calibri;
                font-size: 13pt;
                font-weight: bold;
                color: #0D47A1;
            }
            QProgressBar {
                border: 2px solid #0D47A1;
                border-radius: 5px;
                text-align: center;
                background-color: #E3F2FD;
                min-height: 20px;
            }
            QProgressBar::chunk {
                background-color: #0D47A1;
                width: 10px;
                margin: 0.5px;
            }
        """)
        
        # تعيين خصائص شريط التقدم
        progress_dialog.setMinimumDuration(0)
        progress_dialog.setWindowModality(Qt.WindowModal)
        progress_dialog.setAutoClose(True)
        progress_dialog.setAutoReset(True)
        progress_dialog.setMinimumWidth(400)
        progress_dialog.setCancelButton(None)
        
        # عرض شريط التقدم
        progress_dialog.setValue(0)
        progress_dialog.show()
        QApplication.processEvents()
        
        return progress_dialog
    
    def _extract_academic_year(self, sheets_dict):
        """استخراج السنة الدراسية من الملف"""
        current_academic_year = None
        
        for sheet_name, df in sheets_dict.items():
            if "Unnamed: 6" in df.columns and len(df) > 5:
                current_academic_year = df.iloc[5]["Unnamed: 6"]
                break
        
        return current_academic_year
    
    def _prepare_sheets_data(self, sheets_dict, progress_dialog):
        """تحضير بيانات جميع الشيتات"""
        total_sheets = len(sheets_dict)
        
        for i, (sheet_name, df) in enumerate(sheets_dict.items()):
            # تحديث شريط التقدم
            progress_percent = 10 + int((i / total_sheets) * 20)
            progress_dialog.setValue(progress_percent)
            progress_dialog.setLabelText(f"جاري معالجة القسم {sheet_name}...")
            QApplication.processEvents()
            
            # معالجة بيانات الشيت
            level_rows = df[df["Unnamed: 0"].astype(str).str.strip() == ": المستوى"]
            level_value = level_rows.iloc[0]["Unnamed: 2"] if not level_rows.empty else None
            year_value = df.iloc[5]["Unnamed: 6"] if "Unnamed: 6" in df.columns and len(df) > 5 else None
            
            df["القسم"] = sheet_name
            df["المستوى"] = level_value
            df["السنة الدراسية"] = year_value
    
    def _process_data_silently(self, cursor, academic_year, progress_dialog=None):
        """معالجة البيانات بهدوء بدون عرض كل خطوة في السجل"""
        try:
            # تحديث بيانات المؤسسة
            if progress_dialog:
                progress_dialog.setValue(55)
                progress_dialog.setLabelText("جاري تحديث بيانات المؤسسة...")
                QApplication.processEvents()
            self._update_school_info(cursor)
            
            # إنشاء وتحديث اللوائح
            if progress_dialog:
                progress_dialog.setValue(60)
                progress_dialog.setLabelText("جاري إنشاء وتحديث اللوائح...")
                QApplication.processEvents()
            self._create_and_update_lists_table(cursor, academic_year)
            
            # تحديث السجل العام والبنية التربوية
            if progress_dialog:
                progress_dialog.setValue(80)
                progress_dialog.setLabelText("جاري تحديث السجل العام والبنية التربوية...")
                QApplication.processEvents()
            self._update_structure_tables(cursor, academic_year, progress_dialog)
            
        except Exception as e:
            raise Exception(f"حدث خطأ أثناء معالجة البيانات: {str(e)}")
    
    def _update_school_info(self, cursor):
        """تحديث بيانات المؤسسة"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS بيانات_المؤسسة (
                الأكاديمية TEXT,
                المديرية TEXT,
                الجماعة TEXT,
                المؤسسة TEXT,
                السنة_الدراسية TEXT,
                البلدة TEXT,
                المدير TEXT,
                الحارس_العام TEXT,
                السلك TEXT,
                رقم_الحراسة TEXT,
                رقم_التسجيل TEXT,
                الأسدس TEXT,
                ImagePath1 TEXT
            )
        """)
        
        # استخراج البيانات من السجل الأولي
        academy_row = self._get_row_data(cursor, 3)
        directorate_row = self._get_row_data(cursor, 4)
        year_row = self._get_row_data(cursor, 5)
        
        # التحقق من وجود سجل
        cursor.execute("SELECT COUNT(*) FROM بيانات_المؤسسة")
        if cursor.fetchone()[0] == 0:
            cursor.execute("INSERT INTO بيانات_المؤسسة DEFAULT VALUES")
        
        # تحديث البيانات مع الحفاظ على القيم الموجودة
        if academy_row and directorate_row and year_row:
            current_values = self._get_current_institution_values(cursor)
            self._update_institution_data(cursor, academy_row, directorate_row, year_row, current_values)
    
    def _get_row_data(self, cursor, offset):
        """الحصول على بيانات صف معين من السجل الأولي"""
        cursor.execute(f"""
            SELECT "Unnamed: 2", "Unnamed: 2", "Unnamed: 6"
            FROM "السجل الاولي"
            LIMIT 1 OFFSET {offset}
        """)
        return cursor.fetchone()
    
    def _get_current_institution_values(self, cursor):
        """الحصول على القيم الحالية لبيانات المؤسسة"""
        cursor.execute("""
            SELECT البلدة, المدير, الحارس_العام, السلك, رقم_الحراسة, رقم_التسجيل, ImagePath1
            FROM بيانات_المؤسسة
            WHERE rowid=1
        """)
        current_values = cursor.fetchone()
        
        if current_values:
            return {
                'town': current_values[0] or '',
                'director': current_values[1] or '',
                'guard': current_values[2] or '',
                'cycle': current_values[3] or '',
                'guard_number': current_values[4] or '',
                'reg_number': current_values[5] or '',
                'image_path': current_values[6] or ''
            }
        return {
            'town': '', 'director': '', 'guard': '', 'cycle': '',
            'guard_number': '', 'reg_number': '', 'image_path': ''
        }
    
    def _update_institution_data(self, cursor, academy_row, directorate_row, year_row, current_values):
        """تحديث بيانات المؤسسة في قاعدة البيانات"""
        cursor.execute("""
            UPDATE بيانات_المؤسسة
            SET الأكاديمية=?, المديرية=?, الجماعة=?, المؤسسة=?,
                السنة_الدراسية=?, الأسدس=?, البلدة=?, المدير=?,
                الحارس_العام=?, السلك=?, رقم_الحراسة=?, رقم_التسجيل=?, ImagePath1=?
            WHERE rowid=1
        """, (
            academy_row[0] or '',
            directorate_row[0] or '',
            academy_row[2] or '',
            directorate_row[2] or '',
            year_row[0] or '',
            'الأول',
            current_values['town'],
            current_values['director'],
            current_values['guard'],
            current_values['cycle'],
            current_values['guard_number'],
            current_values['reg_number'],
            current_values['image_path']
        ))
    
    def _create_and_update_lists_table(self, cursor, academic_year):
        """إنشاء وتحديث جدول اللوائح"""
        # إنشاء جدول اللوائح
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS 'اللوائح' (
                'السنة_الدراسية' TEXT, 
                'القسم' TEXT, 
                'المستوى' TEXT, 
                'الرمز' TEXT, 
                'رت' TEXT, 
                'مجموع التلاميذ' INTEGER DEFAULT 0, 
                PRIMARY KEY('السنة_الدراسية', 'الرمز')
            )
        """)
        
        # حذف السجلات القديمة
        if academic_year:
            cursor.execute("DELETE FROM اللوائح WHERE السنة_الدراسية = ?", (academic_year,))
        cursor.execute("DELETE FROM اللوائح WHERE السنة_الدراسية = '2024/2025'")
        
        # إضافة البيانات الجديدة
        cursor.execute("""
            INSERT OR IGNORE INTO "اللوائح" ("السنة_الدراسية", "القسم", "المستوى", "الرمز", "رت")
            SELECT "السنة الدراسية", "القسم", "المستوى", "Unnamed: 1", "Unnamed: 0"
            FROM "السجل الاولي"
        """)
        
        # تنظيف البيانات
        cursor.execute("""
            DELETE FROM "اللوائح"
            WHERE "الرمز" IS NULL OR TRIM("الرمز") = '' OR "الرمز" = 'الرمز'
        """)
        
        # تحديث مجموع التلاميذ
        self._update_student_count(cursor, academic_year)
    
    def _update_student_count(self, cursor, academic_year):
        """تحديث مجموع التلاميذ في جدول اللوائح"""
        if academic_year:
            cursor.execute("""
                UPDATE "اللوائح" as l1
                SET "مجموع التلاميذ" = (
                    SELECT COUNT(*)
                    FROM "اللوائح" AS l2
                    WHERE l2."القسم" = l1."القسم"
                    AND l2."السنة_الدراسية" = l1."السنة_الدراسية"
                )
                WHERE l1."السنة_الدراسية" = ?
            """, (academic_year,))
            
            self.log(f"تم تحديث مجموع التلاميذ للسنة الدراسية {academic_year} فقط", "info")
        else:
            cursor.execute("""
                UPDATE "اللوائح" as l1
                SET "مجموع التلاميذ" = (
                    SELECT COUNT(*)
                    FROM "اللوائح" AS l2
                    WHERE l2."القسم" = l1."القسم"
                    AND l2."السنة_الدراسية" = l1."السنة_الدراسية"
                )
            """)
            
            self.log("تحذير: تم تحديث مجموع التلاميذ لجميع السنوات الدراسية لأن السنة الدراسية غير محددة", "warning")
    
    def _update_structure_tables(self, cursor, academic_year, progress_dialog=None):
        """تحديث جداول البنية التربوية والسجل العام"""
        # تحديث السجل العام
        if progress_dialog:
            progress_dialog.setValue(82)
            progress_dialog.setLabelText("جاري تحديث جدول السجل العام...")
            QApplication.processEvents()
        
        self._update_general_record(cursor)
        
        # تحديث البنية التربوية
        if progress_dialog:
            progress_dialog.setValue(86)
            progress_dialog.setLabelText("جاري تحديث البنية التربوية...")
            QApplication.processEvents()
        
        self._update_educational_structure(cursor, academic_year, progress_dialog)
    
    def _update_general_record(self, cursor):
        """تحديث جدول السجل العام"""
        # إدراج السجلات الجديدة
        cursor.execute("""
            INSERT OR IGNORE INTO "السجل_العام"
                ("الرمز", "الاسم_والنسب", "النوع", "تاريخ_الازدياد", "مكان_الازدياد")
            SELECT "Unnamed: 1",
                   "Unnamed: 2" || ' ' || "Unnamed: 3",
                   "Unnamed: 4",
                   "Unnamed: 5",
                   "Unnamed: 6"
            FROM "السجل الاولي"
            WHERE "Unnamed: 1" IS NOT NULL
              AND TRIM("Unnamed: 1") <> ''
              AND "Unnamed: 1" <> 'الرمز'
        """)
        
        # تحديث السجلات الموجودة
        cursor.execute("""
            UPDATE "السجل_العام"
            SET
                "الاسم_والنسب" = (
                    SELECT "Unnamed: 2" || ' ' || "Unnamed: 3"
                    FROM "السجل الاولي"
                    WHERE "Unnamed: 1" = "السجل_العام"."الرمز"
                ),
                "النوع" = (
                    SELECT "Unnamed: 4"
                    FROM "السجل الاولي"
                    WHERE "Unnamed: 1" = "السجل_العام"."الرمز"
                ),
                "تاريخ_الازدياد" = (
                    SELECT "Unnamed: 5"
                    FROM "السجل الاولي"
                    WHERE "Unnamed: 1" = "السجل_العام"."الرمز"
                ),
                "مكان_الازدياد" = (
                    SELECT "Unnamed: 6"
                    FROM "السجل الاولي"
                    WHERE "Unnamed: 1" = "السجل_العام"."الرمز"
                )
            WHERE EXISTS (
                SELECT 1
                FROM "السجل الاولي"
                WHERE "Unnamed: 1" = "السجل_العام"."الرمز"
            )
        """)
    
    def _update_educational_structure(self, cursor, academic_year, progress_dialog):
        """تحديث جدول البنية التربوية"""
        # إدراج البيانات الجديدة
        cursor.execute("""
            INSERT OR IGNORE INTO "البنية_التربوية" ("السنة_الدراسية", "القسم", "المستوى")
            SELECT DISTINCT "السنة_الدراسية", "القسم", "المستوى"
            FROM "اللوائح"
        """)
        
        # إضافة عمود مجموع التلاميذ إذا لم يكن موجوداً
        try:
            cursor.execute('ALTER TABLE "البنية_التربوية" ADD COLUMN "مجموع_التلاميذ" INTEGER DEFAULT 0')
        except sqlite3.OperationalError:
            pass
        
        if progress_dialog:
            progress_dialog.setValue(88)
            progress_dialog.setLabelText("جاري تحديث مجموع التلاميذ في البنية التربوية...")
            QApplication.processEvents()
        
        # تحديث مجموع التلاميذ
        if academic_year:
            cursor.execute("""
                UPDATE "البنية_التربوية"
                SET "مجموع_التلاميذ" = (
                    SELECT COUNT(*)
                    FROM "اللوائح" AS l
                    WHERE l."السنة_الدراسية" = "البنية_التربوية"."السنة_الدراسية"
                      AND l."القسم" = "البنية_التربوية"."القسم"
                      AND l."المستوى" = "البنية_التربوية"."المستوى"
                )
                WHERE "السنة_الدراسية" = ?
            """, (academic_year,))
            
            # حذف الأقسام التي لا توجد في جدول اللوائح
            cursor.execute("""
                DELETE FROM "البنية_التربوية"
                WHERE "السنة_الدراسية" = ?
                AND NOT EXISTS (
                    SELECT 1
                    FROM "اللوائح"
                    WHERE "اللوائح"."السنة_الدراسية" = "البنية_التربوية"."السنة_الدراسية"
                    AND "اللوائح"."القسم" = "البنية_التربوية"."القسم"
                )
            """, (academic_year,))
        else:
            cursor.execute("""
                UPDATE "البنية_التربوية"
                SET "مجموع_التلاميذ" = (
                    SELECT COUNT(*)
                    FROM "اللوائح" AS l
                    WHERE l."السنة_الدراسية" = "البنية_التربوية"."السنة_الدراسية"
                      AND l."القسم" = "البنية_التربوية"."القسم"
                      AND l."المستوى" = "البنية_التربوية"."المستوى"
                )
            """)
        
        if progress_dialog:
            progress_dialog.setValue(89)
            progress_dialog.setLabelText("اكتمل تحديث البيانات!")
            QApplication.processEvents()
    
    def _display_import_summary(self, academic_year):
        """عرض ملخص لنتائج الاستيراد"""
        conn = None
        try:
            self.log("✅ تم استيراد البيانات بنجاح!", "success")
            self.log("جاري إعداد ملخص النتائج...", "progress")
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # عرض السنة الدراسية
            if academic_year:
                self.log(f"📅 السنة الدراسية: {academic_year}", "info")
            
            # عرض أسماء الأقسام المستوردة
            cursor.execute("""
                SELECT DISTINCT القسم
                FROM اللوائح
                WHERE السنة_الدراسية = ?
                ORDER BY القسم
            """, (academic_year,))
            sections = [row[0] for row in cursor.fetchall()]
            
            if sections:
                self.log(f"📚 الأقسام المستوردة للسنة الدراسية {academic_year}:", "info")
                section_text = ", ".join(sections)
                self.log(f"   {section_text}", "info")
                self.log(f"🔢 مجموع الأقسام المستوردة: {len(sections)}", "info")
            
            # مجموع التلاميذ
            cursor.execute("""
                SELECT COUNT(*)
                FROM اللوائح
                WHERE السنة_الدراسية = ?
            """, (academic_year,))
            total_students = cursor.fetchone()[0] or 0
            self.log(f"👨‍👩‍👧‍👦 مجموع التلاميذ: {total_students}", "info")
            
            # المقارنة بين الجداول
            cursor.execute("""
                SELECT COUNT(*) FROM "البنية_التربوية"
                WHERE "السنة_الدراسية" = ?
                AND NOT EXISTS (
                    SELECT 1
                    FROM "اللوائح"
                    WHERE "اللوائح"."السنة_الدراسية" = "البنية_التربوية"."السنة_الدراسية"
                    AND "اللوائح"."القسم" = "البنية_التربوية"."القسم"
                )
            """, (academic_year,))
            removed_sections = cursor.fetchone()[0] or 0
            
            if removed_sections > 0:
                self.log(f"🧹 تم حذف {removed_sections} من الأقسام غير المتطابقة من جدول البنية_التربوية", "info")
            
            self.log("نتمنى لك التوفيق والنجاح في عملك! 🌟", "success")
            
        except Exception as e:
            self.log(f"خطأ في عرض ملخص الاستيراد: {str(e)}", "error")
        finally:
            if conn:
                try:
                    conn.close()
                except:
                    pass
    
    def _update_levels_order(self):
        """تحديث ترتيب المستويات في قاعدة البيانات"""
        self.log("جاري تحديث ترتيب المستويات...", "progress")
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # إضافة عمود ترتيب_المستويات إذا لم يكن موجوداً
            try:
                cursor.execute("ALTER TABLE البنية_التربوية ADD COLUMN ترتيب_المستويات INTEGER DEFAULT 99")
            except sqlite3.OperationalError:
                pass
            
            # تعيين قيمة افتراضية
            cursor.execute("UPDATE البنية_التربوية SET ترتيب_المستويات = 99")
            
            # تحديث ترتيب المستويات
            cursor.execute("UPDATE البنية_التربوية SET ترتيب_المستويات = 1 WHERE المستوى LIKE '%جذع مشترك%' OR المستوى LIKE '%الجذع%'")
            cursor.execute("UPDATE البنية_التربوية SET ترتيب_المستويات = 2 WHERE المستوى LIKE '%الأولى بكالوريا%' OR المستوى LIKE '%سنة أولى بكالوريا%'")
            cursor.execute("UPDATE البنية_التربوية SET ترتيب_المستويات = 3 WHERE المستوى LIKE '%الثانية بكالوريا%' OR المستوى LIKE '%سنة ثانية بكالوريا%'")
            
            # تحديث ترتيب المستويات الأخرى
            cursor.execute("UPDATE البنية_التربوية SET ترتيب_المستويات = 10 WHERE المستوى LIKE '%الأولى%' AND ترتيب_المستويات = 99")
            cursor.execute("UPDATE البنية_التربوية SET ترتيب_المستويات = 11 WHERE المستوى LIKE '%الثانية%' AND ترتيب_المستويات = 99")
            cursor.execute("UPDATE البنية_التربوية SET ترتيب_المستويات = 12 WHERE المستوى LIKE '%الثالثة%' AND ترتيب_المستويات = 99")
            
            conn.commit()
            conn.close()
            
            self.log("✅ تم تحديث ترتيب المستويات بنجاح!", "success")
        except Exception as e:
            self.log(f"❌ خطأ في تحديث ترتيب المستويات: {str(e)}", "error")
    
    def _assign_all_to_guard1(self):
        """تعيين جميع الأقسام للسنة الدراسية الحالية إلى حراسة رقم 1"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # التحقق من وجود عمود الأقسام_المسندة
            try:
                cursor.execute("ALTER TABLE البنية_التربوية ADD COLUMN الأقسام_المسندة TEXT DEFAULT ''")
            except sqlite3.OperationalError:
                pass
            
            # الحصول على السنة الدراسية الحالية
            cursor.execute("SELECT DISTINCT السنة_الدراسية FROM اللوائح ORDER BY السنة_الدراسية DESC LIMIT 1")
            result = cursor.fetchone()
            
            if result and result[0]:
                current_year = result[0]
                self.log(f"جاري تعيين جميع أقسام السنة الدراسية {current_year} إلى حراسة رقم 1...", "progress")
                
                cursor.execute("UPDATE البنية_التربوية SET الأقسام_المسندة = 'حراسة رقم 1' WHERE السنة_الدراسية = ?", (current_year,))
                updated_count = cursor.rowcount
                conn.commit()
                
                self.log(f"✅ تم تعيين {updated_count} قسم إلى حراسة رقم 1 بنجاح!", "success")
            else:
                self.log("⚠️ لم يتم العثور على سنة دراسية حالية.", "warning")
            
            conn.close()
        except Exception as e:
            self.log(f"❌ خطأ في تعيين الأقسام لحراسة رقم 1: {str(e)}", "error")
    
    def _update_sub11_window(self):
        """تحديث نافذة إدارة الأقسام والحراسة"""
        self.log("جاري تحديث نافذة إدارة الأقسام والحراسة...", "progress")
        try:
            # البحث عن نافذة Sub11Window المفتوحة
            sub11_window = None
            for widget in QApplication.topLevelWidgets():
                if widget.__class__.__name__ == "Sub11Window":
                    sub11_window = widget
                    break
            
            if sub11_window:
                # تحديث البيانات في النافذة
                if hasattr(sub11_window, 'load_years'):
                    sub11_window.load_years()
                if hasattr(sub11_window, 'load_levels'):
                    sub11_window.load_levels()
                if hasattr(sub11_window, 'filter_assigned_sections'):
                    sub11_window.filter_assigned_sections()
                
                self.log("✅ تم تحديث نافذة إدارة الأقسام والحراسة بنجاح!", "success")
            else:
                self.log("ℹ️ نافذة إدارة الأقسام والحراسة غير مفتوحة حالياً.", "info")
        except Exception as e:
            self.log(f"⚠️ تعذر تحديث نافذة إدارة الأقسام والحراسة: {str(e)}", "warning")
    
    def _show_error_dialog(self, message):
        """عرض مربع حوار الخطأ"""
        if self.parent_widget:
            msg_box = QMessageBox(self.parent_widget)
            msg_box.setWindowTitle("خطأ")
            msg_box.setText(message)
            msg_box.setIcon(QMessageBox.Critical)
            
            # إضافة أيقونة البرنامج
            icon_path = "01.ico"
            if os.path.exists(icon_path):
                msg_box.setWindowIcon(QIcon(icon_path))
            
            msg_box.exec_()
