#!/usr/bin/env python
# -*- coding: utf-8 -*-

from flask import Flask, jsonify, request
from flask_cors import CORS
import sqlite3
import os
from datetime import datetime

app = Flask(__name__)
CORS(app)  # للسماح بطلبات من صفحات الويب

# مسار قاعدة البيانات
DB_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), "data.db")

def get_db_connection():
    """إنشاء اتصال بقاعدة البيانات"""
    try:
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row  # لإرجاع النتائج كقاموس
        return conn
    except Exception as e:
        print(f"خطأ في الاتصال بقاعدة البيانات: {str(e)}")
        return None

def init_database():
    """إنشاء قاعدة البيانات والجدول إذا لم تكن موجودة"""
    # تم تعطيل هذه الدالة لأن البيانات موجودة بالفعل
    # إذا كنت تريد إنشاء جدول جديد، يجب مراجعة البنية
    print("ℹ️  تم تعطيل إنشاء قاعدة البيانات - البيانات موجودة بالفعل")
    pass

@app.route('/api/test')
def test_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    try:
        conn = get_db_connection()
        if conn is None:
            return jsonify({'error': 'لا يمكن الاتصال بقاعدة البيانات'}), 500
        
        cursor = conn.cursor()
        cursor.execute('SELECT COUNT(*) as count FROM جدول_البيانات')
        result = cursor.fetchone()
        conn.close()
        
        return jsonify({
            'status': 'success',
            'message': 'تم الاتصال بقاعدة البيانات بنجاح',
            'total_records': result['count']
        })
    except Exception as e:
        return jsonify({'error': f'خطأ في قاعدة البيانات: {str(e)}'}), 500

@app.route('/api/students', methods=['GET'])
def get_students():
    """جلب جميع بيانات الطلاب"""
    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({"error": "فشل في الاتصال بقاعدة البيانات"}), 500
        
        cursor = conn.cursor()        # استعلام لجلب بيانات الطلاب من الجدول الحقيقي
        cursor.execute('''
            SELECT id, اسم_التلميذ, رمز_التلميذ, النوع, رقم_الهاتف_الأول, 
                   رقم_الهاتف_الثاني, اسم_المجموعة, القسم, المؤسسة_الاصلية, 
                   مبلغ_التسجيل, الواجب_الشهري, تاريخ_الانشاء, تاريخ_التحديث,
                   السلك, اسم_الاستاذ, المادة_الدراسية, حالة_واجبات_التسجيل, 
                   السنة_الدراسية, ملاحظات
            FROM جدول_البيانات
            ORDER BY تاريخ_الانشاء DESC
        ''')
        
        students = []
        for row in cursor.fetchall():
            students.append({
                'id': row['id'],
                'name': row['اسم_التلميذ'] or '',
                'code': row['رمز_التلميذ'] or f"STD{row['id']:04d}",
                'type': row['النوع'] or 'طالب عادي',
                'phone': row['رقم_الهاتف_الأول'] or '',
                'phone2': row['رقم_الهاتف_الثاني'] or '',
                'group': row['اسم_المجموعة'] or '',
                'section': row['القسم'] or '',
                'institution': row['المؤسسة_الاصلية'] or '',
                'registration_fee': row['مبلغ_التسجيل'] or 0,
                'monthly_fee': row['الواجب_الشهري'] or 0,
                'registration_date': row['تاريخ_الانشاء'],
                'update_date': row['تاريخ_التحديث'],
                'track': row['السلك'] or '',
                'teacher': row['اسم_الاستاذ'] or '',
                'subject': row['المادة_الدراسية'] or '',
                'payment_status': row['حالة_واجبات_التسجيل'] or 'غير محدد',
                'academic_year': row['السنة_الدراسية'] or '',
                'notes': row['ملاحظات'] or ''
            })
        
        conn.close()
        return jsonify(students)
        
    except Exception as e:
        print(f"خطأ في جلب البيانات: {str(e)}")
        return jsonify({"error": f"خطأ في جلب البيانات: {str(e)}"}), 500

@app.route('/api/groups')
def get_groups():
    """جلب جميع المجموعات"""
    try:
        conn = get_db_connection()
        if conn is None:
            return jsonify({'error': 'لا يمكن الاتصال بقاعدة البيانات'}), 500        
        cursor = conn.cursor()
        cursor.execute('SELECT DISTINCT اسم_المجموعة FROM جدول_البيانات WHERE اسم_المجموعة IS NOT NULL ORDER BY اسم_المجموعة')
        
        groups = [row['اسم_المجموعة'] for row in cursor.fetchall()]
        conn.close()
        
        return jsonify(groups)
    
    except Exception as e:
        return jsonify({'error': f'خطأ في جلب المجموعات: {str(e)}'}), 500

@app.route('/api/sections')
def get_sections():
    """جلب جميع الأقسام"""
    try:
        conn = get_db_connection()
        if conn is None:
            return jsonify({'error': 'لا يمكن الاتصال بقاعدة البيانات'}), 500
        
        cursor = conn.cursor()
        cursor.execute('SELECT DISTINCT القسم FROM جدول_البيانات WHERE القسم IS NOT NULL ORDER BY القسم')
        
        sections = [row['القسم'] for row in cursor.fetchall()]
        conn.close()
        
        return jsonify(sections)
    
    except Exception as e:
        return jsonify({'error': f'خطأ في جلب الأقسام: {str(e)}'}), 500

@app.route('/api/filter')
def filter_students():
    """تصفية الطلاب حسب المعايير"""
    try:
        conn = get_db_connection()
        if conn is None:
            return jsonify({'error': 'لا يمكن الاتصال بقاعدة البيانات'}), 500
        
        # جلب معايير التصفية من URL
        search = request.args.get('search', '')
        group = request.args.get('group', '')
        section = request.args.get('section', '')
        month = request.args.get('month', '')
        
        # بناء الاستعلام
        query = '''
            SELECT id, اسم_التلميذ, اسم_المجموعة, القسم, السنة_الدراسية, 
                   حالة_واجبات_التسجيل, تاريخ_الانشاء, رقم_الهاتف_الأول, 
                   المؤسسة_الاصلية, ملاحظات
            FROM جدول_البيانات
            WHERE 1=1
        '''
        params = []
        
        if search:
            query += ' AND اسم_التلميذ LIKE ?'
            params.append(f'%{search}%')
        
        if group:
            query += ' AND اسم_المجموعة = ?'
            params.append(group)
        
        if section:
            query += ' AND القسم = ?'
            params.append(section)
        
        if month:  # يمكن استخدامه للبحث في السنة الدراسية
            query += ' AND السنة_الدراسية = ?'
            params.append(month)
        
        query += ' ORDER BY تاريخ_الانشاء DESC'
        
        cursor = conn.cursor()
        cursor.execute(query, params)
        
        students = []
        for row in cursor.fetchall():
            students.append({
                'id': row['id'],
                'name': row['اسم_التلميذ'],
                'group': row['اسم_المجموعة'],
                'section': row['القسم'],
                'academic_year': row['السنة_الدراسية'],
                'payment_status': row['حالة_واجبات_التسجيل'],
                'registration_date': row['تاريخ_الانشاء'],
                'phone': row['رقم_الهاتف_الأول'],
                'institution': row['المؤسسة_الاصلية'],
                'notes': row['ملاحظات']
            })
        
        conn.close()
        return jsonify(students)
    
    except Exception as e:
        return jsonify({'error': f'خطأ في تصفية البيانات: {str(e)}'}), 500

@app.route('/api/student/<int:student_id>')
def get_student(student_id):
    """جلب بيانات طالب واحد"""
    try:
        conn = get_db_connection()
        if conn is None:
            return jsonify({'error': 'لا يمكن الاتصال بقاعدة البيانات'}), 500
        
        cursor = conn.cursor()
        cursor.execute('''
            SELECT id, اسم_التلميذ, اسم_المجموعة, القسم, السنة_الدراسية, 
                   حالة_واجبات_التسجيل, تاريخ_الانشاء, رقم_الهاتف_الأول, 
                   المؤسسة_الاصلية, ملاحظات
            FROM جدول_البيانات
            WHERE id = ?
        ''', (student_id,))
        
        row = cursor.fetchone()
        conn.close()
        
        if row is None:
            return jsonify({'error': 'الطالب غير موجود'}), 404
        
        student = {
            'id': row['id'],
            'name': row['اسم_التلميذ'],
            'group': row['اسم_المجموعة'],
            'section': row['القسم'],
            'academic_year': row['السنة_الدراسية'],
            'payment_status': row['حالة_واجبات_التسجيل'],
            'registration_date': row['تاريخ_الانشاء'],
            'phone': row['رقم_الهاتف_الأول'],
            'institution': row['المؤسسة_الاصلية'],
            'notes': row['ملاحظات']
        }
        
        return jsonify(student)
    
    except Exception as e:
        return jsonify({'error': f'خطأ في جلب بيانات الطالب: {str(e)}'}), 500

@app.route('/api/statistics')
def get_statistics():
    """جلب الإحصائيات"""
    try:
        conn = get_db_connection()
        if conn is None:
            return jsonify({'error': 'لا يمكن الاتصال بقاعدة البيانات'}), 500
        
        cursor = conn.cursor()
          # إجمالي الطلاب
        cursor.execute('SELECT COUNT(*) as total FROM جدول_البيانات')
        total_students = cursor.fetchone()['total']
        
        # الطلاب الذين دفعوا
        cursor.execute('SELECT COUNT(*) as paid FROM جدول_البيانات WHERE حالة_واجبات_التسجيل = "مدفوع"')
        paid_students = cursor.fetchone()['paid']
        
        # الطلاب الذين لم يدفعوا
        cursor.execute('SELECT COUNT(*) as unpaid FROM جدول_البيانات WHERE حالة_واجبات_التسجيل = "غير مدفوع"')
        unpaid_students = cursor.fetchone()['unpaid']
        
        # المجموعات
        cursor.execute('SELECT COUNT(DISTINCT اسم_المجموعة) as groups FROM جدول_البيانات WHERE اسم_المجموعة IS NOT NULL')
        total_groups = cursor.fetchone()['groups']
        
        conn.close()
        
        statistics = {
            'total_students': total_students,
            'paid_students': paid_students,
            'unpaid_students': unpaid_students,
            'total_groups': total_groups
        }
        
        return jsonify(statistics)
    
    except Exception as e:
        return jsonify({'error': f'خطأ في جلب الإحصائيات: {str(e)}'}), 500

@app.route('/api/sections/<group_name>')
def get_sections_by_group(group_name):
    """جلب الأقسام لمجموعة معينة"""
    try:
        conn = get_db_connection()
        if conn is None:
            return jsonify({'error': 'لا يمكن الاتصال بقاعدة البيانات'}), 500
        
        cursor = conn.cursor()
        cursor.execute('''
            SELECT DISTINCT القسم 
            FROM جدول_البيانات 
            WHERE اسم_المجموعة = ? AND القسم IS NOT NULL 
            ORDER BY القسم
        ''', (group_name,))
        
        sections = [row['القسم'] for row in cursor.fetchall()]
        conn.close()
        
        return jsonify(sections)
    
    except Exception as e:
        return jsonify({'error': f'خطأ في جلب أقسام المجموعة: {str(e)}'}), 500

@app.route('/api/financial-stats')
def get_financial_stats():
    """جلب الإحصائيات المالية"""
    try:
        conn = get_db_connection()
        if conn is None:
            return jsonify({'error': 'لا يمكن الاتصال بقاعدة البيانات'}), 500
        
        cursor = conn.cursor()
        
        # حساب إجمالي المبالغ المستحقة
        cursor.execute('''
            SELECT 
                SUM(CAST([إجمالي مبلغ التسجيل] AS REAL)) as total_registration,
                SUM(CAST([الواجب الشهري] AS REAL)) as total_monthly,
                COUNT(*) as total_students
            FROM جدول_البيانات 
            WHERE [إجمالي مبلغ التسجيل] IS NOT NULL 
            AND [الواجب الشهري] IS NOT NULL
        ''')
        
        totals = cursor.fetchone()
        total_registration = totals['total_registration'] or 0
        total_monthly = totals['total_monthly'] or 0
        total_students = totals['total_students'] or 0
        
        # حساب المدفوعات حسب حالة الدفع
        cursor.execute('''
            SELECT 
                [حالة الدفع],
                COUNT(*) as count,
                SUM(CAST([إجمالي مبلغ التسجيل] AS REAL)) as sum_registration,
                SUM(CAST([الواجب الشهري] AS REAL)) as sum_monthly
            FROM جدول_البيانات 
            WHERE [إجمالي مبلغ التسجيل] IS NOT NULL 
            AND [الواجب الشهري] IS NOT NULL
            AND [حالة الدفع] IS NOT NULL
            GROUP BY [حالة الدفع]
        ''')
        
        payment_stats = cursor.fetchall()
        conn.close()
        
        # حساب الإحصائيات
        total_due = total_registration + total_monthly
        total_paid = 0
        total_outstanding = 0
        
        for stat in payment_stats:
            status = stat['حالة الدفع']
            reg_sum = stat['sum_registration'] or 0
            monthly_sum = stat['sum_monthly'] or 0
            total_for_status = reg_sum + monthly_sum
            
            if status == 'مدفوع':
                total_paid += total_for_status
            elif status == 'مدفوع جزئياً':
                # تقدير نصف المبلغ كمدفوع
                total_paid += total_for_status * 0.5
                total_outstanding += total_for_status * 0.5
            else:
                total_outstanding += total_for_status
        
        return jsonify({
            'totalDue': round(total_due, 2),
            'totalPaid': round(total_paid, 2),
            'totalOutstanding': round(total_outstanding, 2),
            'totalStudents': total_students,
            'paymentBreakdown': [dict(row) for row in payment_stats]
        })
    
    except Exception as e:
        return jsonify({'error': f'خطأ في جلب الإحصائيات المالية: {str(e)}'}), 500

@app.route('/api/institution-data', methods=['GET'])
def get_institution_data():
    """جلب بيانات المؤسسة"""
    try:
        conn = get_db_connection()
        if conn is None:
            return jsonify({'error': 'لا يمكن الاتصال بقاعدة البيانات'}), 500
        
        cursor = conn.cursor()
        
        # التحقق من وجود جدول بيانات المؤسسة
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='بيانات_المؤسسة'
        """)
        
        if not cursor.fetchone():
            # إنشاء الجدول إذا لم يكن موجوداً
            cursor.execute('''
                CREATE TABLE بيانات_المؤسسة (
                    رقم_الهاتف TEXT,
                    المؤسسة TEXT,
                    السنة_الدراسية TEXT,
                    المدينة TEXT,
                    المسؤول TEXT,
                    رقم_التسجيل TEXT,
                    ImagePath1 TEXT
                )
            ''')
            
            # إدراج سجل فارغ افتراضي
            cursor.execute("""
                INSERT INTO بيانات_المؤسسة 
                (رقم_الهاتف, المؤسسة, السنة_الدراسية, المدينة, المسؤول, رقم_التسجيل, ImagePath1)
                VALUES ('', '', '', '', '', '', '')            """)
            conn.commit()
        
        # جلب البيانات
        cursor.execute("""
            SELECT رقم_الهاتف, المؤسسة, السنة_الدراسية, المدينة, اسم_المسؤول, رقم_التسجيل, ImagePath1
            FROM بيانات_المؤسسة 
            LIMIT 1
        """)
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            return jsonify({
                'phone': result['رقم_الهاتف'] or '',
                'name': result['المؤسسة'] or '',
                'academicYear': result['السنة_الدراسية'] or '',
                'city': result['المدينة'] or '',
                'manager': result['اسم_المسؤول'] or '',
                'registrationNumber': result['رقم_التسجيل'] or '',
                'logoPath': result['ImagePath1'] or ''
            })
        else:
            return jsonify({
                'phone': '', 'name': '', 'academicYear': '', 
                'city': '', 'manager': '', 'registrationNumber': '', 'logoPath': ''
            })
    
    except Exception as e:
        return jsonify({'error': f'خطأ في جلب بيانات المؤسسة: {str(e)}'}), 500

@app.route('/api/institution-data', methods=['POST'])
def save_institution_data():
    """حفظ بيانات المؤسسة"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'لا توجد بيانات للحفظ'}), 400
        
        # التحقق من الحقول المطلوبة
        required_fields = ['phone', 'name', 'academicYear', 'city', 'manager']
        missing_fields = [field for field in required_fields if not data.get(field)]
        
        if missing_fields:
            return jsonify({'error': f'الحقول التالية مطلوبة: {", ".join(missing_fields)}'}), 400
        
        conn = get_db_connection()
        if conn is None:
            return jsonify({'error': 'لا يمكن الاتصال بقاعدة البيانات'}), 500
        
        cursor = conn.cursor()
          # التحقق من وجود سجل
        cursor.execute("SELECT COUNT(*) as count FROM بيانات_المؤسسة")
        count = cursor.fetchone()['count']
        
        if count == 0:
            # إدراج سجل جديد
            cursor.execute("""
                INSERT INTO بيانات_المؤسسة 
                (رقم_الهاتف, المؤسسة, السنة_الدراسية, المدينة, اسم_المسؤول, رقم_التسجيل)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (
                data['phone'], data['name'], data['academicYear'], 
                data['city'], data['manager'], data.get('registrationNumber', '')
            ))
        else:
            # تحديث السجل الموجود
            cursor.execute("""
                UPDATE بيانات_المؤسسة 
                SET رقم_الهاتف = ?, المؤسسة = ?, السنة_الدراسية = ?, 
                    المدينة = ?, اسم_المسؤول = ?, رقم_التسجيل = ?
                WHERE rowid = 1
            """, (
                data['phone'], data['name'], data['academicYear'], 
                data['city'], data['manager'], data.get('registrationNumber', '')
            ))
        
        conn.commit()
        conn.close()
        
        return jsonify({'message': 'تم حفظ بيانات المؤسسة بنجاح'})
    
    except Exception as e:
        return jsonify({'error': f'خطأ في حفظ بيانات المؤسسة: {str(e)}'}), 500

@app.route('/api/upload-logo', methods=['POST'])
def upload_logo():
    """رفع شعار المؤسسة"""
    try:
        if 'logo' not in request.files:
            return jsonify({'error': 'لم يتم العثور على ملف الشعار'}), 400
        
        file = request.files['logo']
        
        if file.filename == '':
            return jsonify({'error': 'لم يتم اختيار ملف'}), 400
        
        # التحقق من نوع الملف
        allowed_extensions = {'png', 'jpg', 'jpeg', 'gif'}
        file_extension = file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else ''
        
        if file_extension not in allowed_extensions:
            return jsonify({'error': 'نوع الملف غير مدعوم. يرجى استخدام PNG, JPG, JPEG أو GIF'}), 400
        
        # إنشاء مجلد للشعارات إذا لم يكن موجوداً
        logos_dir = os.path.join(os.path.dirname(DB_PATH), 'logos')
        os.makedirs(logos_dir, exist_ok=True)
        
        # حفظ الملف
        import uuid
        filename = f"logo_{uuid.uuid4().hex}.{file_extension}"
        file_path = os.path.join(logos_dir, filename)
        file.save(file_path)
        
        # تحديث قاعدة البيانات
        conn = get_db_connection()
        if conn is None:
            return jsonify({'error': 'لا يمكن الاتصال بقاعدة البيانات'}), 500
        
        cursor = conn.cursor()
        cursor.execute("UPDATE بيانات_المؤسسة SET ImagePath1 = ? WHERE rowid = 1", (file_path,))
        conn.commit()
        conn.close()
        
        return jsonify({
            'message': 'تم رفع الشعار بنجاح',
            'logoPath': file_path
        })
    
    except Exception as e:
        return jsonify({'error': f'خطأ في رفع الشعار: {str(e)}'}), 500

@app.route('/api/database-info')
def get_database_info():
    """الحصول على معلومات قاعدة البيانات"""
    try:
        conn = get_db_connection()
        if conn is None:
            return jsonify({'error': 'لا يمكن الاتصال بقاعدة البيانات'}), 500
        
        cursor = conn.cursor()
        
        # حساب حجم قاعدة البيانات
        db_size = os.path.getsize(DB_PATH) if os.path.exists(DB_PATH) else 0
        size_mb = round(db_size / (1024 * 1024), 2)
        
        # عدد الجداول
        cursor.execute("SELECT COUNT(*) as count FROM sqlite_master WHERE type='table'")
        tables_count = cursor.fetchone()['count']
        
        # إجمالي السجلات من الجداول الرئيسية
        total_records = 0
        try:
            cursor.execute('SELECT COUNT(*) as count FROM جدول_البيانات')
            total_records += cursor.fetchone()['count']
        except:
            pass
        
        try:
            cursor.execute('SELECT COUNT(*) as count FROM اللوائح')
            total_records += cursor.fetchone()['count']
        except:
            pass
        
        # آخر تاريخ تعديل
        last_update = datetime.fromtimestamp(os.path.getmtime(DB_PATH)).strftime('%Y-%m-%d %H:%M:%S') if os.path.exists(DB_PATH) else 'غير معروف'
        
        conn.close()
        
        return jsonify({
            'size': f'{size_mb} MB',
            'tables': tables_count,
            'records': total_records,
            'lastUpdate': last_update
        })
    
    except Exception as e:
        return jsonify({'error': f'خطأ في جلب معلومات قاعدة البيانات: {str(e)}'}), 500

@app.route('/api/import-masar', methods=['POST'])
def import_masar_data():
    """استيراد البيانات من ملف منظومة مسار"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'لم يتم رفع أي ملف'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'لم يتم اختيار ملف'}), 400
        
        import_type = request.form.get('importType', 'new')
        academic_year = request.form.get('academicYear', '')
        
        # حفظ الملف مؤقتاً
        temp_path = f"temp_{file.filename}"
        file.save(temp_path)
        
        # هنا يمكن إضافة منطق استيراد البيانات من ملف Excel
        # سنحتاج لاستخدام pandas لقراءة الملف
        
        # تنظيف الملف المؤقت
        if os.path.exists(temp_path):
            os.remove(temp_path)
        
        return jsonify({
            'message': 'تم استيراد البيانات بنجاح',
            'recordsCount': 0,  # سيتم حسابه فعلياً عند التنفيذ
            'importType': import_type,
            'academicYear': academic_year
        })
    
    except Exception as e:
        return jsonify({'error': f'خطأ في استيراد البيانات: {str(e)}'}), 500

@app.route('/api/update-structure', methods=['POST'])
def update_structure():
    """تحديث البنية التربوية"""
    try:
        conn = get_db_connection()
        if conn is None:
            return jsonify({'error': 'لا يمكن الاتصال بقاعدة البيانات'}), 500
        
        cursor = conn.cursor()
        
        # تحديث البنية التربوية
        cursor.execute("""
            INSERT OR IGNORE INTO البنية_التربوية (السنة_الدراسية, القسم, المستوى)
            SELECT DISTINCT السنة_الدراسية, القسم, المستوى
            FROM اللوائح
        """)
        
        conn.commit()
        conn.close()
        
        return jsonify({'message': 'تم تحديث البنية التربوية بنجاح'})
    
    except Exception as e:
        return jsonify({'error': f'خطأ في تحديث البنية التربوية: {str(e)}'}), 500

@app.route('/api/recalculate-totals', methods=['POST'])
def recalculate_totals():
    """إعادة حساب المجاميع"""
    try:
        conn = get_db_connection()
        if conn is None:
            return jsonify({'error': 'لا يمكن الاتصال بقاعدة البيانات'}), 500
        
        cursor = conn.cursor()
        
        # إعادة حساب مجموع التلاميذ في جدول اللوائح
        cursor.execute("""
            UPDATE اللوائح as l1
            SET مجموع_التلاميذ = (
                SELECT COUNT(*)
                FROM اللوائح AS l2
                WHERE l2.القسم = l1.القسم
                AND l2.السنة_الدراسية = l1.السنة_الدراسية
            )
        """)
        
        # إعادة حساب مجموع التلاميذ في البنية التربوية
        cursor.execute("""
            UPDATE البنية_التربوية
            SET مجموع_التلاميذ = (
                SELECT COUNT(*)
                FROM اللوائح AS l
                WHERE l.السنة_الدراسية = البنية_التربوية.السنة_الدراسية
                AND l.القسم = البنية_التربوية.القسم
                AND l.المستوى = البنية_التربوية.المستوى
            )
        """)
        
        conn.commit()
        conn.close()
        
        return jsonify({'message': 'تم إعادة حساب المجاميع بنجاح'})
    
    except Exception as e:
        return jsonify({'error': f'خطأ في إعادة حساب المجاميع: {str(e)}'}), 500

@app.route('/api/validate-data')
def validate_data():
    """التحقق من صحة البيانات"""
    try:
        conn = get_db_connection()
        if conn is None:
            return jsonify({'error': 'لا يمكن الاتصال بقاعدة البيانات'}), 500
        
        cursor = conn.cursor()
        errors = []
        
        # فحص السجلات المكررة
        cursor.execute("""
            SELECT الرمز, COUNT(*) as count
            FROM اللوائح
            GROUP BY الرمز, السنة_الدراسية
            HAVING COUNT(*) > 1
        """)
        duplicates = cursor.fetchall()
        if duplicates:
            errors.append(f'تم العثور على {len(duplicates)} رمز مكرر')
        
        # فحص السجلات الفارغة
        cursor.execute("""
            SELECT COUNT(*) as count
            FROM اللوائح
            WHERE الرمز IS NULL OR TRIM(الرمز) = ''
        """)
        empty_codes = cursor.fetchone()['count']
        if empty_codes > 0:
            errors.append(f'تم العثور على {empty_codes} سجل برمز فارغ')
        
        conn.close()
        
        return jsonify({
            'isValid': len(errors) == 0,
            'errors': len(errors),
            'issues': errors
        })
    
    except Exception as e:
        return jsonify({'error': f'خطأ في فحص البيانات: {str(e)}'}), 500

@app.route('/api/optimize-database', methods=['POST'])
def optimize_database():
    """تحسين قاعدة البيانات"""
    try:
        conn = get_db_connection()
        if conn is None:
            return jsonify({'error': 'لا يمكن الاتصال بقاعدة البيانات'}), 500
        
        cursor = conn.cursor()
        
        # تنفيذ أوامر التحسين
        cursor.execute('VACUUM')
        cursor.execute('ANALYZE')
        
        conn.commit()
        conn.close()
        
        return jsonify({'message': 'تم تحسين قاعدة البيانات بنجاح'})
    
    except Exception as e:
        return jsonify({'error': f'خطأ في تحسين قاعدة البيانات: {str(e)}'}), 500

@app.route('/api/backup-database')
def backup_database():
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    try:
        if not os.path.exists(DB_PATH):
            return jsonify({'error': 'ملف قاعدة البيانات غير موجود'}), 404
        
        # قراءة ملف قاعدة البيانات
        with open(DB_PATH, 'rb') as f:
            data = f.read()
        
        # إرجاع الملف للتحميل
        from flask import send_file
        import io
        return send_file(
            io.BytesIO(data),
            as_attachment=True,
            download_name=f'backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db',
            mimetype='application/octet-stream'
        )
    
    except Exception as e:
        return jsonify({'error': f'خطأ في إنشاء النسخة الاحتياطية: {str(e)}'}), 500

@app.route('/api/check-integrity')
def check_database_integrity():
    """فحص سلامة قاعدة البيانات"""
    try:
        conn = get_db_connection()
        if conn is None:
            return jsonify({'error': 'لا يمكن الاتصال بقاعدة البيانات'}), 500
        
        cursor = conn.cursor()
        
        # فحص سلامة قاعدة البيانات
        cursor.execute('PRAGMA integrity_check')
        result = cursor.fetchone()
        
        is_valid = result[0] == 'ok' if result else False
        issues = [] if is_valid else ['قاعدة البيانات تحتوي على أخطاء']
        
        conn.close()
        
        return jsonify({
            'isValid': is_valid,
            'issues': issues
        })
    
    except Exception as e:
        return jsonify({'error': f'خطأ في فحص سلامة قاعدة البيانات: {str(e)}'}), 500

@app.route('/api/reset-database', methods=['POST'])
def reset_database():
    """إعادة تعيين قاعدة البيانات"""
    try:
        conn = get_db_connection()
        if conn is None:
            return jsonify({'error': 'لا يمكن الاتصال بقاعدة البيانات'}), 500
        
        cursor = conn.cursor()
        
        # حذف جميع البيانات من الجداول الرئيسية
        tables_to_clear = ['اللوائح', 'البنية_التربوية', 'الأساتذة']
        
        for table in tables_to_clear:
            try:
                cursor.execute(f'DELETE FROM {table}')
            except sqlite3.OperationalError:
                pass  # الجدول غير موجود
        
        conn.commit()
        conn.close()
        
        return jsonify({'message': 'تم إعادة تعيين قاعدة البيانات بنجاح'})
    
    except Exception as e:
        return jsonify({'error': f'خطأ في إعادة تعيين قاعدة البيانات: {str(e)}'}), 500

# ...existing code...
