# 🚀 دليل تشغيل نظام إدارة الطلاب

## 📋 **المتطلبات**
- ✅ متصفح ويب حديث (Chrome, Firefox, Edge)
- ✅ Python 3.7+ (للخادم المحلي)
- ✅ لا حاجة لبرامج إضافية!

## 🔧 **طريقة التشغيل**

### **الطريقة الأولى: تشغيل محلي بسيط**
```bash
# 1. افتح مجلد المشروع
cd c:\Users\<USER>\Desktop\csv\taheri77

# 2. شغل الخادم
python server.py

# 3. افتح المتصفح واذهب إلى:
http://localhost:5000
```

### **الطريقة الثانية: تشغيل مباشر (بدون خادم)**
1. افتح ملف `index.html` مباشرة في المتصفح
2. سيعمل النظام ولكن بدون حفظ البيانات

## 🛡️ **إعدادات الأمان**

### **تغيير كلمة المرور:**
افتح ملف `security.js` واعثر على هذا السطر:
```javascript
const defaultPassword = 'admin123'; // غير هذه الكلمة
```

### **إعدادات الجلسة:**
```javascript
this.sessionTimeout = 30 * 60 * 1000; // 30 دقيقة (بالمللي ثانية)
this.maxAttempts = 5; // عدد المحاولات المسموح
```

## 📊 **ميزات الحماية المفعلة**

### **🔒 حماية أساسية:**
- منع النسخ من الحقول الحساسة
- منع فحص العناصر (F12)
- انتهاء الجلسة التلقائي
- قفل بعد المحاولات الفاشلة

### **🔐 حماية البيانات:**
- تشفير البيانات المحلية
- نسخ احتياطية مشفرة
- فحص سلامة البيانات
- تنظيف الذاكرة

### **⚠️ تحذيرات الأمان:**
- رسائل تحذير عند المحاولات المريبة
- تسجيل انتهاكات الأمان
- إقفال تلقائي عند التطفل

## 🎨 **إعدادات الخطوط الجديدة**

### **التحكم في الخطوط:**
1. اذهب لتبويب "الإعدادات"
2. اختر "إعدادات الخطوط"
3. يمكنك التحكم في:
   - نوع الخط الرئيسي
   - حجم الخط
   - نمط الخط (عادي/مائل)
   - سُمك الخط (رفيع/عادي/عريض)
   - خط العناوين الرئيسية
   - خط العناوين الفرعية

### **معاينة فورية:**
- تظهر المعاينة أثناء التغيير
- يمكن تطبيق الإعدادات أو إعادة التعيين

## 📤 **النسخ الاحتياطية الآمنة**

### **إنشاء نسخة احتياطية:**
```javascript
// في console المتصفح:
window.dataProtection.createSecureBackup();
```

### **استرجاع نسخة احتياطية:**
```javascript
// اختر الملف المشفر:
const fileInput = document.createElement('input');
fileInput.type = 'file';
fileInput.accept = '.enc';
fileInput.onchange = async (e) => {
    const file = e.target.files[0];
    const data = await window.dataProtection.restoreSecureBackup(file);
    console.log('تم الاسترجاع:', data);
};
fileInput.click();
```

## 🌐 **نشر النظام**

### **للاستخدام المحلي في المؤسسة:**
1. انسخ جميع الملفات لخادم محلي
2. شارك الرابط مع المستخدمين
3. كل مستخدم يفتح الرابط في متصفحه

### **للنشر السحابي:**
1. ارفع الملفات على GitHub
2. فعل GitHub Pages
3. احصل على رابط عام

## 🔧 **استكشاف الأخطاء**

### **خطأ في الاتصال بقاعدة البيانات:**
```bash
# تأكد من وجود الملف:
ls data.db

# تأكد من تشغيل الخادم:
python server.py
```

### **مشاكل في الخطوط:**
1. امسح cache المتصفح
2. تأكد من تحميل ملفات CSS بشكل صحيح
3. جرب إعادة تعيين إعدادات الخطوط

### **مشاكل الأمان:**
- إذا تم قفل النظام بالخطأ، أعد تحميل الصفحة
- لإعادة تعيين كلمة المرور، عدل ملف `security.js`

## 📱 **التوافق**

### **المتصفحات المدعومة:**
- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+

### **الأجهزة المدعومة:**
- ✅ Windows (جميع الإصدارات)
- ✅ Mac OS
- ✅ Linux
- ✅ Android (Chrome)
- ✅ iOS (Safari)

## 📞 **المساعدة**

### **الوظائف المحمية:**
هذه الوظائف تحتاج كلمة مرور:
- 🗑️ حذف البيانات
- 🗑️ حذف سجل أداء
- 🔐 التحقق من التفعيل

### **رسائل الخطأ الشائعة:**
- **"انتهت مهلة الجلسة"**: طبيعي بعد 30 دقيقة من عدم النشاط
- **"تم تجاوز عدد المحاولات"**: أعد تحميل الصفحة
- **"فشل في الاتصال"**: تأكد من تشغيل `server.py`

## 🎯 **نصائح للاستخدام الأمثل**

1. **احفظ البيانات بانتظام** باستخدام الزر "حفظ البيانات"
2. **أنشئ نسخ احتياطية** مشفرة كل فترة
3. **لا تشارك كلمة المرور** مع غير المخولين
4. **استخدم HTTPS** في البيئة الإنتاجية
5. **حدث المتصفح** للحصول على أفضل أداء

---

🎉 **مبروك! نظامك الآن محمي ويعمل على جميع الأجهزة بدون مشاكل التوافق!**
