<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام استيراد البيانات التعليمية</title>
    <style>        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }        body {
            font-family: 'Calibri', 'Tahoma', 'Arial', sans-serif;
            font-size: 13px;
            font-weight: bold;
            background-color: #f5f5f5;
            padding: 20px;
            direction: rtl;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 10px;
            background-color: #e74c3c;
            animation: pulse 2s infinite;
        }

        .status-indicator.connected {
            background-color: #27ae60;
            animation: none;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .main-content {
            padding: 20px;
        }

        .buttons-section {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }        .import-button {
            flex: 1;
            min-width: 300px;
            background-color: #9b59b6;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 15px 20px;
            font-size: 14px;
            font-weight: bold;
            font-family: 'Calibri', 'Tahoma', 'Arial', sans-serif;
            cursor: pointer;
            transition: background-color 0.3s;
            text-align: center;
        }

        .import-button:hover {
            background-color: #8e44ad;
        }

        .import-button:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
        }

        .import-button.secondary {
            background-color: #3498db;
        }

        .import-button.secondary:hover {
            background-color: #2980b9;
        }

        .log-container {
            background-color: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            height: 350px;
            overflow-y: auto;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
            line-height: 1.5;
            margin-bottom: 20px;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
            white-space: pre-wrap;
        }

        .log-info {
            color: #17a2b8;
        }

        .log-success {
            color: #28a745;
            font-weight: bold;
        }

        .log-error {
            color: #dc3545;
            font-weight: bold;
        }

        .log-warning {
            color: #ffc107;
            font-weight: bold;
        }

        .log-progress {
            color: #6f42c1;
        }

        .progress-container {
            background-color: #E3F2FD;
            border: 2px solid #0D47A1;
            border-radius: 5px;
            margin: 20px 0;
            overflow: hidden;
            display: none;
        }

        .progress-bar {
            background-color: #0D47A1;
            height: 25px;
            width: 0%;
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        .progress-text {
            text-align: center;
            padding: 10px;
            font-weight: bold;
            color: #0D47A1;
        }

        .file-input {
            display: none;
        }

        .bottom-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }        .refresh-button {
            background-color: #2ecc71;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 10px 20px;
            font-size: 14px;
            font-weight: bold;
            font-family: 'Calibri', 'Tahoma', 'Arial', sans-serif;
            cursor: pointer;
            flex: 1;
        }

        .refresh-button:hover {
            background-color: #27ae60;
        }        .help-button {
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 10px 20px;
            font-size: 14px;
            font-weight: bold;
            font-family: 'Calibri', 'Tahoma', 'Arial', sans-serif;
            cursor: pointer;
            flex: 1;
        }

        .help-button:hover {
            background-color: #2980b9;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: white;
            margin: 10% auto;
            padding: 20px;
            border-radius: 10px;
            width: 80%;
            max-width: 600px;
            text-align: center;
            direction: rtl;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-buttons {
            margin-top: 20px;
            display: flex;
            gap: 10px;
            justify-content: center;
        }        .modal-button {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            font-weight: bold;
            font-family: 'Calibri', 'Tahoma', 'Arial', sans-serif;
            font-size: 14px;
            cursor: pointer;
        }

        .modal-button.confirm {
            background-color: #0D47A1;
            color: white;
        }

        .modal-button.cancel {
            background-color: #E3F2FD;
            color: #0D47A1;
            border: 1px solid #0D47A1;
        }

        .modal-button:hover {
            opacity: 0.8;
        }

        .summary-section {
            background-color: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 5px;
            padding: 15px;
            margin-top: 15px;
            display: none;
        }

        .summary-title {
            font-weight: bold;
            color: #2e7d32;
            margin-bottom: 10px;
        }

        .summary-item {
            margin-bottom: 5px;
            color: #388e3c;
        }

        .connection-status {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 15px;
            text-align: center;
        }

        .connection-status.connected {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }

        .connection-status.error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }

        .files-info {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            font-size: 12px;
        }

        .font-controls {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
            justify-content: center;
        }

        .font-control-button {
            background-color: #6c757d;
            color: white;
            border: none;
            border-radius: 3px;
            padding: 5px 10px;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .font-control-button:hover {
            background-color: #5a6268;
        }

        .font-size-display {
            background-color: white;
            border: 1px solid #ced4da;
            border-radius: 3px;
            padding: 5px 10px;
            font-size: 12px;
            font-weight: bold;
            min-width: 60px;
            text-align: center;
        }

        .font-controls label {
            font-size: 12px;
            font-weight: bold;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎓 نظام استيراد البيانات التعليمية</h1>
            <p>منظومة إدارة البيانات الشاملة</p>
            <div>
                <span class="status-indicator" id="statusIndicator"></span>
                <span id="statusText">جاري الاتصال بالخادم...</span>
            </div>
        </div>        <div class="main-content">
            <div class="connection-status" id="connectionStatus">
                جاري التحقق من الاتصال بالخادم وقاعدة البيانات...
            </div>
            
            <div class="font-controls">
                <label>حجم الخط:</label>
                <button class="font-control-button" onclick="decreaseFontSize()">أ-</button>
                <div class="font-size-display" id="fontSizeDisplay">13px</div>
                <button class="font-control-button" onclick="increaseFontSize()">أ+</button>
                <button class="font-control-button" onclick="resetFontSize()">إعادة تعيين</button>
            </div><div class="buttons-section">
                <button class="import-button" id="importMasarButton" onclick="startMasarImport()" disabled>
                    📥 استيراد اللوائح من منظومة مسار باللغة العربية
                </button>
                
                <button class="import-button secondary" id="importSecretButton" onclick="startSecretCodesImport()" disabled>
                    🔐 استيراد الرمز السري وتحيينه دفعة واحدة
                </button>
                
                <button class="import-button secondary" id="importTeachersButton" onclick="startTeachersImport()" disabled>
                    👨‍🏫 استيراد أسماء الأساتذة والمواد المدرسة
                </button>
            </div>

            <input type="file" id="excelFile" class="file-input" accept=".xlsx,.xls" onchange="handleFileSelect(event)">
            <input type="file" id="secretFilesInput" class="file-input" accept=".xlsx,.xls" multiple onchange="handleSecretFilesSelect(event)">
            <input type="file" id="teachersFileInput" class="file-input" accept=".xlsx,.xls" onchange="handleTeachersFileSelect(event)">

            <div class="progress-container" id="progressContainer">
                <div class="progress-text" id="progressText">جاري التحضير...</div>
                <div class="progress-bar" id="progressBar">0%</div>
            </div>

            <div class="log-container" id="logContainer">
                <div class="log-entry log-info">ℹ️ <span id="currentTime"></span> - مرحباً بك في نظام استيراد البيانات التعليمية</div>
                <div class="log-entry log-info">ℹ️ <span id="currentTime2"></span> - جاري التحقق من الاتصال بالخادم...</div>
            </div>

            <div class="summary-section" id="summarySection">
                <div class="summary-title">📊 ملخص عملية الاستيراد</div>
                <div id="summaryContent"></div>
            </div>

            <div class="bottom-buttons">
                <button class="refresh-button" onclick="refreshData()">
                    🔄 تحديث البيانات
                </button>
                <button class="help-button" onclick="showHelp()">
                    ❓ تعليمات الاستخدام
                </button>
            </div>
        </div>
    </div>

    <!-- Modal for file confirmation -->
    <div id="confirmModal" class="modal">
        <div class="modal-content">
            <h3>⚠️ تنبيه: ملف غير معروف</h3>
            <p id="modalMessage"></p>
            <p>هل تريد الاستمرار في استيراد هذا الملف على أي حال؟</p>
            <div class="modal-buttons">
                <button class="modal-button confirm" onclick="confirmImport()">نعم، استمر في الاستيراد</button>
                <button class="modal-button cancel" onclick="cancelImport()">لا، إلغاء الاستيراد</button>
            </div>
        </div>
    </div>

    <!-- Help Modal -->
    <div id="helpModal" class="modal">
        <div class="modal-content">
            <h3>❓ تعليمات الاستخدام</h3>
            <div style="text-align: right; padding: 20px; font-family: 'Calibri', 'Tahoma', 'Arial', sans-serif; font-size: 13px; font-weight: bold;">
                <h4>🔧 متطلبات التشغيل:</h4>
                <ol>
                    <li>تأكد من تشغيل الخادم Python بالأمر: <code>python masar_server.py</code></li>
                    <li>يجب أن يكون ملف قاعدة البيانات data.db في نفس المجلد</li>
                </ol>
                
                <h4>📥 استيراد اللوائح من منظومة مسار:</h4>
                <ol>
                    <li>اضغط على زر "استيراد اللوائح من منظومة مسار"</li>
                    <li>اختر ملف Excel الذي يحتوي على لوائح الطلاب</li>                    <li>يُفضل أن يحتوي اسم الملف على كلمة "ListEleve"</li>
                    <li>انتظر حتى اكتمال عملية الاستيراد</li>
                </ol>
                
                <h4>🔐 استيراد الرمز السري:</h4>
                <ol>
                    <li>اضغط على زر "استيراد الرمز السري وتحيينه دفعة واحدة"</li>
                    <li>اختر ملفات Excel متعددة (حتى 100 ملف)</li>
                    <li>تأكد من أن كل ملف يحتوي على الرمز والرمز السري</li>
                    <li>سيتم تحديث جدول السجل العام تلقائياً</li>
                </ol>
                
                <h4>👨‍🏫 استيراد أسماء الأساتذة والمواد المدرسة:</h4>
                <ol>
                    <li>اضغط على زر "استيراد أسماء الأساتذة والمواد المدرسة"</li>
                    <li>اختر ملف Excel واحد</li>
                    <li>يُفضل أن يحتوي اسم الملف على "SeancesEnseignants" أو "Book"</li>
                    <li>يجب أن يحتوي الملف على الأعمدة: المادة، الرمز، اسم الأستاذ</li>
                </ol>
                
                <h4>📝 ملاحظات مهمة:</h4>
                <ul>
                    <li>سيتم حفظ البيانات في ملف data.db الفعلي</li>
                    <li>سيتم تحديث جميع الجداول ذات الصلة</li>
                    <li>تأكد من عدم استخدام قاعدة البيانات من برامج أخرى أثناء الاستيراد</li>
                    <li>احتفظ بنسخة احتياطية قبل الاستيراد</li>
                </ul>
            </div>
            <div class="modal-buttons">
                <button class="modal-button confirm" onclick="closeHelp()">فهمت</button>
            </div>
        </div>
    </div>

    <script>
        const SERVER_URL = 'http://localhost:5000';
        let currentFile = null;
        let selectedFiles = [];
        let shouldProceed = false;
        let serverConnected = false;
        let currentOperation = null; // 'masar' or 'secret'

        // Font size control variables
        let currentFontSize = 13;
        const minFontSize = 10;
        const maxFontSize = 20;

        // Initialize time display
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar', { hour12: false });
            document.getElementById('currentTime').textContent = timeString;
            document.getElementById('currentTime2').textContent = timeString;
        }

        // Log function
        function log(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString('ar', { hour12: false });
            
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = message;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // Check server connection
        async function checkServerConnection() {
            try {
                const response = await fetch(`${SERVER_URL}/api/check-database`);
                const data = await response.json();
                  if (response.ok) {
                    serverConnected = true;
                    updateConnectionStatus(true, data.message);
                    log(data.message, data.status);
                    
                      document.getElementById('importMasarButton').disabled = false;
                    document.getElementById('importSecretButton').disabled = false;
                    document.getElementById('importTeachersButton').disabled = false;
                } else {
                    throw new Error(data.message || 'خطأ في الاتصال بالخادم');
                }
            } catch (error) {
                serverConnected = false;
                updateConnectionStatus(false, `خطأ في الاتصال بالخادم: ${error.message}`);
                log(`❌ خطأ في الاتصال بالخادم: ${error.message}`, 'error');
                log('💡 تأكد من تشغيل الخادم بالأمر: python masar_server.py', 'warning');
            }
        }

        // Update connection status display
        function updateConnectionStatus(connected, message) {
            const statusIndicator = document.getElementById('statusIndicator');
            const statusText = document.getElementById('statusText');
            const connectionStatus = document.getElementById('connectionStatus');
            
            if (connected) {
                statusIndicator.classList.add('connected');
                statusText.textContent = 'متصل بالخادم';
                connectionStatus.textContent = 'تم الاتصال بالخادم وقاعدة البيانات بنجاح ✅';
                connectionStatus.className = 'connection-status connected';
            } else {
                statusIndicator.classList.remove('connected');
                statusText.textContent = 'غير متصل';
                connectionStatus.textContent = message;
                connectionStatus.className = 'connection-status error';
            }
        }

        // Start Masar import process
        function startMasarImport() {
            if (!serverConnected) {
                log('❌ لا يمكن بدء الاستيراد - الخادم غير متصل', 'error');
                return;
            }
            
            currentOperation = 'masar';
            log('🔄 بدء عملية استيراد اللوائح من منظومة مسار...', 'progress');
            document.getElementById('excelFile').click();
        }

        // Start Secret Codes import process
        function startSecretCodesImport() {
            if (!serverConnected) {
                log('❌ لا يمكن بدء الاستيراد - الخادم غير متصل', 'error');
                return;
            }
            
            currentOperation = 'secret';
            log('🔄 بدء عملية استيراد الرمز السري...', 'progress');
            document.getElementById('secretFilesInput').click();
        }

        // Start Teachers import process
        function startTeachersImport() {
            if (!serverConnected) {
                log('❌ لا يمكن بدء الاستيراد - الخادم غير متصل', 'error');
                return;
            }
            
            currentOperation = 'teachers';
            log('🔄 بدء عملية استيراد أسماء الأساتذة والمواد المدرسة...', 'progress');
            document.getElementById('teachersFileInput').click();
        }

        // Handle single file selection (for Masar import)
        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (!file) {
                log('⚠️ تم إلغاء اختيار الملف', 'warning');
                return;
            }

            currentFile = file;
            const fileName = file.name;

            log(`📁 تم اختيار الملف: ${fileName}`, 'info');

            // Check if filename contains "ListEleve"
            if (!fileName.includes('ListEleve')) {
                const message = `الملف ${fileName} لا يحتوي على العبارة 'ListEleve' في اسمه. قد لا يكون هذا ملف لوائح منظومة مسار.`;
                log(`⚠️ ${message}`, 'warning');
                
                document.getElementById('modalMessage').textContent = message;
                document.getElementById('confirmModal').style.display = 'block';
            } else {
                shouldProceed = true;
                processMasarFile();
            }
        }

        // Handle multiple files selection (for Secret Codes import)
        function handleSecretFilesSelect(event) {
            const files = Array.from(event.target.files);
            if (!files || files.length === 0) {
                log('⚠️ تم إلغاء اختيار الملفات', 'warning');
                return;
            }

            selectedFiles = files;

            if (files.length > 100) {
                log('⚠️ تم اختيار أكثر من 100 ملف. سيتم استيراد أول 100 ملف فقط.', 'warning');
                selectedFiles = files.slice(0, 100);
            }

            log(`📁 تم اختيار ${selectedFiles.length} ملف للاستيراد`, 'info');
            
            // Show files info
            showFilesInfo(selectedFiles);
            
            shouldProceed = true;
            processSecretCodesFiles();
        }

        // Handle single file selection (for Teachers import)
        function handleTeachersFileSelect(event) {
            const file = event.target.files[0];
            if (!file) {
                log('⚠️ تم إلغاء اختيار الملف', 'warning');
                return;
            }

            currentFile = file;
            const fileName = file.name;

            log(`📁 تم اختيار الملف: ${fileName}`, 'info');

            // Check if filename contains expected keywords
            const validKeywords = ["SeancesEnseignants", "Book"];
            const isValidFile = validKeywords.some(keyword => fileName.includes(keyword));
            
            if (!isValidFile) {
                const message = `الملف ${fileName} لا يحتوي على "SeancesEnseignants" أو "Book" في اسمه. قد لا يكون هذا ملف أسماء الأساتذة صحيح.`;
                log(`⚠️ ${message}`, 'warning');
                
                document.getElementById('modalMessage').textContent = message;
                document.getElementById('confirmModal').style.display = 'block';
            } else {
                shouldProceed = true;
                processTeachersFile();
            }
        }

        // Show files information
        function showFilesInfo(files) {
            // Remove existing files info
            const existingInfo = document.querySelector('.files-info');
            if (existingInfo) {
                existingInfo.remove();
            }

            const filesInfo = document.createElement('div');
            filesInfo.className = 'files-info';
            filesInfo.innerHTML = `
                <strong>📁 الملفات المختارة (${files.length}):</strong><br>
                ${files.slice(0, 10).map(f => `• ${f.name}`).join('<br>')}
                ${files.length > 10 ? `<br>... و ${files.length - 10} ملف آخر` : ''}
            `;
            
            document.getElementById('progressContainer').parentNode.insertBefore(filesInfo, document.getElementById('progressContainer'));
        }

        // Confirm import after warning        function confirmImport() {
            document.getElementById('confirmModal').style.display = 'none';
            log('✅ تم اختيار الاستمرار في الاستيراد رغم التحذير', 'info');
            shouldProceed = true;
            
            if (currentOperation === 'masar') {
                processMasarFile();
            } else if (currentOperation === 'teachers') {                processTeachersFile();
            }

        // Cancel import
        function cancelImport() {
            document.getElementById('confirmModal').style.display = 'none';
            log('❌ تم إلغاء عملية الاستيراد بناءً على طلب المستخدم', 'info');
            currentFile = null;
            selectedFiles = [];
            shouldProceed = false;
        }

        // Process Masar file
        async function processMasarFile() {
            if (!currentFile || !shouldProceed || !serverConnected) return;

            try {
                // Show progress bar
                document.getElementById('progressContainer').style.display = 'block';
                updateProgress(0, 'جاري رفع الملف إلى الخادم...');

                // Prepare form data
                const formData = new FormData();
                formData.append('file', currentFile);

                // Upload and process file
                const response = await fetch(`${SERVER_URL}/api/import-excel`, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.status === 'success') {
                    // Process messages
                    for (const msg of result.messages) {
                        if (msg.type === 'progress' && msg.progress) {
                            updateProgress(msg.progress, msg.message.split(' - ')[1] || msg.message);
                        }
                        log(msg.message, msg.type);
                    }

                    // Show summary
                    if (result.summary) {
                        showMasarSummary(result.summary);
                    }

                    log('🎉 تم الانتهاء من استيراد البيانات بنجاح!', 'success');
                    log('✨ نتمنى لك التوفيق والنجاح في عملك!', 'success');

                } else {
                    handleImportError(result);
                }

                // Hide progress bar after delay
                setTimeout(() => {
                    document.getElementById('progressContainer').style.display = 'none';
                }, 3000);

            } catch (error) {
                log(`❌ خطأ في الاتصال بالخادم: ${error.message}`, 'error');
                updateProgress(0, 'خطأ في الاتصال');
                setTimeout(() => {
                    document.getElementById('progressContainer').style.display = 'none';
                }, 3000);
            }
        }

        // Process Secret Codes files
        async function processSecretCodesFiles() {
            if (!selectedFiles || selectedFiles.length === 0 || !shouldProceed || !serverConnected) return;

            try {
                // Show progress bar
                document.getElementById('progressContainer').style.display = 'block';
                updateProgress(0, 'جاري رفع الملفات إلى الخادم...');

                // Prepare form data
                const formData = new FormData();
                selectedFiles.forEach(file => {
                    formData.append('files', file);
                });

                // Upload and process files
                const response = await fetch(`${SERVER_URL}/api/import-secret-codes`, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.status === 'success') {
                    // Process messages
                    for (const msg of result.messages) {
                        if (msg.type === 'progress' && msg.progress) {
                            updateProgress(msg.progress, msg.message.split(' - ')[1] || msg.message);
                        }
                        log(msg.message, msg.type);
                    }

                    // Show summary
                    if (result.summary) {
                        showSecretCodesSummary(result.summary);
                    }

                    log('🎉 تم الانتهاء من استيراد الرموز السرية بنجاح!', 'success');
                    log('✨ نتمنى لك التوفيق والنجاح في عملك!', 'success');

                } else {
                    handleImportError(result);
                }

                // Hide progress bar after delay
                setTimeout(() => {
                    document.getElementById('progressContainer').style.display = 'none';
                }, 3000);

            } catch (error) {
                log(`❌ خطأ في الاتصال بالخادم: ${error.message}`, 'error');
                updateProgress(0, 'خطأ في الاتصال');
                setTimeout(() => {
                    document.getElementById('progressContainer').style.display = 'none';
                }, 3000);
            }
        }

        // Process Teachers file
        async function processTeachersFile() {
            if (!currentFile || !shouldProceed || !serverConnected) return;

            try {
                // Show progress bar
                document.getElementById('progressContainer').style.display = 'block';
                updateProgress(0, 'جاري رفع الملف إلى الخادم...');

                // Prepare form data
                const formData = new FormData();
                formData.append('file', currentFile);

                // Upload and process file
                const response = await fetch(`${SERVER_URL}/api/import-teachers`, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.status === 'success') {
                    // Process messages
                    for (const msg of result.messages) {
                        if (msg.type === 'progress' && msg.progress) {
                            updateProgress(msg.progress, msg.message.split(' - ')[1] || msg.message);
                        }
                        log(msg.message, msg.type);
                    }

                    // Show summary
                    if (result.summary) {
                        showTeachersSummary(result.summary);
                    }

                    log('🎉 تم الانتهاء من استيراد أسماء الأساتذة والمواد المدرسة بنجاح!', 'success');
                    log('✨ نتمنى لك التوفيق والنجاح في عملك!', 'success');

                } else {
                    handleImportError(result);
                }

                // Hide progress bar after delay
                setTimeout(() => {
                    document.getElementById('progressContainer').style.display = 'none';
                }, 3000);

            } catch (error) {
                log(`❌ خطأ في الاتصال بالخادم: ${error.message}`, 'error');
                updateProgress(0, 'خطأ في الاتصال');
                setTimeout(() => {
                    document.getElementById('progressContainer').style.display = 'none';
                }, 3000);
            }
        }

        // Handle import errors
        function handleImportError(result) {
            if (result.messages) {
                for (const msg of result.messages) {
                    log(msg.message, msg.type);
                }
            } else {
                log(`❌ خطأ في معالجة الملف: ${result.error || 'خطأ غير معروف'}`, 'error');
            }
            
            updateProgress(0, 'حدث خطأ في العملية');
        }

        // Update progress bar
        function updateProgress(percent, text) {
            document.getElementById('progressBar').style.width = percent + '%';
            document.getElementById('progressBar').textContent = percent + '%';
            document.getElementById('progressText').textContent = text;
        }

        // Show Masar import summary
        function showMasarSummary(summary) {
            const summarySection = document.getElementById('summarySection');
            const summaryContent = document.getElementById('summaryContent');

            let summaryHTML = '<h4>📥 ملخص استيراد اللوائح من منظومة مسار:</h4>';

            if (summary.academic_year) {
                summaryHTML += `<div class="summary-item">📅 السنة الدراسية: ${summary.academic_year}</div>`;
            }

            if (summary.sections && summary.sections.length > 0) {
                summaryHTML += `<div class="summary-item">📚 الأقسام المستوردة: ${summary.sections.join(', ')}</div>`;
                summaryHTML += `<div class="summary-item">🔢 مجموع الأقسام: ${summary.sections_count}</div>`;
            }

            if (summary.students_count !== undefined) {
                summaryHTML += `<div class="summary-item">👨‍👩‍👧‍👦 مجموع التلاميذ: ${summary.students_count}</div>`;
            }

            if (summary.error) {
                summaryHTML += `<div class="summary-item">❌ خطأ في الملخص: ${summary.error}</div>`;
            }

            summaryContent.innerHTML = summaryHTML;
            summarySection.style.display = 'block';
        }

        // Show Secret Codes import summary
        function showSecretCodesSummary(summary) {
            const summarySection = document.getElementById('summarySection');
            const summaryContent = document.getElementById('summaryContent');

            let summaryHTML = '<h4>🔐 ملخص استيراد الرموز السرية:</h4>';

            if (summary.total_files !== undefined) {
                summaryHTML += `<div class="summary-item">📁 عدد الملفات المعالجة: ${summary.total_files}</div>`;
            }

            if (summary.total_records !== undefined) {
                summaryHTML += `<div class="summary-item">📝 إجمالي السجلات المستوردة: ${summary.total_records}</div>`;
            }

            if (summary.updated_records !== undefined) {
                summaryHTML += `<div class="summary-item">🔄 السجلات المحدثة في السجل العام: ${summary.updated_records}</div>`;
            }

            summaryContent.innerHTML = summaryHTML;
            summarySection.style.display = 'block';
        }

        // Show Teachers import summary
        function showTeachersSummary(summary) {
            const summarySection = document.getElementById('summarySection');
            const summaryContent = document.getElementById('summaryContent');

            let summaryHTML = '<h4>👨‍🏫 ملخص استيراد أسماء الأساتذة والمواد المدرسة:</h4>';

            if (summary.filename !== undefined) {
                summaryHTML += `<div class="summary-item">📁 اسم الملف: ${summary.filename}</div>`;
            }

            if (summary.total_records !== undefined) {
                summaryHTML += `<div class="summary-item">📝 إجمالي السجلات المستوردة: ${summary.total_records}</div>`;
            }

            summaryContent.innerHTML = summaryHTML;
            summarySection.style.display = 'block';
        }

        // Refresh data
        function refreshData() {
            log('🔄 جاري تحديث البيانات...', 'progress');
            
            // Clear log
            document.getElementById('logContainer').innerHTML = '';
            document.getElementById('summarySection').style.display = 'none';
            
            // Remove files info if exists
            const existingInfo = document.querySelector('.files-info');
            if (existingInfo) {
                existingInfo.remove();
            }
            
            updateTime();
            log('ℹ️ مرحباً بك في نظام استيراد البيانات التعليمية', 'info');
            
            // Check server connection again
            checkServerConnection();
        }

        // Show help
        function showHelp() {
            document.getElementById('helpModal').style.display = 'block';
        }

        // Close help
        function closeHelp() {
            document.getElementById('helpModal').style.display = 'none';
        }

        // Font size control functions
        function increaseFontSize() {
            if (currentFontSize < maxFontSize) {
                currentFontSize += 1;
                updateFontSize();
            }
        }

        function decreaseFontSize() {
            if (currentFontSize > minFontSize) {
                currentFontSize -= 1;
                updateFontSize();
            }
        }

        function resetFontSize() {
            currentFontSize = 13;
            updateFontSize();
        }

        function updateFontSize() {
            // Update body font size
            document.body.style.fontSize = currentFontSize + 'px';
            
            // Update font size display
            document.getElementById('fontSizeDisplay').textContent = currentFontSize + 'px';
            
            // Update button font sizes (keep them slightly larger)
            const buttons = document.querySelectorAll('.import-button, .refresh-button, .help-button, .modal-button');
            buttons.forEach(button => {
                button.style.fontSize = (currentFontSize + 1) + 'px';
            });
            
            // Update log container font size (keep it slightly smaller for readability)
            const logContainer = document.getElementById('logContainer');
            if (logContainer) {
                logContainer.style.fontSize = (currentFontSize - 1) + 'px';
            }
            
            // Update progress text
            const progressText = document.getElementById('progressText');
            if (progressText) {
                progressText.style.fontSize = currentFontSize + 'px';
            }
            
            // Update progress bar text
            const progressBar = document.getElementById('progressBar');
            if (progressBar) {
                progressBar.style.fontSize = (currentFontSize - 1) + 'px';
            }
              // Update help modal content
            const helpModal = document.querySelector('#helpModal .modal-content > div');
            if (helpModal) {
                helpModal.style.fontSize = currentFontSize + 'px';
            }
            
            // Update header title size (keep it proportionally larger)
            const headerTitle = document.querySelector('.header h1');
            if (headerTitle) {
                headerTitle.style.fontSize = (currentFontSize * 1.8) + 'px';
            }
            
            // Save font size to localStorage
            localStorage.setItem('preferredFontSize', currentFontSize);
        }

        // Load saved font size from localStorage
        function loadSavedFontSize() {
            const savedSize = localStorage.getItem('preferredFontSize');
            if (savedSize) {
                currentFontSize = parseInt(savedSize);
                updateFontSize();
            }
        }

        // Close modals when clicking outside
        window.onclick = function(event) {
            const confirmModal = document.getElementById('confirmModal');
            const helpModal = document.getElementById('helpModal');
            
            if (event.target === confirmModal) {
                cancelImport();
            }
            if (event.target === helpModal) {
                closeHelp();
            }
        }

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            updateTime();
            setInterval(updateTime, 1000);
            
            // Check server connection on load
            setTimeout(checkServerConnection, 1000);
            
            // Retry connection every 30 seconds if not connected
            setInterval(() => {
                if (!serverConnected) {
                    checkServerConnection();
                }
            }, 30000);            // Load saved font size
            loadSavedFontSize();
        });
    </script>
</body>
</html>
