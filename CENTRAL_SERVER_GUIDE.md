# دليل الخادم المركزي لمنظومة إدارة التعليم

## نظرة عامة
تم توحيد جميع الخوادم المنفصلة في خادم واحد مركزي شامل (`masar_server.py`) يحتوي على جميع الوظائف:

## الملفات الرئيسية
- **`masar_server.py`** - الخادم المركزي الموحد (🔴 الملف الرئيسي)
- **`import_data_system.html`** - واجهة استيراد البيانات من مسار
- **`settings_window.html`** - واجهة إعدادات البرنامج
- **`index.html`** - الواجهة الرئيسية لإدارة الطلاب
- **`data.db`** - قاعدة البيانات الرئيسية

## كيفية التشغيل

### الطريقة الأولى: ملف Batch
```bash
# انقر مرتين على الملف:
run_main_server.bat
```

### الطريقة الثانية: سطر الأوامر
```bash
cd "c:\Users\<USER>\Desktop\csv\taheri77"
python masar_server.py
```

## الوظائف المتوفرة

### 🏠 الصفحة الرئيسية
**الرابط:** `http://localhost:5000`
- واجهة استيراد البيانات من منظومة مسار
- استيراد ملفات Excel
- استيراد الرمز السري
- استيراد بيانات الأساتذة

### ⚙️ إعدادات البرنامج  
**الرابط:** `http://localhost:5000/settings`
- 🗑️ حذف جميع البيانات
- 🔄 تهيئة البرنامج لبداية موسم جديد
- 💾 نسخ احتياطي للبيانات
- 📂 استيراد نسخة احتياطية
- 📊 استيراد البيانات من ملف إكسل
- 🖨️ إعدادات الطابعة

### 📊 API للمطورين
جميع واجهات API متوفرة تحت `http://localhost:5000/api/`

## الوظائف المدمجة

### من settings_server.py:
- `/api/delete-all-data` - حذف جميع البيانات
- `/api/prepare-new-season` - تهيئة موسم جديد
- `/api/backup-database` - نسخ احتياطي
- `/api/restore-backup` - استعادة نسخة احتياطية
- `/api/import-excel` - استيراد من Excel

### من server.py:
- `/api/test` - اختبار الاتصال
- `/api/students` - إدارة الطلاب
- `/api/groups` - المجموعات
- `/api/sections` - الأقسام
- `/api/statistics` - الإحصائيات

### من masar_server.py الأصلي:
- `/api/check-database` - فحص قاعدة البيانات
- `/api/import-students` - استيراد الطلاب
- `/api/import-secret-codes` - استيراد الرمز السري
- `/api/import-teachers` - استيراد الأساتذة

## الملفات المحذوفة/المستبدلة

### ❌ لم تعد هناك حاجة لهذه الملفات:
- `settings_server.py` ➡️ مدمج في `masar_server.py`
- `server.py` ➡️ مدمج في `masar_server.py` 
- `run_settings.bat` ➡️ استبدل بـ `run_main_server.bat`

### ✅ يمكن الاحتفاظ بها كنسخة احتياطية ولكن لا تشغلها:
الملفات المدمجة متوفرة كمرجع فقط

## المتطلبات التقنية

### مكتبات Python المطلوبة:
```bash
pip install flask flask-cors pandas openpyxl
```

### بنية قاعدة البيانات:
- SQLite3 مع دعم UTF-8
- جداول متعددة للطلاب والأساتذة والإعدادات

## الأمان والحماية

### كلمات المرور:
- حذف البيانات: `12345`
- تهيئة الموسم الجديد: `12345`

### إجراءات الأمان:
- نسخ احتياطية تلقائية قبل العمليات الحساسة
- فحص سلامة قاعدة البيانات
- تشفير الاتصالات

## استكشاف الأخطاء

### مشاكل شائعة:
1. **"Port already in use"**: أغلق أي خادم آخر يعمل على المنفذ 5000
2. **"Database locked"**: تأكد من إغلاق جميع الاتصالات بقاعدة البيانات
3. **"Module not found"**: تأكد من تثبيت جميع المكتبات المطلوبة

### حلول سريعة:
```bash
# إيقاف جميع العمليات على المنفذ 5000
netstat -ano | findstr :5000
taskkill /PID [PID_NUMBER] /F

# إعادة تثبيت المكتبات
pip install --upgrade flask flask-cors pandas openpyxl
```

## اختبار النظام

### للتأكد من عمل النظام:
1. شغل `run_main_server.bat`
2. انتقل إلى `http://localhost:5000`
3. اختبر رفع ملف Excel
4. انتقل إلى `http://localhost:5000/settings`
5. اختبر عملية النسخ الاحتياطي

## التحديثات المستقبلية

### مميزات مخططة:
- واجهة موحدة لجميع الوظائف
- نظام مصادقة متقدم
- تقارير تفاعلية
- دعم قواعد بيانات متعددة

---

**ملاحظة مهمة:** استخدم فقط `masar_server.py` والملفات المرتبطة به. الملفات الأخرى (settings_server.py, server.py) لم تعد مطلوبة للتشغيل ولكن محفوظة كمرجع.
