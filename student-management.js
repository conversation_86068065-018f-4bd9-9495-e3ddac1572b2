// نظام إدارة بيانات الطلاب مع قاعدة البيانات الحقيقية
class StudentManagementSystem {
    constructor() {
        this.students = [];
        this.filteredStudents = [];
        this.selectedStudent = null;
        this.groups = [];
        this.sections = [];
        this.apiUrl = 'http://localhost:5000/api'; // عنوان الخادم
        
        this.init();
    }    async init() {
        this.setupEventListeners();
        await this.testConnection();
        await this.loadData();
        this.updateStatistics();
        this.initializeTabs();
        this.initializeInstitutionTab();
        this.initializeFontSettings();
    }async testConnection() {
        try {
            this.showLoading(true);
            const response = await fetch(`${this.apiUrl}/test`);
            const result = await response.json();
            
            if (result.status === 'success') {
                this.showAlert('نجح', `تم الاتصال بقاعدة البيانات بنجاح - إجمالي السجلات: ${result.total_records}`, 'success');
            } else {
                this.showAlert('خطأ', result.error, 'error');
            }
        } catch (error) {
            this.showAlert('خطأ', 'فشل في الاتصال بالخادم. تأكد من تشغيل server.py', 'error');
            console.error('خطأ في الاتصال:', error);
        } finally {
            this.showLoading(false);
        }
    }    async loadData() {
        try {
            this.showLoading(true);
            
            // تحميل بيانات الطلاب
            const studentsResponse = await fetch(`${this.apiUrl}/students`);
            if (!studentsResponse.ok) {
                throw new Error(`HTTP error! status: ${studentsResponse.status}`);
            }
            
            const studentsResult = await studentsResponse.json();
            
            // API يُرجع مصفوفة مباشرة، ليس كائناً مع خاصية success
            if (Array.isArray(studentsResult)) {
                this.students = studentsResult;
                this.filteredStudents = [...this.students];
                console.log(`تم تحميل ${this.students.length} طالب من قاعدة البيانات`);
            } else if (studentsResult.error) {
                throw new Error(studentsResult.error);
            }
            
            // تحميل المجموعات
            const groupsResponse = await fetch(`${this.apiUrl}/groups`);
            if (groupsResponse.ok) {
                const groupsResult = await groupsResponse.json();
                
                if (Array.isArray(groupsResult)) {
                    this.groups = groupsResult;
                } else if (groupsResult.error) {
                    console.warn('تحذير في تحميل المجموعات:', groupsResult.error);
                }
            }
            
            // تحميل الأقسام
            const sectionsResponse = await fetch(`${this.apiUrl}/sections`);
            if (sectionsResponse.ok) {
                const sectionsResult = await sectionsResponse.json();
                
                if (Array.isArray(sectionsResult)) {
                    this.sections = sectionsResult;
                }
            }
            
            this.loadFilterOptions();
            this.renderTable();
            
        } catch (error) {
            this.showAlert('خطأ', `فشل في تحميل البيانات من قاعدة البيانات: ${error.message}`, 'error');
            console.error('خطأ في تحميل البيانات:', error);
        } finally {
            this.showLoading(false);
        }
    }

    setupEventListeners() {
        // أزرار العمليات
        document.querySelector('.registration-btn').addEventListener('click', () => this.handleRegistration());
        document.querySelector('.monthly-btn').addEventListener('click', () => this.handleMonthlyDuties());
        document.querySelector('.reports-btn').addEventListener('click', () => this.handleReports());
        document.querySelector('.delete-btn').addEventListener('click', () => this.handleDelete());
        document.querySelector('.query-btn').addEventListener('click', () => this.handleQuery());
        document.querySelector('.cancel-btn').addEventListener('click', () => this.handleCancel());
        document.querySelector('.restore-btn').addEventListener('click', () => this.handleRestore());
        document.querySelector('.refresh-btn').addEventListener('click', () => this.handleRefresh());
        document.querySelector('.delete-record-btn').addEventListener('click', () => this.handleDeleteRecord());        // فلاتر البحث والتصفية
        document.getElementById('search-input').addEventListener('input', (e) => this.handleSearch(e.target.value));
        document.getElementById('group-filter').addEventListener('change', (e) => this.handleGroupFilter(e.target.value));
        document.getElementById('section-filter').addEventListener('change', (e) => this.handleSectionFilter(e.target.value));
        document.getElementById('month-filter').addEventListener('change', (e) => this.handleMonthFilter(e.target.value));
        
        // تهيئة التبويبات
        this.initializeTabs();
    }loadFilterOptions() {
        // تحميل المجموعات
        const groupSelect = document.getElementById('group-filter');
        groupSelect.innerHTML = '<option value="">جميع المجموعات</option>';
        this.groups.forEach(group => {
            const option = document.createElement('option');
            option.value = group;
            option.textContent = group;
            groupSelect.appendChild(option);
        });
        
        // تحميل الأقسام
        const sectionSelect = document.getElementById('section-filter');
        sectionSelect.innerHTML = '<option value="">جميع الأقسام</option>';
        this.sections.forEach(section => {
            const option = document.createElement('option');
            option.value = section;
            option.textContent = section;
            sectionSelect.appendChild(option);
        });    }    async handleGroupFilter(selectedGroup) {
        const sectionSelect = document.getElementById('section-filter');
        sectionSelect.innerHTML = '<option value="">جميع الأقسام</option>';

        if (selectedGroup) {
            try {
                // جلب الأقسام حسب المجموعة المختارة من الخادم
                const response = await fetch(`/api/sections/${selectedGroup}`);
                const sections = await response.json();
                
                sections.forEach(section => {
                    const option = document.createElement('option');
                    option.value = section;
                    option.textContent = section;
                    sectionSelect.appendChild(option);
                });
            } catch (error) {
                console.error('خطأ في جلب الأقسام:', error);
                // في حالة الخطأ، نستخدم جميع الأقسام كبديل
                this.sections.forEach(section => {
                    const option = document.createElement('option');
                    option.value = section;
                    option.textContent = section;
                    sectionSelect.appendChild(option);
                });
            }
        } else {
            // إذا لم يتم اختيار مجموعة، نعرض جميع الأقسام
            this.sections.forEach(section => {
                const option = document.createElement('option');
                option.value = section;
                option.textContent = section;
                sectionSelect.appendChild(option);
            });
        }

        this.applyFilters();
    }

    handleSectionFilter() {
        this.applyFilters();
    }

    handleSearch(searchTerm) {
        this.applyFilters();
    }

    handleMonthFilter() {
        this.applyFilters();
    }    async applyFilters() {
        try {
            this.showLoading(true);
            
            const searchTerm = document.getElementById('search-input').value.trim();
            const selectedGroup = document.getElementById('group-filter').value;
            const selectedSection = document.getElementById('section-filter').value;
            const selectedMonth = document.getElementById('month-filter').value;

            // بناء URL مع query parameters
            const params = new URLSearchParams();
            if (searchTerm) params.append('search', searchTerm);
            if (selectedGroup) params.append('group', selectedGroup);
            if (selectedSection) params.append('section', selectedSection);
            if (selectedMonth) params.append('month', selectedMonth);

            const url = `${this.apiUrl}/filter${params.toString() ? '?' + params.toString() : ''}`;
            const response = await fetch(url);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            
            if (Array.isArray(result)) {
                this.filteredStudents = result;
                this.renderTable();
                this.updateStatistics();
            } else if (result.error) {
                throw new Error(result.error);
            }
            
        } catch (error) {
            this.showAlert('خطأ', `فشل في تطبيق التصفية: ${error.message}`, 'error');
            console.error('خطأ في التصفية:', error);        } finally {
            this.showLoading(false);
        }
    }

    renderTable() {
        const tbody = document.getElementById('table-body');
        tbody.innerHTML = '';

        this.filteredStudents.forEach((student, index) => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <div class="checkbox-container">
                        <input type="checkbox" class="student-checkbox" data-student-id="${student.id}" 
                               onchange="studentSystem.handleStudentSelection(${student.id}, this.checked)">
                    </div>
                </td>
                <td>${student.id}</td>
                <td>${student.name || 'غير محدد'}</td>
                <td>${student.code || 'غير محدد'}</td>
                <td>${student.type || 'غير محدد'}</td>
                <td>${student.phone || 'غير محدد'}</td>
                <td>${student.section || 'غير محدد'}</td>
                <td>${student.group || 'غير محدد'}</td>                <td>${(student.registration_fee || 0).toLocaleString()} د.م</td>
                <td>${(student.monthly_fee || 0).toLocaleString()} د.م</td>
                <td>
                    <span class="payment-status ${this.getPaymentStatusClass(student.payment_status)}">
                        ${student.payment_status || 'غير محدد'}
                    </span>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    getPaymentStatusClass(status) {
        switch(status) {
            case 'مدفوع': return 'payment-paid';
            case 'غير مدفوع': return 'payment-unpaid';
            case 'مدفوع جزئياً': return 'payment-partial';
            default: return '';
        }
    }

    handleStudentSelection(studentId, isSelected) {
        // إزالة التحديد من جميع الطلاب الآخرين (تحديد واحد فقط)
        document.querySelectorAll('.student-checkbox').forEach(checkbox => {
            if (parseInt(checkbox.dataset.studentId) !== studentId) {
                checkbox.checked = false;
            }
        });

        // إزالة التحديد من جميع الصفوف
        document.querySelectorAll('#students-table tbody tr').forEach(row => {
            row.classList.remove('selected-row');
        });

        if (isSelected) {
            this.selectedStudent = this.students.find(s => s.id === studentId) || 
                                   this.filteredStudents.find(s => s.id === studentId);
            // إضافة التحديد للصف الحالي
            const checkbox = document.querySelector(`[data-student-id="${studentId}"]`);
            const row = checkbox.closest('tr');
            row.classList.add('selected-row');
        } else {
            this.selectedStudent = null;
        }

        this.updateStatistics();
    }

    updateStatistics() {
        const totalStudents = this.filteredStudents.length;
        const registeredStudents = this.filteredStudents.filter(s => s.payment_status === 'مدفوع').length;
        const totalRecords = this.students.length;

        document.getElementById('total-students').textContent = totalStudents;
        document.getElementById('registered-students').textContent = registeredStudents;
        document.getElementById('total-records').textContent = totalRecords;
    }

    // إدارة التبويبات
    initializeTabs() {
        const tabButtons = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');

        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const targetTab = button.getAttribute('data-tab');
                
                // إزالة الحالة النشطة من جميع الأزرار والمحتويات
                tabButtons.forEach(btn => btn.classList.remove('active'));
                tabContents.forEach(content => content.classList.remove('active'));
                
                // تفعيل الزر والمحتوى المحدد
                button.classList.add('active');
                document.getElementById(`${targetTab}-tab`).classList.add('active');
                
                // تحديث البيانات إذا كان التبويب النشط هو الطلاب
                if (targetTab === 'students') {
                    this.loadInitialData();
                } else if (targetTab === 'financial') {
                    this.updateFinancialStats();
                } else if (targetTab === 'institution') {
                    this.initializeInstitutionTab();
                } else if (targetTab === 'setup') {
                    this.initializeSetupTab();
                }
            });
        });
        
        // تهيئة التبويب الافتراضي (بيانات المؤسسة)
        this.initializeInstitutionTab();
        
        // تهيئة التبويبات العمودية
        this.initializeVerticalTabs();
    }

    // تهيئة التبويبات العمودية
    initializeVerticalTabs() {
        const verticalTabButtons = document.querySelectorAll('.vertical-tab-btn');
        const verticalTabPanels = document.querySelectorAll('.vertical-tab-panel');

        verticalTabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const targetPanel = button.getAttribute('data-vertical-tab');
                
                // إزالة الحالة النشطة من جميع الأزرار والألواح
                verticalTabButtons.forEach(btn => btn.classList.remove('active'));
                verticalTabPanels.forEach(panel => panel.classList.remove('active'));
                
                // تفعيل الزر واللوح المحدد
                button.classList.add('active');
                document.getElementById(`${targetPanel}-panel`).classList.add('active');
                
                // تحديث محتوى التبويب حسب النوع
                this.updateVerticalTabContent(targetPanel);
            });
        });
    }

    // تهيئة تبويب تهيئة البرنامج
    initializeSetupTab() {
        // تحميل معلومات قاعدة البيانات
        this.loadDatabaseInfo();
        
        // ربط أحداث أزرار الاستيراد
        this.bindSetupEvents();
        
        // إضافة رسالة ترحيبية في السجل
        this.addLogEntry('مرحباً بك في نظام تهيئة البرنامج', 'info');
    }

    // ربط أحداث تبويب التهيئة
    bindSetupEvents() {
        // أزرار استيراد اللوائح
        const selectMasarFileBtn = document.getElementById('select-masar-file');
        const startImportBtn = document.getElementById('start-import');
        
        if (selectMasarFileBtn) {
            selectMasarFileBtn.addEventListener('click', () => this.selectMasarFile());
        }
        
        if (startImportBtn) {
            startImportBtn.addEventListener('click', () => this.startMasarImport());
        }

        // أزرار تحديث البيانات
        const updateStructureBtn = document.getElementById('update-structure');
        const recalculateTotalsBtn = document.getElementById('recalculate-totals');
        const validateDataBtn = document.getElementById('validate-data');
        
        if (updateStructureBtn) {
            updateStructureBtn.addEventListener('click', () => this.updateStructure());
        }
        
        if (recalculateTotalsBtn) {
            recalculateTotalsBtn.addEventListener('click', () => this.recalculateTotals());
        }
        
        if (validateDataBtn) {
            validateDataBtn.addEventListener('click', () => this.validateData());
        }

        // أزرار الرموز السرية
        const selectCodesFileBtn = document.getElementById('select-codes-file');
        const importCodesBtn = document.getElementById('import-codes');
        
        if (selectCodesFileBtn) {
            selectCodesFileBtn.addEventListener('click', () => this.selectCodesFile());
        }
        
        if (importCodesBtn) {
            importCodesBtn.addEventListener('click', () => this.importSecretCodes());
        }

        // أزرار الأساتذة
        const selectTeachersFileBtn = document.getElementById('select-teachers-file');
        const importTeachersBtn = document.getElementById('import-teachers');
        
        if (selectTeachersFileBtn) {
            selectTeachersFileBtn.addEventListener('click', () => this.selectTeachersFile());
        }
        
        if (importTeachersBtn) {
            importTeachersBtn.addEventListener('click', () => this.importTeachers());
        }

        // أزرار الطلاب
        const selectStudentsFileBtn = document.getElementById('select-students-file');
        const importStudentsBtn = document.getElementById('import-students');
        
        if (selectStudentsFileBtn) {
            selectStudentsFileBtn.addEventListener('click', () => this.selectStudentsFile());
        }
        
        if (importStudentsBtn) {
            importStudentsBtn.addEventListener('click', () => this.importStudentsData());
        }

        // أزرار قاعدة البيانات
        const optimizeDatabaseBtn = document.getElementById('optimize-database');
        const backupDatabaseBtn = document.getElementById('backup-database');
        const checkIntegrityBtn = document.getElementById('check-integrity');
        const resetDatabaseBtn = document.getElementById('reset-database');
        
        if (optimizeDatabaseBtn) {
            optimizeDatabaseBtn.addEventListener('click', () => this.optimizeDatabase());
        }
        
        if (backupDatabaseBtn) {
            backupDatabaseBtn.addEventListener('click', () => this.backupDatabase());
        }
        
        if (checkIntegrityBtn) {
            checkIntegrityBtn.addEventListener('click', () => this.checkDatabaseIntegrity());
        }
        
        if (resetDatabaseBtn) {
            resetDatabaseBtn.addEventListener('click', () => this.resetDatabase());
        }
    }

    // تحديث محتوى التبويبات العمودية
    updateVerticalTabContent(tabName) {
        switch(tabName) {
            case 'import-lists':
                this.addLogEntry('تم تفعيل وضع استيراد اللوائح وتحينها', 'info');
                break;
            case 'import-codes':
                this.addLogEntry('تم تفعيل وضع استيراد الرموز السرية', 'info');
                break;
            case 'import-teachers':
                this.addLogEntry('تم تفعيل وضع استيراد بيانات الأساتذة', 'info');
                break;
            case 'import-students':
                this.addLogEntry('تم تفعيل وضع استيراد بيانات الطلاب', 'info');
                break;
            case 'database-settings':
                this.addLogEntry('تم تفعيل وضع إعدادات قاعدة البيانات', 'info');
                this.loadDatabaseInfo();
                break;
        }
    }

    // إضافة مدخل إلى السجل
    addLogEntry(message, type = 'info') {
        const logContainer = document.getElementById('import-log');
        if (!logContainer) return;

        const timestamp = new Date().toLocaleTimeString('ar-EG');
        const logEntry = document.createElement('div');
        logEntry.className = `log-entry ${type}`;
        
        logEntry.innerHTML = `
            <span class="log-time">[${timestamp}]</span>
            <span class="log-message">${message}</span>
        `;
        
        logContainer.appendChild(logEntry);
        logContainer.scrollTop = logContainer.scrollHeight;
    }

    // تحديث شريط التقدم
    updateProgress(percentage, message = '') {
        const progressFill = document.querySelector('.progress-fill');
        const progressText = document.querySelector('.progress-text');
        
        if (progressFill) {
            progressFill.style.width = `${percentage}%`;
        }
        
        if (progressText) {
            progressText.textContent = `${percentage}%`;
        }
        
        if (message) {
            this.addLogEntry(message, 'progress');
        }
    }

    // تحميل معلومات قاعدة البيانات
    async loadDatabaseInfo() {
        try {
            const response = await fetch(`${this.apiUrl}/database-info`);
            if (response.ok) {
                const info = await response.json();
                
                document.getElementById('db-size').textContent = info.size || '---';
                document.getElementById('db-tables').textContent = info.tables || '---';
                document.getElementById('db-records').textContent = info.records || '---';
                document.getElementById('db-last-update').textContent = info.lastUpdate || '---';
            }
        } catch (error) {
            console.error('خطأ في تحميل معلومات قاعدة البيانات:', error);
        }
    }

    // وظائف استيراد اللوائح
    selectMasarFile() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.xlsx,.xls';
        input.onchange = (e) => {
            const file = e.target.files[0];
            if (file) {
                this.selectedMasarFile = file;
                this.addLogEntry(`تم اختيار الملف: ${file.name}`, 'success');
                document.getElementById('start-import').disabled = false;
            }
        };
        input.click();
    }

    async startMasarImport() {
        if (!this.selectedMasarFile) {
            this.addLogEntry('يرجى اختيار ملف أولاً', 'error');
            return;
        }

        const importType = document.getElementById('import-type').value;
        const academicYear = document.getElementById('academic-year-import').value;
        
        this.addLogEntry('بدء عملية استيراد البيانات من منظومة مسار...', 'info');
        this.updateProgress(0, 'جاري تحضير البيانات...');

        try {
            const formData = new FormData();
            formData.append('file', this.selectedMasarFile);
            formData.append('importType', importType);
            formData.append('academicYear', academicYear);

            const response = await fetch(`${this.apiUrl}/import-masar`, {
                method: 'POST',
                body: formData
            });

            if (response.ok) {
                const result = await response.json();
                this.updateProgress(100, 'تم استيراد البيانات بنجاح!');
                this.addLogEntry(`تم استيراد ${result.recordsCount} سجل بنجاح`, 'success');
            } else {
                throw new Error('فشل في استيراد البيانات');
            }
        } catch (error) {
            this.addLogEntry(`خطأ في استيراد البيانات: ${error.message}`, 'error');
            this.updateProgress(0);
        }
    }

    // وظائف الصيانة
    async updateStructure() {
        this.addLogEntry('جاري تحديث البنية التربوية...', 'info');
        try {
            const response = await fetch(`${this.apiUrl}/update-structure`, { method: 'POST' });
            if (response.ok) {
                this.addLogEntry('تم تحديث البنية التربوية بنجاح', 'success');
            } else {
                throw new Error('فشل في تحديث البنية');
            }
        } catch (error) {
            this.addLogEntry(`خطأ في تحديث البنية: ${error.message}`, 'error');
        }
    }

    async recalculateTotals() {
        this.addLogEntry('جاري إعادة حساب المجاميع...', 'info');
        try {
            const response = await fetch(`${this.apiUrl}/recalculate-totals`, { method: 'POST' });
            if (response.ok) {
                this.addLogEntry('تم إعادة حساب المجاميع بنجاح', 'success');
            } else {
                throw new Error('فشل في إعادة حساب المجاميع');
            }
        } catch (error) {
            this.addLogEntry(`خطأ في إعادة حساب المجاميع: ${error.message}`, 'error');
        }
    }

    async validateData() {
        this.addLogEntry('جاري التحقق من صحة البيانات...', 'info');
        try {
            const response = await fetch(`${this.apiUrl}/validate-data`);
            if (response.ok) {
                const result = await response.json();
                this.addLogEntry(`فحص البيانات مكتمل. تم العثور على ${result.errors || 0} خطأ`, result.errors > 0 ? 'warning' : 'success');
            } else {
                throw new Error('فشل في فحص البيانات');
            }
        } catch (error) {
            this.addLogEntry(`خطأ في فحص البيانات: ${error.message}`, 'error');
        }
    }

    // وظائف الرموز السرية
    selectCodesFile() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.xlsx,.xls';
        input.onchange = (e) => {
            const file = e.target.files[0];
            if (file) {
                this.selectedCodesFile = file;
                this.addLogEntry(`تم اختيار ملف الرموز: ${file.name}`, 'success');
                document.getElementById('import-codes').disabled = false;
            }
        };
        input.click();
    }

    async importSecretCodes() {
        if (!this.selectedCodesFile) {
            this.addLogEntry('يرجى اختيار ملف الرموز أولاً', 'error');
            return;
        }

        this.addLogEntry('بدء استيراد الرموز السرية...', 'info');
        // تنفيذ عملية الاستيراد هنا
    }

    // وظائف الأساتذة
    selectTeachersFile() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.xlsx,.xls';
        input.onchange = (e) => {
            const file = e.target.files[0];
            if (file) {
                this.selectedTeachersFile = file;
                this.addLogEntry(`تم اختيار ملف الأساتذة: ${file.name}`, 'success');
                document.getElementById('import-teachers').disabled = false;
            }
        };
        input.click();
    }

    async importTeachers() {
        if (!this.selectedTeachersFile) {
            this.addLogEntry('يرجى اختيار ملف الأساتذة أولاً', 'error');
            return;
        }

        this.addLogEntry('بدء استيراد بيانات الأساتذة...', 'info');
        // تنفيذ عملية الاستيراد هنا
    }

    // وظائف الطلاب
    selectStudentsFile() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.xlsx,.xls';
        input.onchange = (e) => {
            const file = e.target.files[0];
            if (file) {
                this.selectedStudentsFile = file;
                this.addLogEntry(`تم اختيار ملف الطلاب: ${file.name}`, 'success');
                document.getElementById('import-students').disabled = false;
            }
        };
        input.click();
    }

    async importStudentsData() {
        if (!this.selectedStudentsFile) {
            this.addLogEntry('يرجى اختيار ملف الطلاب أولاً', 'error');
            return;
        }

        this.addLogEntry('بدء استيراد بيانات الطلاب...', 'info');
        // تنفيذ عملية الاستيراد هنا
    }

    // وظائف قاعدة البيانات
    async optimizeDatabase() {
        this.addLogEntry('جاري تحسين قاعدة البيانات...', 'info');
        try {
            const response = await fetch(`${this.apiUrl}/optimize-database`, { method: 'POST' });
            if (response.ok) {
                this.addLogEntry('تم تحسين قاعدة البيانات بنجاح', 'success');
                this.loadDatabaseInfo();
            } else {
                throw new Error('فشل في تحسين قاعدة البيانات');
            }
        } catch (error) {
            this.addLogEntry(`خطأ في تحسين قاعدة البيانات: ${error.message}`, 'error');
        }
    }

    async backupDatabase() {
        this.addLogEntry('جاري إنشاء نسخة احتياطية...', 'info');
        try {
            const response = await fetch(`${this.apiUrl}/backup-database`);
            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `backup_${new Date().toISOString().split('T')[0]}.db`;
                a.click();
                window.URL.revokeObjectURL(url);
                this.addLogEntry('تم إنشاء النسخة الاحتياطية وتحميلها', 'success');
            } else {
                throw new Error('فشل في إنشاء النسخة الاحتياطية');
            }
        } catch (error) {
            this.addLogEntry(`خطأ في إنشاء النسخة الاحتياطية: ${error.message}`, 'error');
        }
    }

    async checkDatabaseIntegrity() {
        this.addLogEntry('جاري فحص سلامة قاعدة البيانات...', 'info');
        try {
            const response = await fetch(`${this.apiUrl}/check-integrity`);
            if (response.ok) {
                const result = await response.json();
                if (result.isValid) {
                    this.addLogEntry('قاعدة البيانات سليمة وتعمل بشكل صحيح', 'success');
                } else {
                    this.addLogEntry(`تم العثور على مشاكل في قاعدة البيانات: ${result.issues.join(', ')}`, 'warning');
                }
            } else {
                throw new Error('فشل في فحص قاعدة البيانات');
            }
        } catch (error) {
            this.addLogEntry(`خطأ في فحص قاعدة البيانات: ${error.message}`, 'error');
        }
    }

    async resetDatabase() {
        const confirmed = confirm('تحذير: هذا سيحذف جميع البيانات في قاعدة البيانات. هل تريد المتابعة؟');
        if (!confirmed) return;

        this.addLogEntry('جاري إعادة تعيين قاعدة البيانات...', 'warning');
        try {
            const response = await fetch(`${this.apiUrl}/reset-database`, { method: 'POST' });
            if (response.ok) {
                this.addLogEntry('تم إعادة تعيين قاعدة البيانات بنجاح', 'success');
                this.loadDatabaseInfo();
            } else {
                throw new Error('فشل في إعادة تعيين قاعدة البيانات');
            }
        } catch (error) {
            this.addLogEntry(`خطأ في إعادة تعيين قاعدة البيانات: ${error.message}`, 'error');
        }
    }

    // إدارة بيانات المؤسسة
    initializeInstitutionTab() {
        // تحميل السنوات الدراسية
        this.loadAcademicYears();
        
        // تحميل بيانات المؤسسة
        this.loadInstitutionData();
        
        // ربط الأحداث
        this.bindInstitutionEvents();
    }

    loadAcademicYears() {
        const academicYearSelect = document.getElementById('academic-year');
        if (!academicYearSelect) return;
        
        // إضافة السنوات الدراسية من 2024/2025 إلى 20 سنة قادمة
        academicYearSelect.innerHTML = '<option value="">اختر السنة</option>';
        
        for (let i = 0; i < 20; i++) {
            const startYear = 2024 + i;
            const endYear = startYear + 1;
            const yearText = `${startYear}/${endYear}`;
            
            const option = document.createElement('option');
            option.value = yearText;
            option.textContent = yearText;
            academicYearSelect.appendChild(option);
        }
    }    async loadInstitutionData() {
        try {
            const response = await fetch(`${this.apiUrl}/institution-data`);
            if (response.ok) {
                const data = await response.json();
                this.populateInstitutionForm(data);
                this.generateInstitutionCode();
            } else {
                console.log('لم يتم العثور على بيانات المؤسسة، سيتم إنشاء نموذج فارغ');
            }
        } catch (error) {
            console.error('خطأ في تحميل بيانات المؤسسة:', error);
            this.showAlert('خطأ', 'فشل في تحميل بيانات المؤسسة', 'error');
        }
    }

    populateInstitutionForm(data) {
        if (!data) return;
        
        // ملء الحقول بالبيانات
        const fields = {
            'institution-phone': data.phone || '',
            'institution-name': data.name || '',
            'academic-year': data.academicYear || '',
            'institution-city': data.city || '',
            'institution-manager': data.manager || '',
            'registration-number': data.registrationNumber || ''
        };
        
        Object.keys(fields).forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field) {
                field.value = fields[fieldId];
            }
        });
          // تحميل الشعار إذا كان متوفراً
        if (data.logoPath && data.logoPath.trim() !== '') {
            this.displayLogo(data.logoPath);
        }
    }

    bindInstitutionEvents() {
        // حفظ البيانات
        const saveBtn = document.getElementById('save-institution-btn');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => this.saveInstitutionData());
        }
        
        // تحديث البيانات
        const refreshBtn = document.getElementById('refresh-institution-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.loadInstitutionData());
        }
        
        // عرض المعلومات
        const infoBtn = document.getElementById('show-institution-info-btn');
        if (infoBtn) {
            infoBtn.addEventListener('click', () => this.showInstitutionInfo());
        }
        
        // التحقق من التفعيل
        const verifyBtn = document.getElementById('verify-activation-btn');
        if (verifyBtn) {
            verifyBtn.addEventListener('click', () => this.verifyActivation());
        }
        
        // رفع الشعار
        const uploadBtn = document.getElementById('upload-logo-btn');
        if (uploadBtn) {
            uploadBtn.addEventListener('click', () => this.uploadLogo());
        }
        
        // حذف الشعار
        const clearBtn = document.getElementById('clear-logo-btn');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => this.clearLogo());
        }
        
        // تحديث رمز المؤسسة عند تغيير الاسم أو الهاتف
        const nameField = document.getElementById('institution-name');
        const phoneField = document.getElementById('institution-phone');
        
        if (nameField && phoneField) {
            nameField.addEventListener('input', () => this.generateInstitutionCode());
            phoneField.addEventListener('input', () => this.generateInstitutionCode());
        }
    }

    generateInstitutionCode() {
        const nameField = document.getElementById('institution-name');
        const phoneField = document.getElementById('institution-phone');
        const codeDisplay = document.getElementById('institution-code-display');
        
        if (!nameField || !phoneField || !codeDisplay) return;
        
        const institutionName = nameField.value.trim();
        const phoneNumber = phoneField.value.trim();
        
        if (!institutionName || !phoneNumber) {
            codeDisplay.textContent = 'غير متوفر';
            return;
        }
        
        // توليد رقم فريد 10 أرقام بناءً على اسم المؤسسة ورقم الهاتف
        const combinedText = `${institutionName}-${phoneNumber}`;
        
        // حساب قيمة هاش ثابتة
        let hashValue = 0;
        for (let i = 0; i < combinedText.length; i++) {
            hashValue = (hashValue * 31 + combinedText.charCodeAt(i)) & 0xFFFFFFFF;
        }
        
        // تأكد من أن الرقم يكون دائمًا 10 أرقام
        let numericCode = hashValue % 10000000000;
        if (numericCode < 1000000000) {
            numericCode += 1000000000;
        }
        
        // تنسيق الرقم
        const formattedCode = numericCode.toString().padStart(10, '0');
        codeDisplay.textContent = formattedCode;
    }

    async saveInstitutionData() {
        try {
            // جمع البيانات من النموذج
            const data = {
                phone: document.getElementById('institution-phone').value.trim(),
                name: document.getElementById('institution-name').value.trim(),
                academicYear: document.getElementById('academic-year').value,
                city: document.getElementById('institution-city').value.trim(),
                manager: document.getElementById('institution-manager').value.trim(),
                registrationNumber: document.getElementById('registration-number').value.trim()
            };
            
            // التحقق من الحقول المطلوبة
            const requiredFields = ['phone', 'name', 'academicYear', 'city', 'manager'];
            const emptyFields = requiredFields.filter(field => !data[field] || data[field] === 'اختر السنة');
              if (emptyFields.length > 0) {
                this.showAlert('تحذير', 'يرجى ملء جميع الحقول المطلوبة', 'warning');
                return;
            }
              // إرسال البيانات إلى الخادم
            const response = await fetch(`${this.apiUrl}/institution-data`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });
              if (response.ok) {
                this.showAlert('نجح', 'تم حفظ بيانات المؤسسة بنجاح', 'success');
                this.generateInstitutionCode();
                
                // التحقق من رقم التسجيل إذا كان موجوداً
                if (data.registrationNumber) {
                    this.verifyActivation();
                }
            } else {
                const error = await response.json();
                this.showAlert('خطأ', `خطأ في حفظ البيانات: ${error.error}`, 'error');
            }        } catch (error) {
            console.error('خطأ في حفظ بيانات المؤسسة:', error);
            this.showAlert('خطأ', 'حدث خطأ أثناء حفظ البيانات', 'error');
        }
    }

    uploadLogo() {
        // إنشاء عنصر input مخفي لاختيار الملف
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.accept = 'image/*';
        fileInput.style.display = 'none';
        
        fileInput.addEventListener('change', (event) => {
            const file = event.target.files[0];
            if (file) {
                // التحقق من نوع الملف
                if (!file.type.startsWith('image/')) {
                    this.showAlert('يرجى اختيار ملف صورة صالح', 'warning');
                    return;
                }
                
                // التحقق من حجم الملف (5MB كحد أقصى)
                if (file.size > 5 * 1024 * 1024) {
                    this.showAlert('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت', 'warning');
                    return;
                }
                
                // عرض الصورة
                const reader = new FileReader();
                reader.onload = (e) => {
                    this.displayLogo(e.target.result);
                    // هنا يمكن إرسال الصورة إلى الخادم
                    this.uploadLogoToServer(file);
                };
                reader.readAsDataURL(file);
            }
        });
        
        // إضافة العنصر إلى الصفحة والنقر عليه
        document.body.appendChild(fileInput);
        fileInput.click();
        document.body.removeChild(fileInput);
    }

    displayLogo(imageSrc) {
        const logoImg = document.getElementById('institution-logo');
        const placeholder = document.getElementById('logo-placeholder');
        
        if (logoImg && placeholder) {
            logoImg.src = imageSrc;
            logoImg.style.display = 'block';
            placeholder.style.display = 'none';
        }
    }

    clearLogo() {
        const logoImg = document.getElementById('institution-logo');
        const placeholder = document.getElementById('logo-placeholder');
        
        if (logoImg && placeholder) {
            logoImg.src = '';
            logoImg.style.display = 'none';
            placeholder.style.display = 'block';
        }
        
        this.showAlert('تم حذف الشعار', 'info');
    }

    async uploadLogoToServer(file) {
        try {
            const formData = new FormData();
            formData.append('logo', file);
            
            const response = await fetch('/api/upload-logo', {
                method: 'POST',
                body: formData
            });
            
            if (response.ok) {
                this.showAlert('تم رفع الشعار بنجاح', 'success');
            } else {
                this.showAlert('خطأ في رفع الشعار', 'error');
            }
        } catch (error) {
            console.error('خطأ في رفع الشعار:', error);
            this.showAlert('حدث خطأ أثناء رفع الشعار', 'error');
        }
    }

    showInstitutionInfo() {
        const data = {
            phone: document.getElementById('institution-phone').value,
            name: document.getElementById('institution-name').value,
            academicYear: document.getElementById('academic-year').value,
            city: document.getElementById('institution-city').value,
            manager: document.getElementById('institution-manager').value,
            registrationNumber: document.getElementById('registration-number').value,
            code: document.getElementById('institution-code-display').textContent
        };
        
        const infoText = `
📋 معلومات المؤسسة:

🏢 اسم المؤسسة: ${data.name || 'غير محدد'}
📞 رقم الهاتف: ${data.phone || 'غير محدد'}
🏙️ المدينة: ${data.city || 'غير محدد'}
👤 المسؤول: ${data.manager || 'غير محدد'}
📅 السنة الدراسية: ${data.academicYear || 'غير محدد'}
🔢 رمز المؤسسة: ${data.code || 'غير متوفر'}
🔐 رقم التسجيل: ${data.registrationNumber || 'غير محدد'}
        `;
        
        alert(infoText);
    }

    verifyActivation() {
        const registrationNumber = document.getElementById('registration-number').value.trim();
        const institutionCode = document.getElementById('institution-code-display').textContent;
        
        if (!registrationNumber) {
            this.showAlert('يرجى إدخال رقم التسجيل أولاً', 'warning');
            return;
        }
        
        if (!institutionCode || institutionCode === 'غير متوفر') {
            this.showAlert('يرجى ملء بيانات المؤسسة أولاً لإنشاء الرمز', 'warning');
            return;
        }
        
        // تطبيق معادلة التحقق: (رمز المؤسسة * 98) + (أول 3 أرقام * 71)
        try {
            const codeInt = parseInt(institutionCode);
            const firstThreeDigits = parseInt(institutionCode.substring(0, 3));
            const expectedRegistration = (codeInt * 98) + (firstThreeDigits * 71);
            
            if (parseInt(registrationNumber) === expectedRegistration) {
                this.showAlert('✅ رقم التسجيل صحيح! تم تفعيل البرنامج بنجاح', 'success');
            } else {
                this.showAlert('❌ رقم التسجيل غير صحيح', 'error');
            }        } catch (error) {
            this.showAlert('خطأ في التحقق من رقم التسجيل', 'error');
        }
    }

    // إعدادات الخطوط
    initializeFontSettings() {
        // تحميل الإعدادات المحفوظة
        this.loadFontSettings();
        
        // ربط الأحداث
        this.bindFontEvents();
        
        // تحديث المعاينة
        this.updateFontPreview();
    }    bindFontEvents() {
        // ربط تغيير إعدادات الخطوط بالمعاينة
        const fontControls = [
            'main-font-family', 'main-font-size', 'main-font-style', 'main-font-weight',
            'header-font-family', 'header-font-size', 'header-font-style', 'header-font-weight',
            'subheader-font-family', 'subheader-font-size', 'subheader-font-style', 'subheader-font-weight',
            'table-font-size', 'table-font-weight'
        ];

        fontControls.forEach(controlId => {
            const control = document.getElementById(controlId);
            if (control) {
                control.addEventListener('change', () => {
                    this.updateFontPreview();
                });
            }
        });

        // زر تطبيق الإعدادات
        const applyBtn = document.getElementById('apply-font-settings');
        if (applyBtn) {
            applyBtn.addEventListener('click', () => {
                this.applyFontSettings();
            });
        }

        // زر إعادة التعيين
        const resetBtn = document.getElementById('reset-font-settings');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => {
                this.resetFontSettings();
            });
        }
    }    updateFontPreview() {
        const mainFontFamily = document.getElementById('main-font-family')?.value || 'Calibri, Arial, sans-serif';
        const mainFontSize = document.getElementById('main-font-size')?.value || '16px';
        const mainFontStyle = document.getElementById('main-font-style')?.value || 'normal';
        const mainFontWeight = document.getElementById('main-font-weight')?.value || '400';
        
        const headerFontFamily = document.getElementById('header-font-family')?.value || 'Calibri, Arial, sans-serif';
        const headerFontSize = document.getElementById('header-font-size')?.value || '2em';
        const headerFontStyle = document.getElementById('header-font-style')?.value || 'normal';
        const headerFontWeight = document.getElementById('header-font-weight')?.value || '700';
        
        const subheaderFontFamily = document.getElementById('subheader-font-family')?.value || 'Calibri, Arial, sans-serif';
        const subheaderFontSize = document.getElementById('subheader-font-size')?.value || '1.4em';
        const subheaderFontStyle = document.getElementById('subheader-font-style')?.value || 'normal';
        const subheaderFontWeight = document.getElementById('subheader-font-weight')?.value || '600';

        // تحديث المعاينة
        const previewHeader = document.getElementById('preview-header');
        const previewSubheader = document.getElementById('preview-subheader');
        const previewText = document.getElementById('preview-text');

        if (previewHeader) {
            previewHeader.style.fontFamily = headerFontFamily;
            previewHeader.style.fontSize = headerFontSize;
            previewHeader.style.fontStyle = headerFontStyle;
            previewHeader.style.fontWeight = headerFontWeight;
        }

        if (previewSubheader) {
            previewSubheader.style.fontFamily = subheaderFontFamily;
            previewSubheader.style.fontSize = subheaderFontSize;
            previewSubheader.style.fontStyle = subheaderFontStyle;
            previewSubheader.style.fontWeight = subheaderFontWeight;
        }

        if (previewText) {
            previewText.style.fontFamily = mainFontFamily;
            previewText.style.fontSize = mainFontSize;
            previewText.style.fontStyle = mainFontStyle;
            previewText.style.fontWeight = mainFontWeight;
        }
    }    applyFontSettings() {
        try {
            const settings = {
                mainFontFamily: document.getElementById('main-font-family')?.value || 'Calibri, Arial, sans-serif',
                mainFontSize: document.getElementById('main-font-size')?.value || '16px',
                mainFontStyle: document.getElementById('main-font-style')?.value || 'normal',
                mainFontWeight: document.getElementById('main-font-weight')?.value || '400',
                
                headerFontFamily: document.getElementById('header-font-family')?.value || 'Calibri, Arial, sans-serif',
                headerFontSize: document.getElementById('header-font-size')?.value || '2em',
                headerFontStyle: document.getElementById('header-font-style')?.value || 'normal',
                headerFontWeight: document.getElementById('header-font-weight')?.value || '700',
                
                subheaderFontFamily: document.getElementById('subheader-font-family')?.value || 'Calibri, Arial, sans-serif',
                subheaderFontSize: document.getElementById('subheader-font-size')?.value || '1.4em',
                subheaderFontStyle: document.getElementById('subheader-font-style')?.value || 'normal',
                subheaderFontWeight: document.getElementById('subheader-font-weight')?.value || '600',
                
                tableFontSize: document.getElementById('table-font-size')?.value || '14px',
                tableFontWeight: document.getElementById('table-font-weight')?.value || '400'
            };

            // تطبيق الإعدادات على الصفحة
            document.documentElement.style.setProperty('--main-font-family', settings.mainFontFamily);
            document.documentElement.style.setProperty('--main-font-size', settings.mainFontSize);
            document.documentElement.style.setProperty('--main-font-style', settings.mainFontStyle);
            document.documentElement.style.setProperty('--main-font-weight', settings.mainFontWeight);
            
            document.documentElement.style.setProperty('--header-font-family', settings.headerFontFamily);
            document.documentElement.style.setProperty('--header-font-size', settings.headerFontSize);
            document.documentElement.style.setProperty('--header-font-style', settings.headerFontStyle);
            document.documentElement.style.setProperty('--header-font-weight', settings.headerFontWeight);
            
            document.documentElement.style.setProperty('--subheader-font-family', settings.subheaderFontFamily);
            document.documentElement.style.setProperty('--subheader-font-size', settings.subheaderFontSize);
            document.documentElement.style.setProperty('--subheader-font-style', settings.subheaderFontStyle);
            document.documentElement.style.setProperty('--subheader-font-weight', settings.subheaderFontWeight);
            
            document.documentElement.style.setProperty('--table-font-size', settings.tableFontSize);
            document.documentElement.style.setProperty('--table-font-weight', settings.tableFontWeight);

            // إضافة فئة CSS للتفعيل
            document.body.classList.add('custom-fonts');

            // حفظ الإعدادات
            localStorage.setItem('fontSettings', JSON.stringify(settings));

            this.showAlert('نجح', 'تم تطبيق إعدادات الخطوط بنجاح!', 'success');

        } catch (error) {
            console.error('خطأ في تطبيق إعدادات الخطوط:', error);
            this.showAlert('خطأ', 'خطأ في تطبيق إعدادات الخطوط', 'error');
        }
    }    loadFontSettings() {
        try {
            const savedSettings = localStorage.getItem('fontSettings');
            if (savedSettings) {
                const settings = JSON.parse(savedSettings);
                
                // تطبيق الإعدادات المحفوظة على العناصر
                if (document.getElementById('main-font-family')) document.getElementById('main-font-family').value = settings.mainFontFamily || 'Calibri, Arial, sans-serif';
                if (document.getElementById('main-font-size')) document.getElementById('main-font-size').value = settings.mainFontSize || '16px';
                if (document.getElementById('main-font-style')) document.getElementById('main-font-style').value = settings.mainFontStyle || 'normal';
                if (document.getElementById('main-font-weight')) document.getElementById('main-font-weight').value = settings.mainFontWeight || '400';
                
                if (document.getElementById('header-font-family')) document.getElementById('header-font-family').value = settings.headerFontFamily || 'Calibri, Arial, sans-serif';
                if (document.getElementById('header-font-size')) document.getElementById('header-font-size').value = settings.headerFontSize || '2em';
                if (document.getElementById('header-font-style')) document.getElementById('header-font-style').value = settings.headerFontStyle || 'normal';
                if (document.getElementById('header-font-weight')) document.getElementById('header-font-weight').value = settings.headerFontWeight || '700';
                
                if (document.getElementById('subheader-font-family')) document.getElementById('subheader-font-family').value = settings.subheaderFontFamily || 'Calibri, Arial, sans-serif';
                if (document.getElementById('subheader-font-size')) document.getElementById('subheader-font-size').value = settings.subheaderFontSize || '1.4em';
                if (document.getElementById('subheader-font-style')) document.getElementById('subheader-font-style').value = settings.subheaderFontStyle || 'normal';
                if (document.getElementById('subheader-font-weight')) document.getElementById('subheader-font-weight').value = settings.subheaderFontWeight || '600';
                
                if (document.getElementById('table-font-size')) document.getElementById('table-font-size').value = settings.tableFontSize || '14px';
                if (document.getElementById('table-font-weight')) document.getElementById('table-font-weight').value = settings.tableFontWeight || '400';

                // تطبيق الإعدادات على الصفحة
                this.applyFontSettings();
            }
        } catch (error) {
            console.error('خطأ في تحميل إعدادات الخطوط:', error);
        }
    }    resetFontSettings() {
        // إعادة تعيين القيم الافتراضية
        if (document.getElementById('main-font-family')) document.getElementById('main-font-family').value = 'Calibri, Arial, sans-serif';
        if (document.getElementById('main-font-size')) document.getElementById('main-font-size').value = '16px';
        if (document.getElementById('main-font-style')) document.getElementById('main-font-style').value = 'normal';
        if (document.getElementById('main-font-weight')) document.getElementById('main-font-weight').value = '400';
        
        if (document.getElementById('header-font-family')) document.getElementById('header-font-family').value = 'Calibri, Arial, sans-serif';
        if (document.getElementById('header-font-size')) document.getElementById('header-font-size').value = '2em';
        if (document.getElementById('header-font-style')) document.getElementById('header-font-style').value = 'normal';
        if (document.getElementById('header-font-weight')) document.getElementById('header-font-weight').value = '700';
        
        if (document.getElementById('subheader-font-family')) document.getElementById('subheader-font-family').value = 'Calibri, Arial, sans-serif';
        if (document.getElementById('subheader-font-size')) document.getElementById('subheader-font-size').value = '1.4em';
        if (document.getElementById('subheader-font-style')) document.getElementById('subheader-font-style').value = 'normal';
        if (document.getElementById('subheader-font-weight')) document.getElementById('subheader-font-weight').value = '600';
        
        if (document.getElementById('table-font-size')) document.getElementById('table-font-size').value = '14px';
        if (document.getElementById('table-font-weight')) document.getElementById('table-font-weight').value = '400';

        // حذف الإعدادات المحفوظة
        localStorage.removeItem('fontSettings');

        // إزالة فئة CSS المخصصة
        document.body.classList.remove('custom-fonts');

        // إعادة تعيين متغيرات CSS
        const cssVariables = [
            '--main-font-family', '--main-font-size', '--main-font-style', '--main-font-weight',
            '--header-font-family', '--header-font-size', '--header-font-style', '--header-font-weight',
            '--subheader-font-family', '--subheader-font-size', '--subheader-font-style', '--subheader-font-weight',
            '--table-font-size', '--table-font-weight'
        ];
        
        cssVariables.forEach(variable => {
            document.documentElement.style.removeProperty(variable);
        });

        // تحديث المعاينة
        this.updateFontPreview();

        this.showAlert('نجح', 'تم إعادة تعيين إعدادات الخطوط إلى الافتراضية', 'success');
    }
}

// تشغيل النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.studentSystem = new StudentManagementSystem();
});