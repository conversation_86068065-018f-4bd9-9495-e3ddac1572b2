<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات الخط والألوان</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Calibri', 'Tahoma', 'Arial', sans-serif;
            font-size: 13px;
            font-weight: bold;
            background-color: #B0E0E6;
            min-height: 100vh;
            padding: 15px;
            direction: rtl;
        }

        .content-frame {
            background-color: white;
            border-radius: 8px;
            box-shadow: 5px 5px 15px rgba(0, 0, 0, 0.15);
            width: calc(100% - 30px);
            height: calc(100vh - 30px);
            margin: 0 auto;
            padding: 10px;
            overflow-y: auto;
            overflow-x: hidden;
            direction: ltr;
        }

        .content-frame > * {
            direction: rtl;
        }

        /* تنسيق شريط التمرير */
        .content-frame::-webkit-scrollbar {
            width: 14px;
        }

        .content-frame::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 7px;
            margin: 10px 0;
        }

        .content-frame::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #1976d2, #1565c0);
            border-radius: 7px;
            border: 2px solid #f1f1f1;
        }

        .content-frame::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #1565c0, #0d47a1);
        }

        .header {
            background: linear-gradient(135deg, #1976d2, #1565c0);
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
            margin: -10px -10px 20px -10px;
        }

        .header h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }

        .back-button {
            position: absolute;
            top: 25px;
            right: 25px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            padding: 8px 15px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .back-button:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .settings-container {
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .font-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }

        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #1976d2;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .font-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .control-group {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #ddd;
        }

        .control-label {
            font-size: 14px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
            display: block;
        }

        .control-input {
            width: 100%;
            padding: 8px 12px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 13px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .control-input:focus {
            outline: none;
            border-color: #1976d2;
            box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
        }

        select.control-input {
            cursor: pointer;
        }

        .font-preview {
            margin-top: 10px;
            padding: 10px;
            background: #f0f8ff;
            border-radius: 4px;
            border: 1px solid #1976d2;
            text-align: center;
        }

        .preview-text {
            color: #333;
            margin: 5px 0;
        }

        .color-section {
            background: #fff3e0;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #ffcc02;
        }

        .color-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .color-control {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #ddd;
        }

        .color-input {
            width: 50px;
            height: 40px;
            border: 2px solid #ddd;
            border-radius: 6px;
            cursor: pointer;
            margin-left: 10px;
        }

        .color-preview {
            width: 100%;
            height: 30px;
            border-radius: 4px;
            margin-top: 8px;
            border: 1px solid #ddd;
        }

        .actions-section {
            text-align: center;
            padding: 20px;
        }

        .action-button {
            background: linear-gradient(135deg, #1976d2, #1565c0);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            min-width: 120px;
        }

        .action-button:hover {
            background: linear-gradient(135deg, #1565c0, #0d47a1);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
        }

        .action-button.secondary {
            background: linear-gradient(135deg, #757575, #616161);
        }

        .action-button.secondary:hover {
            background: linear-gradient(135deg, #616161, #424242);
        }

        .action-button.success {
            background: linear-gradient(135deg, #388e3c, #2e7d32);
        }

        .action-button.success:hover {
            background: linear-gradient(135deg, #2e7d32, #1b5e20);
        }

        .message {
            padding: 12px;
            border-radius: 6px;
            margin: 10px 0;
            font-weight: bold;
            text-align: center;
            display: none;
        }

        .message.success {
            background: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }

        .message.error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #f44336;
        }

        .font-families {
            display: none;
        }
    </style>
</head>
<body>
    <div class="content-frame">
        <div class="header">
            <button class="back-button" onclick="goBack()">← العودة للرئيسية</button>
            <h1>🎨 إعدادات الخط والألوان</h1>
            <p>تخصيص خطوط وألوان البرنامج لتناسب احتياجاتك</p>
        </div>

        <div class="settings-container">
            <!-- رسائل النظام -->
            <div id="successMessage" class="message success"></div>
            <div id="errorMessage" class="message error"></div>

            <!-- إعدادات الخطوط -->
            <div class="font-section">
                <div class="section-title">
                    <span>🔤</span>
                    <span>إعدادات الخطوط</span>
                </div>

                <div class="font-controls">
                    <!-- خط العناوين الرئيسية -->
                    <div class="control-group">
                        <label class="control-label">خط العناوين الرئيسية</label>
                        
                        <label class="control-label">نوع الخط:</label>
                        <select class="control-input" id="mainTitleFamily" onchange="updatePreview('mainTitle')">
                            <option value="'Calibri', 'Tahoma', 'Arial', sans-serif">Calibri</option>
                            <option value="'Tahoma', 'Arial', sans-serif">Tahoma</option>
                            <option value="'Arial', sans-serif">Arial</option>
                            <option value="'Times New Roman', serif">Times New Roman</option>
                            <option value="'Traditional Arabic', serif">Traditional Arabic</option>
                            <option value="'Cairo', 'Tahoma', sans-serif">Cairo</option>
                        </select>

                        <label class="control-label">حجم الخط:</label>
                        <select class="control-input" id="mainTitleSize" onchange="updatePreview('mainTitle')">
                            <option value="16px">16px</option>
                            <option value="18px">18px</option>
                            <option value="20px">20px</option>
                            <option value="22px">22px</option>
                            <option value="24px" selected>24px</option>
                            <option value="26px">26px</option>
                            <option value="28px">28px</option>
                            <option value="30px">30px</option>
                        </select>

                        <label class="control-label">نمط الخط:</label>
                        <select class="control-input" id="mainTitleWeight" onchange="updatePreview('mainTitle')">
                            <option value="normal">عادي</option>
                            <option value="bold" selected>غليظ</option>
                            <option value="bolder">غليظ جداً</option>
                        </select>

                        <div class="font-preview">
                            <div class="preview-text" id="mainTitlePreview">عنوان رئيسي تجريبي</div>
                        </div>
                    </div>

                    <!-- خط العناوين الفرعية -->
                    <div class="control-group">
                        <label class="control-label">خط العناوين الفرعية</label>
                        
                        <label class="control-label">نوع الخط:</label>
                        <select class="control-input" id="subTitleFamily" onchange="updatePreview('subTitle')">
                            <option value="'Calibri', 'Tahoma', 'Arial', sans-serif" selected>Calibri</option>
                            <option value="'Tahoma', 'Arial', sans-serif">Tahoma</option>
                            <option value="'Arial', sans-serif">Arial</option>
                            <option value="'Times New Roman', serif">Times New Roman</option>
                            <option value="'Traditional Arabic', serif">Traditional Arabic</option>
                            <option value="'Cairo', 'Tahoma', sans-serif">Cairo</option>
                        </select>

                        <label class="control-label">حجم الخط:</label>
                        <select class="control-input" id="subTitleSize" onchange="updatePreview('subTitle')">
                            <option value="12px">12px</option>
                            <option value="14px" selected>14px</option>
                            <option value="16px">16px</option>
                            <option value="18px">18px</option>
                            <option value="20px">20px</option>
                            <option value="22px">22px</option>
                        </select>

                        <label class="control-label">نمط الخط:</label>
                        <select class="control-input" id="subTitleWeight" onchange="updatePreview('subTitle')">
                            <option value="normal">عادي</option>
                            <option value="bold" selected>غليظ</option>
                            <option value="bolder">غليظ جداً</option>
                        </select>

                        <div class="font-preview">
                            <div class="preview-text" id="subTitlePreview">عنوان فرعي تجريبي</div>
                        </div>
                    </div>

                    <!-- خط العناصر الأخرى -->
                    <div class="control-group">
                        <label class="control-label">خط العناصر الأخرى (مربعات النص والقوائم)</label>
                        
                        <label class="control-label">نوع الخط:</label>
                        <select class="control-input" id="elementFamily" onchange="updatePreview('element')">
                            <option value="'Calibri', 'Tahoma', 'Arial', sans-serif" selected>Calibri</option>
                            <option value="'Tahoma', 'Arial', sans-serif">Tahoma</option>
                            <option value="'Arial', sans-serif">Arial</option>
                            <option value="'Times New Roman', serif">Times New Roman</option>
                            <option value="'Traditional Arabic', serif">Traditional Arabic</option>
                            <option value="'Cairo', 'Tahoma', sans-serif">Cairo</option>
                        </select>

                        <label class="control-label">حجم الخط:</label>
                        <select class="control-input" id="elementSize" onchange="updatePreview('element')">
                            <option value="10px">10px</option>
                            <option value="11px">11px</option>
                            <option value="12px">12px</option>
                            <option value="13px" selected>13px</option>
                            <option value="14px">14px</option>
                            <option value="15px">15px</option>
                            <option value="16px">16px</option>
                        </select>

                        <label class="control-label">نمط الخط:</label>
                        <select class="control-input" id="elementWeight" onchange="updatePreview('element')">
                            <option value="normal">عادي</option>
                            <option value="bold" selected>غليظ</option>
                            <option value="bolder">غليظ جداً</option>
                        </select>

                        <div class="font-preview">
                            <div class="preview-text" id="elementPreview">نص العناصر التجريبي</div>
                        </div>
                    </div>

                    <!-- خط الجداول -->
                    <div class="control-group">
                        <label class="control-label">خط الجداول</label>
                        
                        <label class="control-label">نوع الخط:</label>
                        <select class="control-input" id="tableFamily" onchange="updatePreview('table')">
                            <option value="'Calibri', 'Tahoma', 'Arial', sans-serif" selected>Calibri</option>
                            <option value="'Tahoma', 'Arial', sans-serif">Tahoma</option>
                            <option value="'Arial', sans-serif">Arial</option>
                            <option value="'Times New Roman', serif">Times New Roman</option>
                            <option value="'Traditional Arabic', serif">Traditional Arabic</option>
                            <option value="'Cairo', 'Tahoma', sans-serif">Cairo</option>
                        </select>

                        <label class="control-label">حجم الخط:</label>
                        <select class="control-input" id="tableSize" onchange="updatePreview('table')">
                            <option value="10px">10px</option>
                            <option value="11px">11px</option>
                            <option value="12px">12px</option>
                            <option value="13px" selected>13px</option>
                            <option value="14px">14px</option>
                            <option value="15px">15px</option>
                        </select>

                        <label class="control-label">نمط الخط:</label>
                        <select class="control-input" id="tableWeight" onchange="updatePreview('table')">
                            <option value="normal">عادي</option>
                            <option value="bold" selected>غليظ</option>
                            <option value="bolder">غليظ جداً</option>
                        </select>

                        <div class="font-preview">
                            <div class="preview-text" id="tablePreview">نص الجداول التجريبي</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إعدادات الألوان -->
            <div class="color-section">
                <div class="section-title">
                    <span>🎨</span>
                    <span>إعدادات الألوان</span>
                </div>

                <div class="color-grid">
                    <!-- لون الخلفية الرئيسية -->
                    <div class="color-control">
                        <label class="control-label">لون الخلفية الرئيسية</label>
                        <input type="color" class="color-input" id="primaryBgColor" value="#B0E0E6" onchange="updateColorPreview('primaryBg')">
                        <div class="color-preview" id="primaryBgPreview"></div>
                    </div>

                    <!-- لون خلفية البطاقات -->
                    <div class="color-control">
                        <label class="control-label">لون خلفية البطاقات</label>
                        <input type="color" class="color-input" id="cardBgColor" value="#FFFFFF" onchange="updateColorPreview('cardBg')">
                        <div class="color-preview" id="cardBgPreview"></div>
                    </div>

                    <!-- اللون الأساسي -->
                    <div class="color-control">
                        <label class="control-label">اللون الأساسي (الأزرار والروابط)</label>
                        <input type="color" class="color-input" id="primaryColor" value="#1976d2" onchange="updateColorPreview('primary')">
                        <div class="color-preview" id="primaryPreview"></div>
                    </div>

                    <!-- لون النص الرئيسي -->
                    <div class="color-control">
                        <label class="control-label">لون النص الرئيسي</label>
                        <input type="color" class="color-input" id="textColor" value="#333333" onchange="updateColorPreview('text')">
                        <div class="color-preview" id="textPreview"></div>
                    </div>

                    <!-- لون الحدود -->
                    <div class="color-control">
                        <label class="control-label">لون الحدود</label>
                        <input type="color" class="color-input" id="borderColor" value="#DDDDDD" onchange="updateColorPreview('border')">
                        <div class="color-preview" id="borderPreview"></div>
                    </div>

                    <!-- لون رؤوس الجداول -->
                    <div class="color-control">
                        <label class="control-label">لون رؤوس الجداول</label>
                        <input type="color" class="color-input" id="tableHeaderColor" value="#1976d2" onchange="updateColorPreview('tableHeader')">
                        <div class="color-preview" id="tableHeaderPreview"></div>
                    </div>
                </div>
            </div>

            <!-- أزرار العمليات -->
            <div class="actions-section">
                <button class="action-button" onclick="saveSettings()">💾 حفظ الإعدادات</button>
                <button class="action-button secondary" onclick="resetToDefaults()">🔄 الإعدادات الافتراضية</button>
                <button class="action-button success" onclick="applyChanges()">✅ تطبيق التغييرات</button>
            </div>
        </div>
    </div>

    <script>
        const BASE_URL = 'http://localhost:5000';

        // العودة للنافذة الرئيسية
        function goBack() {
            window.location.href = 'main_window0.html';
        }

        // تحديث معاينة الخط
        function updatePreview(type) {
            const familySelect = document.getElementById(type + 'Family');
            const sizeSelect = document.getElementById(type + 'Size');
            const weightSelect = document.getElementById(type + 'Weight');
            const preview = document.getElementById(type + 'Preview');

            if (familySelect && sizeSelect && weightSelect && preview) {
                preview.style.fontFamily = familySelect.value;
                preview.style.fontSize = sizeSelect.value;
                preview.style.fontWeight = weightSelect.value;
            }
        }

        // تحديث معاينة الألوان
        function updateColorPreview(type) {
            const colorInput = document.getElementById(type + 'Color');
            const preview = document.getElementById(type + 'Preview');

            if (colorInput && preview) {
                preview.style.backgroundColor = colorInput.value;
            }
        }

        // تهيئة المعاينات
        function initializePreviews() {
            // تهيئة معاينات الخطوط
            updatePreview('mainTitle');
            updatePreview('subTitle');
            updatePreview('element');
            updatePreview('table');

            // تهيئة معاينات الألوان
            updateColorPreview('primaryBg');
            updateColorPreview('cardBg');
            updateColorPreview('primary');
            updateColorPreview('text');
            updateColorPreview('border');
            updateColorPreview('tableHeader');
        }

        // عرض رسالة
        function showMessage(message, type = 'success') {
            const successDiv = document.getElementById('successMessage');
            const errorDiv = document.getElementById('errorMessage');
            
            // إخفاء جميع الرسائل
            successDiv.style.display = 'none';
            errorDiv.style.display = 'none';
            
            if (type === 'success') {
                successDiv.textContent = message;
                successDiv.style.display = 'block';
                setTimeout(() => {
                    successDiv.style.display = 'none';
                }, 3000);
            } else if (type === 'error') {
                errorDiv.textContent = message;
                errorDiv.style.display = 'block';
                setTimeout(() => {
                    errorDiv.style.display = 'none';
                }, 5000);
            }
        }

        // جمع الإعدادات الحالية
        function getCurrentSettings() {
            return {
                fonts: {
                    mainTitle: {
                        family: document.getElementById('mainTitleFamily').value,
                        size: document.getElementById('mainTitleSize').value,
                        weight: document.getElementById('mainTitleWeight').value
                    },
                    subTitle: {
                        family: document.getElementById('subTitleFamily').value,
                        size: document.getElementById('subTitleSize').value,
                        weight: document.getElementById('subTitleWeight').value
                    },
                    element: {
                        family: document.getElementById('elementFamily').value,
                        size: document.getElementById('elementSize').value,
                        weight: document.getElementById('elementWeight').value
                    },
                    table: {
                        family: document.getElementById('tableFamily').value,
                        size: document.getElementById('tableSize').value,
                        weight: document.getElementById('tableWeight').value
                    }
                },
                colors: {
                    primaryBg: document.getElementById('primaryBgColor').value,
                    cardBg: document.getElementById('cardBgColor').value,
                    primary: document.getElementById('primaryColor').value,
                    text: document.getElementById('textColor').value,
                    border: document.getElementById('borderColor').value,
                    tableHeader: document.getElementById('tableHeaderColor').value
                }
            };
        }

        // حفظ الإعدادات
        async function saveSettings() {
            try {
                const settings = getCurrentSettings();
                
                // حفظ في التخزين المحلي
                localStorage.setItem('fontColorSettings', JSON.stringify(settings));
                
                // يمكن إضافة API call للخادم هنا لاحقاً
                // const response = await fetch(`${BASE_URL}/api/settings/save`, {
                //     method: 'POST',
                //     headers: { 'Content-Type': 'application/json' },
                //     body: JSON.stringify(settings)
                // });

                showMessage('تم حفظ الإعدادات بنجاح! ستطبق التغييرات عند إعادة تحميل الصفحات.', 'success');
            } catch (error) {
                console.error('خطأ في حفظ الإعدادات:', error);
                showMessage('حدث خطأ أثناء حفظ الإعدادات', 'error');
            }
        }

        // إعادة تعيين الإعدادات الافتراضية
        function resetToDefaults() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟')) {
                // إعدادات الخطوط الافتراضية
                document.getElementById('mainTitleFamily').value = "'Calibri', 'Tahoma', 'Arial', sans-serif";
                document.getElementById('mainTitleSize').value = "24px";
                document.getElementById('mainTitleWeight').value = "bold";

                document.getElementById('subTitleFamily').value = "'Calibri', 'Tahoma', 'Arial', sans-serif";
                document.getElementById('subTitleSize').value = "14px";
                document.getElementById('subTitleWeight').value = "bold";

                document.getElementById('elementFamily').value = "'Calibri', 'Tahoma', 'Arial', sans-serif";
                document.getElementById('elementSize').value = "13px";
                document.getElementById('elementWeight').value = "bold";

                document.getElementById('tableFamily').value = "'Calibri', 'Tahoma', 'Arial', sans-serif";
                document.getElementById('tableSize').value = "13px";
                document.getElementById('tableWeight').value = "bold";

                // إعدادات الألوان الافتراضية
                document.getElementById('primaryBgColor').value = "#B0E0E6";
                document.getElementById('cardBgColor').value = "#FFFFFF";
                document.getElementById('primaryColor').value = "#1976d2";
                document.getElementById('textColor').value = "#333333";
                document.getElementById('borderColor').value = "#DDDDDD";
                document.getElementById('tableHeaderColor').value = "#1976d2";

                // تحديث المعاينات
                initializePreviews();
                
                // حذف الإعدادات المحفوظة
                localStorage.removeItem('fontColorSettings');
                
                showMessage('تم إعادة تعيين الإعدادات إلى القيم الافتراضية', 'success');
            }
        }

        // تطبيق التغييرات فوراً
        function applyChanges() {
            const settings = getCurrentSettings();
            
            // تطبيق إعدادات الخطوط على العناصر الحالية
            const style = document.createElement('style');
            style.id = 'dynamicStyles';
            
            // إزالة الأنماط السابقة إذا وجدت
            const existingStyle = document.getElementById('dynamicStyles');
            if (existingStyle) {
                existingStyle.remove();
            }

            // إنشاء CSS ديناميكي
            style.textContent = `
                /* خط العناوين الرئيسية */
                .header h1, .section-title {
                    font-family: ${settings.fonts.mainTitle.family} !important;
                    font-size: ${settings.fonts.mainTitle.size} !important;
                    font-weight: ${settings.fonts.mainTitle.weight} !important;
                }
                
                /* خط العناوين الفرعية */
                .control-label, .header p {
                    font-family: ${settings.fonts.subTitle.family} !important;
                    font-size: ${settings.fonts.subTitle.size} !important;
                    font-weight: ${settings.fonts.subTitle.weight} !important;
                }
                
                /* خط العناصر الأخرى */
                .control-input, .action-button, .preview-text {
                    font-family: ${settings.fonts.element.family} !important;
                    font-size: ${settings.fonts.element.size} !important;
                    font-weight: ${settings.fonts.element.weight} !important;
                }
                
                /* ألوان الخلفية والعناصر */
                body {
                    background-color: ${settings.colors.primaryBg} !important;
                }
                
                .content-frame, .control-group, .color-control {
                    background-color: ${settings.colors.cardBg} !important;
                }
                
                .action-button, .header {
                    background: linear-gradient(135deg, ${settings.colors.primary}, ${settings.colors.primary}dd) !important;
                }
                
                .control-input, .color-control {
                    border-color: ${settings.colors.border} !important;
                }
                
                .section-title, .control-label {
                    color: ${settings.colors.text} !important;
                }
            `;
            
            document.head.appendChild(style);
            
            showMessage('تم تطبيق التغييرات على هذه الصفحة! احفظ الإعدادات لتطبيقها على باقي الصفحات.', 'success');
        }

        // تحميل الإعدادات المحفوظة
        function loadSavedSettings() {
            try {
                const saved = localStorage.getItem('fontColorSettings');
                if (saved) {
                    const settings = JSON.parse(saved);
                    
                    // تطبيق إعدادات الخطوط
                    if (settings.fonts) {
                        // خط العناوين الرئيسية
                        if (settings.fonts.mainTitle) {
                            document.getElementById('mainTitleFamily').value = settings.fonts.mainTitle.family;
                            document.getElementById('mainTitleSize').value = settings.fonts.mainTitle.size;
                            document.getElementById('mainTitleWeight').value = settings.fonts.mainTitle.weight;
                        }
                        
                        // خط العناوين الفرعية
                        if (settings.fonts.subTitle) {
                            document.getElementById('subTitleFamily').value = settings.fonts.subTitle.family;
                            document.getElementById('subTitleSize').value = settings.fonts.subTitle.size;
                            document.getElementById('subTitleWeight').value = settings.fonts.subTitle.weight;
                        }
                        
                        // خط العناصر
                        if (settings.fonts.element) {
                            document.getElementById('elementFamily').value = settings.fonts.element.family;
                            document.getElementById('elementSize').value = settings.fonts.element.size;
                            document.getElementById('elementWeight').value = settings.fonts.element.weight;
                        }
                        
                        // خط الجداول
                        if (settings.fonts.table) {
                            document.getElementById('tableFamily').value = settings.fonts.table.family;
                            document.getElementById('tableSize').value = settings.fonts.table.size;
                            document.getElementById('tableWeight').value = settings.fonts.table.weight;
                        }
                    }
                    
                    // تطبيق إعدادات الألوان
                    if (settings.colors) {
                        document.getElementById('primaryBgColor').value = settings.colors.primaryBg || "#B0E0E6";
                        document.getElementById('cardBgColor').value = settings.colors.cardBg || "#FFFFFF";
                        document.getElementById('primaryColor').value = settings.colors.primary || "#1976d2";
                        document.getElementById('textColor').value = settings.colors.text || "#333333";
                        document.getElementById('borderColor').value = settings.colors.border || "#DDDDDD";
                        document.getElementById('tableHeaderColor').value = settings.colors.tableHeader || "#1976d2";
                    }
                }
            } catch (error) {
                console.error('خطأ في تحميل الإعدادات المحفوظة:', error);
            }
        }

        // تهيئة الصفحة عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            loadSavedSettings();
            initializePreviews();
            
            // تطبيق الإعدادات المحفوظة فوراً
            const saved = localStorage.getItem('fontColorSettings');
            if (saved) {
                applyChanges();
            }
        });
    </script>
</body>
</html>
