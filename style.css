* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #f8f9fc, #e9ecef);
    direction: rtl;
    text-align: right;
    min-height: 100vh;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

h1 {
    text-align: center;
    color: #2c3e50;
    margin-bottom: 30px;
    font-size: 2.5em;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    background: linear-gradient(135deg, #3498db, #2980b9);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* منطقة الأزرار */
.buttons-section {
    background: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 25px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    border: 1px solid rgba(255,255,255,0.2);
}

.action-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    justify-content: center;
}

.action-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 10px;
    font-family: 'Calibri', sans-serif;
    font-size: 14px;
    font-weight: bold;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    position: relative;
    overflow: hidden;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.3);
}

.action-btn:active {
    transform: translateY(0);
}

.registration-btn {
    background: linear-gradient(135deg, #27ae60, #229954);
}

.monthly-btn {
    background: linear-gradient(135deg, #e67e22, #d35400);
}

.reports-btn {
    background: linear-gradient(135deg, #6f42c1, #5a32a3);
}

.delete-btn {
    background: linear-gradient(135deg, #dc3545, #c82333);
}

.query-btn {
    background: linear-gradient(135deg, #17a2b8, #138496);
}

.cancel-btn {
    background: linear-gradient(135deg, #fd7e14, #e8680b);
}

.restore-btn {
    background: linear-gradient(135deg, #28a745, #1e7e34);
}

.refresh-btn {
    background: linear-gradient(135deg, #007bff, #0056b3);
}

.delete-record-btn {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
}

/* منطقة التصفية */
.filter-section {
    background: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 25px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    border: 1px solid rgba(255,255,255,0.2);
}

.filter-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    align-items: center;
    margin-bottom: 15px;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.filter-group label {
    font-weight: bold;
    color: #495057;
    font-size: 14px;
}

.filter-group input,
.filter-group select {
    padding: 10px 12px;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    font-size: 14px;
    font-family: 'Calibri', sans-serif;
    transition: all 0.3s ease;
    min-width: 150px;
}

.filter-group input:focus,
.filter-group select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0,123,255,0.25);
}

#search-input {
    min-width: 250px;
}

#group-filter {
    border-color: #ff5722;
}

#group-filter:focus {
    border-color: #e64a19;
    box-shadow: 0 0 0 3px rgba(255,87,34,0.25);
}

.statistics {
    text-align: center;
}

.counter-label {
    background: linear-gradient(135deg, #e7f3ff, #cce7ff);
    color: #007bff;
    padding: 12px 20px;
    border-radius: 10px;
    font-weight: bold;
    font-size: 16px;
    border: 2px solid #007bff;
    display: inline-block;
}

/* منطقة الجدول */
.table-section {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    border: 1px solid rgba(255,255,255,0.2);
}

.table-container {
    overflow-x: auto;
    border-radius: 10px;
    border: 1px solid #dee2e6;
}

#students-table {
    width: 100%;
    border-collapse: collapse;
    font-family: 'Calibri', sans-serif;
}

#students-table thead {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    position: sticky;
    top: 0;
    z-index: 10;
}

#students-table th {
    padding: 15px 12px;
    text-align: center;
    font-weight: bold;
    color: #0d47a1;
    font-family: 'Calibri', Arial, sans-serif;
    font-size: 15px;
    border: 1px solid #90caf9;
    position: relative;
}

#students-table th:hover {
    background: linear-gradient(135deg, #bbdefb, #90caf9);
    color: #1565c0;
}

.checkbox-col {
    width: 80px;
    min-width: 80px;
}

#students-table tbody tr:nth-child(even) {
    background-color: #f8f9fa;
}

#students-table tbody tr:hover {
    background-color: #e3f2fd;
    /* تم إزالة التموج والتكبير - لا تأثيرات انتقالية */
}

#students-table td {
    padding: 12px;
    text-align: center;
    border: 1px solid #dee2e6;
    font-family:  Arial, sans-serif;
    font-size: 15px;
    font-weight: bold;
    color: #000000;
}

/* تنسيق مربعات الاختيار */
.checkbox-container {
    display: flex;
    justify-content: center;
    align-items: center;
}

.student-checkbox {
    width: 28px;
    height: 28px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.student-checkbox:hover {
    transform: scale(1.1);
}

/* الصف المحدد */
.selected-row {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7) !important;
    color: #856404 !important;
    font-weight: bold;
}

.selected-row td {
    color: #856404 !important;
    border-color: #ffc107 !important;
}

/* حالة الدفع */
.payment-status {
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: bold;
    font-size: 12px;
    text-align: center;
}

.payment-paid {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.payment-unpaid {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.payment-partial {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

/* رسائل التنبيه */
#alert-container {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1000;
    max-width: 400px;
}

.alert {
    padding: 15px 20px;
    margin-bottom: 10px;
    border-radius: 10px;
    color: white;
    font-weight: bold;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    animation: slideIn 0.5s ease-out;
    position: relative;
    overflow: hidden;
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: rgba(255,255,255,0.3);
}

.alert-success {
    background: linear-gradient(135deg, #28a745, #1e7e34);
}

.alert-error {
    background: linear-gradient(135deg, #dc3545, #c82333);
}

.alert-warning {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    color: #212529;
}

.alert-info {
    background: linear-gradient(135deg, #17a2b8, #138496);
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* تنسيق التبويبات */
.tabs-container {
    background: white;
    border-radius: 15px;
    margin-bottom: 25px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    overflow: hidden;
}

.tabs-nav {
    display: flex;
    background: linear-gradient(135deg, #2c3e50, #34495e);
    border-bottom: 3px solid #3498db;
}

.tab-btn {
    flex: 1;
    padding: 15px 20px;
    border: none;
    background: transparent;
    color: #ecf0f1;
    font-family: 'Calibri', sans-serif;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    text-align: center;
}

.tab-btn:hover {
    background: rgba(52, 152, 219, 0.2);
    color: #3498db;
}

.tab-btn.active {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    box-shadow: inset 0 -3px 0 #2c3e50;
}

.tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-bottom: 10px solid white;
}

.tab-content {
    display: none;
    padding: 30px;
    animation: fadeIn 0.5s ease-in;
}

.tab-content.active {
    display: block;
}

/* تنسيق تبويب التقارير */
.reports-section h2 {
    color: #2c3e50;
    margin-bottom: 25px;
    text-align: center;
    font-size: 2em;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

.reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.report-card {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 12px;
    padding: 25px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.report-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 25px rgba(0,0,0,0.15);
}

.report-card h3 {
    color: #495057;
    margin-bottom: 15px;
    font-size: 1.3em;
}

.report-card p {
    color: #6c757d;
    margin-bottom: 20px;
    line-height: 1.6;
}

/* تنسيق تبويب الإدارة المالية */
.financial-section h2 {
    color: #2c3e50;
    margin-bottom: 25px;
    text-align: center;
    font-size: 2em;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

.financial-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 25px;
    margin-top: 20px;
}

.financial-card {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.financial-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.financial-card h3 {
    color: #495057;
    margin-bottom: 20px;
    font-size: 1.4em;
    text-align: center;
}

.financial-stats {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.stat-label {
    font-weight: bold;
    color: #495057;
}

.stat-value {
    font-weight: bold;
    font-size: 1.2em;
    color: #28a745;
}

.stat-value.outstanding {
    color: #dc3545;
}

.payment-actions {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 15px;
}

/* تنسيق تبويب الإعدادات */
.settings-section h2 {
    color: #2c3e50;
    margin-bottom: 25px;
    text-align: center;
    font-size: 2em;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 25px;
    margin-top: 20px;
}

.settings-card {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.settings-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.settings-card h3 {
    color: #495057;
    margin-bottom: 20px;
    font-size: 1.4em;
    text-align: center;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.setting-item label {
    font-weight: bold;
    color: #495057;
}

.setting-item select {
    padding: 8px 12px;
    border: 2px solid #dee2e6;
    border-radius: 6px;
    font-family: 'Calibri', sans-serif;
    background: white;
    min-width: 120px;
}

.setting-item select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0,123,255,0.25);
}

.system-actions {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 15px;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .action-buttons {
        justify-content: center;
    }
    
    .action-btn {
        font-size: 12px;
        padding: 10px 15px;
    }
    
    .filter-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-group {
        width: 100%;
    }
    
    .filter-group input,
    .filter-group select {
        min-width: 100%;
    }
    
    .table-container {
        font-size: 12px;
    }
    
    #students-table th,
    #students-table td {
        padding: 8px 6px;
        font-size: 12px;
    }
    
    h1 {
        font-size: 2em;
    }
    
    .tabs-nav {
        flex-direction: column;
    }
    
    .tab-btn {
        text-align: center;
        padding: 12px 15px;
        font-size: 14px;
    }
    
    .tab-content {
        padding: 20px 15px;
    }
    
    .reports-grid,
    .financial-grid,
    .settings-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .report-card,
    .financial-card,
    .settings-card {
        padding: 20px;
    }
    
    .financial-stats {
        gap: 10px;
    }
    
    .stat-item {
        flex-direction: column;
        text-align: center;
        gap: 8px;
    }
    
    .payment-actions,
    .system-actions {
        gap: 10px;
    }
}

/* تأثيرات إضافية */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.teachers-section h2 {
    color: #34495e;
    margin-bottom: 15px;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
}

.teachers-pool {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    min-height: 120px;
    border: 2px dashed #bdc3c7;
    border-radius: 8px;
    padding: 15px;
    background-color: #ecf0f1;
}

.teacher-card {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: grab;
    user-select: none;
    font-weight: bold;
    font-size: 0.9em;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
    position: relative;
}

.teacher-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

.teacher-card:active {
    cursor: grabbing;
}

.teacher-card.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

.teacher-card .card-number {
    position: absolute;
    top: -5px;
    left: -5px;
    background: #e74c3c;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7em;
    font-weight: bold;
}

/* منطقة الجدول الزمني */
.schedule-section {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.schedule-section h2 {
    color: #34495e;
    margin-bottom: 20px;
    border-bottom: 3px solid #e74c3c;
    padding-bottom: 10px;
}

.schedule-grid {
    display: flex;
    gap: 20px;
    overflow-x: auto;
    padding-bottom: 20px;
}

.day-column {
    min-width: 400px;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    border: 2px solid #dee2e6;
}

.day-column h3 {
    text-align: center;
    color: #495057;
    margin-bottom: 15px;
    background: #6c757d;
    color: white;
    padding: 10px;
    border-radius: 6px;
}

.period {
    margin-bottom: 20px;
    background: white;
    border-radius: 6px;
    padding: 15px;
    border: 1px solid #dee2e6;
}

.period h4 {
    color: #6c757d;
    margin-bottom: 15px;
    text-align: center;
    background: #e9ecef;
    padding: 8px;
    border-radius: 4px;
}

.halls-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
}

.hall {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 10px;
    text-align: center;
}

.hall-title {
    font-weight: bold;
    color: #495057;
    margin-bottom: 8px;
    font-size: 0.9em;
}

.seats {
    display: flex;
    gap: 5px;
    justify-content: center;
}

.seat {
    width: 60px;
    height: 40px;
    border: 2px dashed #adb5bd;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #ffffff;
    transition: all 0.3s ease;
    position: relative;
    font-size: 0.8em;
}

.seat:hover {
    border-color: #007bff;
    background: #e7f3ff;
}

.seat.occupied {
    border: 2px solid #28a745;
    background: #d4edda;
}

.seat.drag-over {
    border-color: #ffc107;
    background: #fff3cd;
    transform: scale(1.05);
}

.seat.invalid-drop {
    border-color: #dc3545;
    background: #f8d7da;
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* رسائل التنبيه */
.alert {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 6px;
    color: white;
    font-weight: bold;
    z-index: 1000;
    animation: slideIn 0.3s ease-out;
}

.alert.success {
    background: #28a745;
}

.alert.error {
    background: #dc3545;
}

.alert.warning {
    background: #ffc107;
    color: #212529;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .schedule-grid {
        flex-direction: column;
    }
    
    .day-column {
        min-width: auto;
    }
    
    .halls-container {
        grid-template-columns: 1fr;
    }
    
    .teachers-pool {
        justify-content: center;
    }
}

/* تأثيرات إضافية */
.seat.drop-success {
    animation: dropSuccess 0.6s ease-out;
}

@keyframes dropSuccess {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); background: #d4edda; }
    100% { transform: scale(1); }
}

.teacher-card.used {
    opacity: 0.6;
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
}

.teacher-card.used:hover {
    transform: none;
    cursor: not-allowed;
}

/* تنسيق تبويب بيانات المؤسسة */
.institution-section {
    padding: 20px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.institution-section h2 {
    color: #2c3e50;
    margin-bottom: 30px;
    text-align: center;
    font-size: 2em;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

/* منطقة الشعار */
.logo-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 12px;
    border: 2px solid #dee2e6;
}

.logo-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.logo-display {
    width: 400px;
    height: 150px;
    border: 2px solid #1976d2;
    border-radius: 8px;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    position: relative;
}

.logo-display img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.logo-display #logo-placeholder {
    color: #6c757d;
    font-size: 16px;
    font-weight: bold;
}

.logo-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    justify-content: center;
}

.institution-code {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    margin-top: 15px;
}

.institution-code label {
    font-weight: bold;
    color: #495057;
    font-size: 14px;
}

.code-display {
    background: #f8f9fa;
    color: #2c3e50;
    border: 1px solid #bdc3c7;
    border-radius: 5px;
    padding: 8px 15px;
    font-family: 'Courier New', monospace;
    font-weight: bold;
    font-size: 14px;
    min-width: 200px;
    text-align: center;
}

/* منطقة النموذج */
.institution-form {
    margin-bottom: 30px;
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.form-group label {
    font-weight: bold;
    color: #495057;
    font-size: 18px;
    margin-bottom: 5px;
}

.form-input {
    padding: 12px 15px;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    font-family: 'Calibri', sans-serif;
    font-size: 17px;
    transition: all 0.3s ease;
    background: white;
}

.form-input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0,123,255,0.25);
}

.form-input::placeholder {
    color: #6c757d;
}

/* أزرار العمليات */
.institution-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    border: 1px solid #dee2e6;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .logo-display {
        width: 300px;
        height: 120px;
    }
    
    .form-row {
        flex-direction: column;
        gap: 15px;
    }
    
    .logo-buttons {
        flex-direction: column;
        width: 100%;
    }
    
    .institution-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .institution-actions .action-btn {
        width: 100%;
        max-width: 250px;
    }
}

/* حالات خاصة للحقول */
.form-input.required {
    border-color: #dc3545;
}

.form-input.valid {
    border-color: #28a745;
}

.form-input.error {
    border-color: #dc3545;
    background-color: #fff5f5;
}

.error-message {
    color: #dc3545;
    font-size: 12px;
    margin-top: 5px;
    display: none;
}

.success-message {
    color: #28a745;
    font-size: 12px;
    margin-top: 5px;
    display: none;
}

/* تنسيق خاص لحقل رقم التسجيل */
#registration-number {
    font-family: 'Courier New', monospace;
    letter-spacing: 1px;
}

/* تنسيق قائمة السنوات الدراسية */
#academic-year {
    cursor: pointer;
}

#academic-year option {
    padding: 8px;
    font-family: 'Calibri', sans-serif;
}

/* تنسيقات إعدادات الخطوط */
.font-preview {
    margin-top: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.font-preview h4 {
    color: #495057;
    margin-bottom: 15px;
    font-size: 1.1em;
}

.preview-sample {
    background: white;
    padding: 20px;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.preview-header {
    font-size: 2em;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 10px;
    text-align: center;
}

.preview-subheader {
    font-size: 1.4em;
    font-weight: bold;
    color: #495057;
    margin-bottom: 10px;
    text-align: center;
}

.preview-text {
    font-size: 16px;
    color: #6c757d;
    line-height: 1.5;
    text-align: center;
}

.font-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-top: 15px;
    flex-wrap: wrap;
}

.font-actions .action-btn {
    flex: 1;
    min-width: 140px;
}

/* تحسينات إضافية لإعدادات الخطوط */
.settings-card .setting-item {
    margin-bottom: 12px;
}

.settings-card .setting-item select {
    min-width: 160px;
}

/* فئات CSS للتحكم في الخطوط */
.custom-main-font {
    font-family: var(--main-font-family, 'Calibri, Arial, sans-serif');
    font-size: var(--main-font-size, 16px);
}

.custom-header-font {
    font-family: var(--header-font-family, 'Calibri, Arial, sans-serif');
    font-size: var(--header-font-size, 2em);
}

.custom-subheader-font {
    font-family: var(--subheader-font-family, 'Calibri, Arial, sans-serif');
    font-size: var(--subheader-font-size, 1.4em);
}

.custom-table-font {
    font-size: var(--table-font-size, 14px);
}

/* متغيرات الخطوط الافتراضية */
:root {
    --main-font-family: 'Calibri, Arial, sans-serif';
    --main-font-size: 16px;
    --main-font-style: normal;
    --main-font-weight: 400;
    --header-font-family: 'Calibri, Arial, sans-serif';
    --header-font-size: 2em;
    --header-font-style: normal;
    --header-font-weight: 700;
    --subheader-font-family: 'Calibri, Arial, sans-serif';
    --subheader-font-size: 1.4em;
    --subheader-font-style: normal;
    --subheader-font-weight: 600;
    --table-font-size: 14px;
    --table-font-weight: 400;
}

/* تطبيق الخطوط على العناصر */
body.custom-fonts {
    font-family: var(--main-font-family);
    font-size: var(--main-font-size);
    font-style: var(--main-font-style);
    font-weight: var(--main-font-weight);
}

body.custom-fonts h1 {
    font-family: var(--header-font-family);
    font-size: var(--header-font-size);
    font-style: var(--header-font-style);
    font-weight: var(--header-font-weight);
}

body.custom-fonts h2 {
    font-family: var(--header-font-family);
    font-size: calc(var(--header-font-size) * 0.8);
    font-style: var(--header-font-style);
    font-weight: var(--header-font-weight);
}

body.custom-fonts h3 {
    font-family: var(--subheader-font-family);
    font-size: var(--subheader-font-size);
    font-style: var(--subheader-font-style);
    font-weight: var(--subheader-font-weight);
}

body.custom-fonts #students-table,
body.custom-fonts #students-table th,
body.custom-fonts #students-table td {
    font-size: var(--table-font-size);
    font-weight: var(--table-font-weight);
}

body.custom-fonts .form-input,
body.custom-fonts .action-btn,
body.custom-fonts .filter-group input,
body.custom-fonts .filter-group select {
    font-family: var(--main-font-family);
    font-size: calc(var(--main-font-size) * 0.9);
    font-style: var(--main-font-style);
    font-weight: var(--main-font-weight);
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .font-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .font-actions .action-btn {
        width: 100%;
        max-width: 200px;
    }
    
    .preview-sample {
        padding: 15px;
    }
    
    .preview-header {
        font-size: 1.6em;
    }
    
    .preview-subheader {
        font-size: 1.2em;
    }
    
    .preview-text {
        font-size: 14px;
    }
}

/* تبويب تهيئة البرنامج */
.setup-section {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    border: 1px solid rgba(255,255,255,0.2);
}

.setup-container {
    display: flex;
    gap: 25px;
    min-height: 600px;
}

/* التبويبات العمودية */
.vertical-tabs {
    display: flex;
    width: 100%;
    background: #f8f9fa;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.vertical-tab-nav {
    background: linear-gradient(135deg, #3498db, #2980b9);
    width: 300px;
    padding: 20px 0;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.vertical-tab-btn {
    background: transparent;
    border: none;
    color: rgba(255,255,255,0.8);
    padding: 15px 20px;
    text-align: right;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 500;
    border-right: 3px solid transparent;
}

.vertical-tab-btn:hover {
    background: rgba(255,255,255,0.1);
    color: white;
    border-right-color: rgba(255,255,255,0.3);
}

.vertical-tab-btn.active {
    background: white;
    color: #2980b9;
    border-right-color: #e74c3c;
    font-weight: bold;
}

.vertical-tab-content {
    flex: 1;
    padding: 30px;
    background: white;
    overflow-y: auto;
}

.vertical-tab-panel {
    display: none;
}

.vertical-tab-panel.active {
    display: block;
}

.vertical-tab-panel h3 {
    color: #2c3e50;
    margin-bottom: 25px;
    font-size: 1.8em;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
}

/* بطاقات الاستيراد */
.import-section, .codes-section, .teachers-section, .students-section, .database-section {
    display: grid;
    gap: 20px;
    margin-bottom: 30px;
}

.import-card, .codes-card, .teachers-card, .students-card, .database-card {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 10px;
    padding: 20px;
    transition: all 0.3s ease;
}

.import-card:hover, .codes-card:hover, .teachers-card:hover, .students-card:hover, .database-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.import-card h4, .codes-card h4, .teachers-card h4, .students-card h4, .database-card h4 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 1.3em;
}

.import-card p, .codes-card p, .teachers-card p, .students-card p {
    color: #6c757d;
    margin-bottom: 20px;
    line-height: 1.6;
}

/* خيارات الاستيراد */
.import-options, .codes-options, .teachers-options, .students-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.option-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.option-group label {
    color: #495057;
    font-weight: 600;
    font-size: 14px;
}

.option-group select {
    padding: 10px;
    border: 1px solid #ced4da;
    border-radius: 5px;
    background: white;
    font-size: 14px;
    transition: all 0.3s ease;
}

.option-group select:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* أزرار العمليات */
.import-actions, .update-actions, .codes-actions, .codes-management, .teachers-actions, .students-actions, .database-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* شريط التقدم */
.import-log-section {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
}

.import-log-section h4 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.2em;
}

.progress-container {
    margin-bottom: 20px;
}

.progress-bar {
    background: #e9ecef;
    border-radius: 20px;
    height: 25px;
    position: relative;
    overflow: hidden;
}

.progress-fill {
    background: linear-gradient(90deg, #3498db, #2ecc71);
    height: 100%;
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 20px;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #2c3e50;
    font-weight: bold;
    font-size: 14px;
}

/* منطقة السجل */
.log-container {
    max-height: 200px;
    overflow-y: auto;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 10px;
}

.import-log {
    font-family: 'Courier New', monospace;
    font-size: 13px;
}

.log-entry {
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.log-time {
    color: #6c757d;
    font-weight: bold;
}

.log-message {
    flex: 1;
}

.log-entry.info .log-message {
    color: #3498db;
}

.log-entry.success .log-message {
    color: #27ae60;
}

.log-entry.warning .log-message {
    color: #f39c12;
}

.log-entry.error .log-message {
    color: #e74c3c;
}

/* معلومات قاعدة البيانات */
.database-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.info-item {
    background: white;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.info-label {
    color: #6c757d;
    font-size: 14px;
    font-weight: 600;
}

.info-value {
    color: #2c3e50;
    font-size: 16px;
    font-weight: bold;
}

/* تجاوب التبويبات العمودية */
@media (max-width: 768px) {
    .setup-container {
        flex-direction: column;
    }
    
    .vertical-tabs {
        flex-direction: column;
    }
    
    .vertical-tab-nav {
        width: 100%;
        flex-direction: row;
        overflow-x: auto;
        padding: 10px;
    }
    
    .vertical-tab-btn {
        white-space: nowrap;
        min-width: 200px;
    }
    
    .import-options, .codes-options, .teachers-options, .students-options {
        grid-template-columns: 1fr;
    }
    
    .import-actions, .update-actions, .codes-actions, .codes-management, .teachers-actions, .students-actions, .database-actions {
        flex-direction: column;
    }
}

/* أنماط نظام الاستيراد المدمج */

/* حالة الاتصال */
.connection-status {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 20px;
    text-align: center;
    font-weight: bold;
    color: #856404;
}

.connection-status.connected {
    background-color: #d4edda;
    border-color: #00b894;
    color: #155724;
}

.connection-status.error {
    background-color: #f8d7da;
    border-color: #d63031;
    color: #721c24;
}

/* التحكم في حجم الخط */
.font-controls {
    display: flex;
    align-items: center;
    gap: 10px;
    background-color: #f8f9fa;
    padding: 10px 15px;
    border-radius: 5px;
    margin-bottom: 20px;
    justify-content: center;
}

.font-control-button {
    background-color: #6c757d;
    color: white;
    border: none;
    border-radius: 3px;
    padding: 5px 10px;
    font-size: 12px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s;
    min-width: 40px;
}

.font-control-button:hover {
    background-color: #5a6268;
}

.font-size-display {
    background-color: white;
    border: 1px solid #ced4da;
    border-radius: 3px;
    padding: 5px 10px;
    font-size: 12px;
    font-weight: bold;
    min-width: 50px;
    text-align: center;
}

.font-controls label {
    font-size: 12px;
    font-weight: bold;
    color: #495057;
}

/* أزرار الاستيراد */
.import-buttons-section {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 20px;
}

.import-button {
    background-color: #9b59b6;
    color: white;
    border: none;
    border-radius: 5px;
    padding: 15px 20px;
    font-size: 14px;
    font-weight: bold;
    font-family: 'Calibri', 'Tahoma', 'Arial', sans-serif;
    cursor: pointer;
    transition: background-color 0.3s;
    text-align: center;
    min-height: 50px;
}

.import-button:hover {
    background-color: #8e44ad;
}

.import-button:disabled {
    background-color: #bdc3c7;
    cursor: not-allowed;
}

.import-button.primary {
    background-color: #9b59b6;
}

.import-button.secondary {
    background-color: #3498db;
}

.import-button.secondary:hover {
    background-color: #2980b9;
}

/* حاوية السجل المحدثة */
#import-lists-panel .log-container {
    background-color: #f8f9fa;
    border: 2px solid #dee2e6;
    border-radius: 5px;
    padding: 15px;
    height: 350px;
    overflow-y: auto;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 12px;
    line-height: 1.5;
    margin-bottom: 20px;
}

#import-lists-panel .log-entry {
    margin-bottom: 5px;
    padding: 2px 0;
    white-space: pre-wrap;
}

.log-info {
    color: #17a2b8;
}

.log-success {
    color: #28a745;
    font-weight: bold;
}

.log-error {
    color: #dc3545;
    font-weight: bold;
}

.log-warning {
    color: #ffc107;
    font-weight: bold;
}

.log-progress {
    color: #6f42c1;
}

/* شريط التقدم المحدث */
#import-lists-panel .progress-container {
    background-color: #E3F2FD;
    border: 2px solid #0D47A1;
    border-radius: 5px;
    margin: 20px 0;
    overflow: hidden;
}

#import-lists-panel .progress-text {
    background-color: #0D47A1;
    color: white;
    padding: 10px;
    font-weight: bold;
    text-align: center;
}

#import-lists-panel .progress-bar {
    background-color: #4CAF50;
    color: white;
    text-align: center;
    padding: 5px;
    font-weight: bold;
    transition: width 0.3s ease;
    min-height: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* نافذة التأكيد */
.modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: white;
    border-radius: 10px;
    padding: 30px;
    max-width: 500px;
    width: 90%;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-content h3 {
    color: #e74c3c;
    margin-bottom: 15px;
    font-size: 18px;
}

.modal-content p {
    margin-bottom: 20px;
    line-height: 1.5;
    color: #555;
}

.modal-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.modal-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s;
}

.modal-btn.confirm {
    background-color: #e74c3c;
    color: white;
}

.modal-btn.confirm:hover {
    background-color: #c0392b;
}

.modal-btn.cancel {
    background-color: #95a5a6;
    color: white;
}

.modal-btn.cancel:hover {
    background-color: #7f8c8d;
}

/* معلومات الملفات */
.files-info {
    background-color: #e8f4fd;
    border: 1px solid #bee5eb;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 15px;
    font-size: 12px;
    line-height: 1.4;
}

/* إخفاء حقول الملفات */
.file-input {
    display: none !important;
}

/* تحسينات التصميم المتجاوب */
@media (max-width: 768px) {
    .font-controls {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .import-buttons-section {
        gap: 10px;
    }
    
    .import-button {
        padding: 12px 15px;
        font-size: 13px;
    }
    
    #import-lists-panel .log-container {
        height: 250px;
        font-size: 11px;
    }
    
    .modal-content {
        margin: 20px;
        padding: 20px;
    }
    
    .modal-buttons {
        flex-direction: column;
        gap: 10px;
    }
}
