# تعليمات تشغيل نظام استيراد اللوائح من منظومة مسار

## 🚀 خطوات التشغيل

### 1. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 2. تشغيل الخادم
```bash
python masar_server.py
```

### 3. فتح الواجهة
افتح المتصفح واذهب إلى:
```
http://localhost:5000
```

## 📁 الملفات المطلوبة

- `masar_server.py` - خادم Python لمعالجة البيانات
- `import_masar_v2.html` - واجهة المستخدم
- `data.db` - قاعدة البيانات (سيتم إنشاؤها تلقائياً إذا لم تكن موجودة)
- `requirements.txt` - قائمة المكتبات المطلوبة

## 🔧 المميزات

### ✅ يتعامل مع قاعدة البيانات الحقيقية
- حفظ البيانات في ملف `data.db` الفعلي
- تحديث جميع الجداول كما في النسخة الأصلية:
  - `اللوائح`
  - `السجل_العام`
  - `البنية_التربوية`
  - `بيانات_المؤسسة`

### ✅ نفس منطق العمل
- حذف السجلات الافتراضية
- معالجة ملفات Excel متعددة الشيتات
- استخراج السنة الدراسية تلقائياً
- تحديث مجموع التلاميذ
- ترتيب المستويات التعليمية
- تعيين الأقسام لحراسة رقم 1

### ✅ واجهة متطورة
- شريط تقدم حقيقي
- رسائل السجل الملونة
- التحقق من اتصال الخادم
- ملخص النتائج التفصيلي
- رسائل تأكيد للملفات غير المعروفة

## ⚠️ ملاحظات مهمة

1. **تأكد من إغلاق أي برامج تستخدم قاعدة البيانات** قبل بدء الاستيراد
2. **احتفظ بنسخة احتياطية** من `data.db` قبل الاستيراد
3. **يُفضل أن يحتوي اسم ملف Excel على "ListEleve"** للتأكد من أنه ملف لوائح صحيح
4. **لا تغلق نافذة الطرفية** أثناء تشغيل الخادم

## 🐛 حل المشاكل الشائعة

### المشكلة: "خطأ في الاتصال بالخادم"
**الحل:** تأكد من تشغيل `python masar_server.py` أولاً

### المشكلة: "الزر معطل"
**الحل:** انتظر حتى يتم الاتصال بالخادم (المؤشر الأخضر)

### المشكلة: "خطأ في قاعدة البيانات"
**الحل:** تأكد من عدم استخدام ملف `data.db` من برامج أخرى

## 📊 مقارنة مع النسخة الأصلية

| الميزة | Python الأصلي | HTML الجديد |
|--------|---------------|-------------|
| قاعدة البيانات | ✅ data.db | ✅ data.db |
| معالجة Excel | ✅ pandas | ✅ pandas |
| نفس الجداول | ✅ | ✅ |
| نفس المنطق | ✅ | ✅ |
| واجهة ويب | ❌ | ✅ |
| شريط التقدم | ✅ | ✅ |
| رسائل السجل | ✅ | ✅ |
| سهولة الاستخدام | متوسط | عالية |

## 🎯 النتيجة

هذا النظام يوفر **نفس الوظائف تماماً** كما في ملف Python الأصلي، لكن بواجهة ويب حديثة وسهلة الاستخدام. جميع البيانات تُحفظ في نفس قاعدة البيانات `data.db` وبنفس البنية والمنطق.
